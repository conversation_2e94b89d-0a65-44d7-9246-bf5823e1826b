package com.stt.android.offlinemaps.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.watch.WatchHelper
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

@Composable
fun DeviceConnectionStatusImage(
    deviceType: SuuntoDeviceType,
    deviceSku: String,
    modifier: Modifier = Modifier
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
    ) {
        // Background for device display
        if (deviceType.isEonComputer) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(fraction = 0.51f)
                    .fillMaxHeight(fraction = 0.41f)
                    .background(Color.Black, shape = RectangleShape)
            )
        } else {
            Box(
                modifier = Modifier
                    .fillMaxSize(fraction = 0.43f)
                    .background(Color.Black, shape = CircleShape)
            )
        }

        // Device image
        Image(
            painter = painterResource(
                WatchHelper.getDrawableResIdForSuuntoDeviceType(deviceType, deviceSku)
            ),
            contentDescription = null,
        )

        // TODO: Connection status indicator
    }
}

@Preview(name = "Vertical", widthDp = 130, heightDp = 130, showBackground = true)
@Composable
private fun DeviceConnectionStatusImagePreviewVertical() {
    AppTheme {
        DeviceConnectionStatusImage(
            deviceType = SuuntoDeviceType.SuuntoVertical,
            deviceSku = ""
        )
    }
}

@Preview(name = "Eon", widthDp = 130, heightDp = 130, showBackground = true)
@Composable
private fun DeviceConnectionStatusImagePreviewEon() {
    AppTheme {
        DeviceConnectionStatusImage(
            deviceType = SuuntoDeviceType.EonSteelBlack,
            deviceSku = ""
        )
    }
}
