package com.stt.android.offlinemaps.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.stt.android.R
import com.stt.android.core.R as CoreR
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.offlinemaps.datasource.DummyOfflineRegionDataSource
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList

@Composable
fun SearchResults(
    searching: Boolean,
    cancellingDownload: Boolean,
    requestingDownload: Boolean,
    searchTerm: String,
    searchResults: ImmutableList<OfflineRegionResult>,
    onOfflineRegionSelected: (OfflineRegionResult.OfflineRegion) -> Unit,
    onOfflineRegionGroupSelected: (OfflineRegionResult.OfflineRegionGroup) -> Unit,
    onCancel: (OfflineRegionResult.OfflineRegion) -> Unit,
    onRetry: (OfflineRegionResult.OfflineRegion) -> Unit,
    onViewInLibrary: (OfflineRegionResult.OfflineRegion?) -> Unit,
    modifier: Modifier = Modifier
) {
    if (searchTerm.isBlank()) {
        // Info state
        Column(
            modifier = modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(
                modifier = Modifier
                    .weight(1f, fill = false)
                    .height(MaterialTheme.spacing.xxxxlarge)
            )
            Icon(
                painter = painterResource(id = CoreR.drawable.ic_search_fill),
                contentDescription = null,
                tint = MaterialTheme.colors.darkGrey,
                modifier = Modifier
                    .align(CenterHorizontally)
                    .padding(bottom = MaterialTheme.spacing.medium)
                    .size(MaterialTheme.iconSizes.large)
            )
            Text(
                text = stringResource(id = R.string.search_info_text),
                textAlign = TextAlign.Center,
                color = MaterialTheme.colors.darkGrey,
                modifier = Modifier
                    .align(CenterHorizontally)
                    .padding(horizontal = MaterialTheme.spacing.xxlarge)
            )
        }
    } else if (!searching && searchResults.isEmpty()) {
        // No results state
        Column(
            modifier = modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(
                modifier = Modifier
                    .weight(1f, fill = false)
                    .height(MaterialTheme.spacing.xxxxlarge)
            )
            Icon(
                painter = painterResource(id = CoreR.drawable.ic_search_fill),
                contentDescription = null,
                tint = MaterialTheme.colors.darkGrey,
                modifier = Modifier
                    .align(CenterHorizontally)
                    .padding(bottom = MaterialTheme.spacing.medium)
                    .size(MaterialTheme.iconSizes.large)
            )
            Text(
                text = stringResource(id = R.string.search_no_results_text),
                textAlign = TextAlign.Center,
                color = MaterialTheme.colors.darkGrey,
                modifier = Modifier
                    .align(CenterHorizontally)
                    .padding(horizontal = MaterialTheme.spacing.xxlarge)
            )
        }
    } else {
        // Show results state
        OfflineRegionList(
            results = searchResults,
            cancellingDownload = cancellingDownload,
            requestingDownload = requestingDownload,
            onRegionSelected = onOfflineRegionSelected,
            onGroupSelected = onOfflineRegionGroupSelected,
            onCancel = onCancel,
            onRetry = onRetry,
            onViewInLibrary = onViewInLibrary,
            modifier = modifier.fillMaxSize(),
            highlight = searchTerm
        )
    }
}

@Preview(showBackground = true, widthDp = 360, heightDp = 720)
@Preview(showBackground = true, widthDp = 720, heightDp = 360)
@Composable
private fun SearchResultsPreview(
    @PreviewParameter(SearchResultsParamsProvider::class) params: SearchResultsParams
) {
    AppTheme {
        SearchResults(
            searching = false,
            cancellingDownload = false,
            requestingDownload = false,
            searchTerm = params.searchTerm,
            searchResults = params.searchResults,
            onOfflineRegionSelected = {},
            onOfflineRegionGroupSelected = {},
            onCancel = {},
            onRetry = {},
            onViewInLibrary = {},
        )
    }
}

private class SearchResultsParamsProvider : PreviewParameterProvider<SearchResultsParams> {
    override val values: Sequence<SearchResultsParams> = sequenceOf(
        SearchResultsParams(searchTerm = "", searchResults = persistentListOf()),
        SearchResultsParams(searchTerm = "keyword", searchResults = persistentListOf()),
        SearchResultsParams(
            searchTerm = "keyword",
            searchResults = (
                DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS + DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS
                    .first { it.name == "Finland" }.regions
                ).toPersistentList()
        ),
    )
}

private data class SearchResultsParams(
    val searchTerm: String,
    val searchResults: ImmutableList<OfflineRegionResult>
)
