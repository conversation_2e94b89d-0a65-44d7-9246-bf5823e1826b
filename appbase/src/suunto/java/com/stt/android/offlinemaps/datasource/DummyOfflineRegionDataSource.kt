package com.stt.android.offlinemaps.datasource

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionMapStyle
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.entity.OfflineRegionSearchResult
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.delay
import javax.inject.Inject
import javax.inject.Singleton
import com.stt.android.core.R as CR

@Singleton
class DummyOfflineRegionDataSource @Inject constructor() : OfflineRegionDataSource {

    override suspend fun getCatalogue(
        deviceSerial: String,
        latLng: LatLng?,
        capabilities: String?,
        includeNearbyGroups: Boolean
    ): OfflineRegionListData.Catalogue {
        delay(1000)
        return OfflineRegionListData.Catalogue(
            nearby = persistentListOf(),
            groups = DUMMY_OFFLINE_REGION_GROUPS
        )
    }

    override suspend fun putDownloadOrder(
        deviceSerial: String,
        regionId: String,
        capabilities: String?,
        groupName: String?
    ): OfflineRegionResult.OfflineRegion {
        delay(1000)
        return DUMMY_OFFLINE_REGION_GROUPS.map { it.regions }.flatten().first { it.id == regionId }
            .copy(
                downloadOrder = DownloadOrder(
                    deviceSerialNumber = deviceSerial,
                    downloadCompletedAt = null,
                    status = OfflineRegionStatus.REQUESTED,
                    downloadedSize = 0,
                    sourceTypeUsed = "TILE_LIST"
                )
            )
    }

    override suspend fun deleteRegion(
        deviceSerial: String,
        region: OfflineRegionResult.OfflineRegion,
        capabilities: String?,
    ): OfflineRegionResult.OfflineRegion {
        delay(1000)
        return DUMMY_OFFLINE_REGION_GROUPS.flatMap { it.regions }
            .first { it.id == region.id }.run {
                if (region.downloading) {
                    copy(downloadOrder = null)
                } else {
                    copy(downloadOrder = downloadOrder?.copy(status = OfflineRegionStatus.DELETE_REQUESTED))
                }
            }
    }

    override suspend fun search(searchTerm: String): ImmutableList<OfflineRegionSearchResult> {
        delay(1000)
        return if (searchTerm.isBlank()) {
            persistentListOf()
        } else {
            DUMMY_OFFLINE_REGION_GROUPS.map { offlineRegionGroup ->
                offlineRegionGroup.regions
                    .filter {
                        it.name.contains(searchTerm, ignoreCase = true)
                    }
                    .map {
                        OfflineRegionSearchResult(
                            id = it.id,
                            name = it.name,
                            highlightResult = searchTerm,
                            type = RemoteResultType.REGION
                        )
                    }
            }.flatten().toPersistentList()
        }
    }

    override suspend fun getLibrary(
        deviceSerial: String,
        capabilities: String?,
    ): ImmutableList<OfflineRegionResult.OfflineRegion> {
        delay(1000)
        return DUMMY_OFFLINE_REGION_GROUPS[2].regions.toPersistentList()
    }

    override suspend fun getCatalogueRegion(
        deviceSerial: String,
        regionId: String,
        capabilities: String?,
        groupName: String?
    ): OfflineRegionResult.OfflineRegion {
        delay(1000)
        return DUMMY_OFFLINE_REGION_GROUPS.flatMap { it.regions }.first { it.id == regionId }
    }

    override suspend fun getLibraryRegion(
        deviceSerial: String,
        regionId: String,
        capabilities: String?,
    ): OfflineRegionResult.OfflineRegion {
        delay(1000)
        return DUMMY_OFFLINE_REGION_GROUPS.flatMap { it.regions }.first { it.id == regionId }
    }

    override suspend fun getMapStyles(): List<OfflineRegionMapStyle> =
        DUMMY_OFFLINE_REGION_MAP_STYLES

    override suspend fun resetLibrary(
        deviceSerial: String,
        capabilities: String?,
    ): ImmutableList<OfflineRegionResult.OfflineRegion> {
        return DUMMY_OFFLINE_REGION_GROUPS[2].regions.toPersistentList()
    }

    companion object {
        private val FINLAND_BOUNDS = LatLngBounds(
            LatLng(59.2508, 15.4372),
            LatLng(70.1446, 33.3072)
        )

        val DUMMY_OFFLINE_REGION_MAP_STYLES = persistentListOf(
            OfflineRegionMapStyle(
                id = "outdoorstyle",
                imageUrl = "http://suuntomapsassetsdev.blob.core.windows.net/styles/images/style-orca-outdoor.png",
                name = "Outdoor",
                description = "Tailored for hiking and outdoor activities"
            )
        )

        val DUMMY_OFFLINE_REGION_GROUPS = persistentListOf(
            OfflineRegionResult.OfflineRegionGroup(
                id = "1",
                name = "Austria",
                size = 18732000,
                regions = persistentListOf()
            ),
            OfflineRegionResult.OfflineRegionGroup(
                id = "2",
                name = "France",
                size = 11193000,
                regions = persistentListOf()
            ),
            OfflineRegionResult.OfflineRegionGroup(
                id = "3",
                name = "Finland",
                size = 19375000,
                regions = persistentListOf(
                    OfflineRegionResult.OfflineRegion(
                        id = "1",
                        name = "Whole Finland",
                        size = 19375000,
                        transferSize = 19375000,
                        area = 24583.412,
                        areaUnitRes = CR.string.square_kilometers,
                        bounds = FINLAND_BOUNDS,
                        boundaryUrl = "",
                        maskUrl = "",
                        description = "",
                        downloadOrder = DownloadOrder(
                            deviceSerialNumber = "022401200117",
                            downloadCompletedAt = 1672208623000,
                            status = OfflineRegionStatus.FINISHED,
                            downloadedSize = 19375000,
                            sourceTypeUsed = "TILE_LIST"
                        ),
                        styleIds = persistentListOf("outdoorstyle")
                    ),
                    OfflineRegionResult.OfflineRegion(
                        id = "2",
                        name = "Kainuu",
                        size = 1936000,
                        transferSize = 1936000,
                        area = 24583.412,
                        areaUnitRes = CR.string.square_kilometers,
                        bounds = FINLAND_BOUNDS,
                        boundaryUrl = "",
                        maskUrl = "",
                        description = "",
                        downloadOrder = DownloadOrder(
                            deviceSerialNumber = "022401200117",
                            downloadCompletedAt = null,
                            status = OfflineRegionStatus.REQUESTED,
                            downloadedSize = 0,
                            sourceTypeUsed = "TILE_LIST"
                        ),
                        styleIds = persistentListOf("outdoorstyle")
                    ),
                    OfflineRegionResult.OfflineRegion(
                        id = "3",
                        name = "Lappi",
                        size = 8368000,
                        transferSize = 8368000,
                        area = 24583.412,
                        areaUnitRes = CR.string.square_kilometers,
                        bounds = FINLAND_BOUNDS,
                        boundaryUrl = "",
                        maskUrl = "",
                        description = "",
                        downloadOrder = DownloadOrder(
                            deviceSerialNumber = "022401200117",
                            downloadCompletedAt = null,
                            status = OfflineRegionStatus.FAILED,
                            downloadedSize = 0,
                            sourceTypeUsed = "TILE_LIST"
                        ),
                        styleIds = persistentListOf("outdoorstyle")
                    ),
                    OfflineRegionResult.OfflineRegion(
                        id = "4",
                        name = "Uusimaa",
                        size = 7238000,
                        transferSize = 7238000,
                        area = 24583.412,
                        areaUnitRes = CR.string.square_kilometers,
                        bounds = FINLAND_BOUNDS,
                        boundaryUrl = "",
                        maskUrl = "",
                        description = "",
                        downloadOrder = null,
                        styleIds = persistentListOf("outdoorstyle")
                    ),
                    OfflineRegionResult.OfflineRegion(
                        id = "5",
                        name = "Pirkanmaa",
                        size = 4123000,
                        transferSize = 4123000,
                        area = 24583.412,
                        areaUnitRes = CR.string.square_kilometers,
                        bounds = FINLAND_BOUNDS,
                        boundaryUrl = "",
                        maskUrl = "",
                        description = "",
                        downloadOrder = DownloadOrder(
                            deviceSerialNumber = "022401200117",
                            downloadCompletedAt = 1,
                            status = OfflineRegionStatus.FINISHED,
                            downloadedSize = 4123000,
                            sourceTypeUsed = "TILE_LIST"
                        ),
                        styleIds = persistentListOf()
                    ),
                    OfflineRegionResult.OfflineRegion(
                        id = "6",
                        name = "Pohjanmaa",
                        size = 5873000,
                        transferSize = 5873000,
                        area = 24583.412,
                        areaUnitRes = CR.string.square_kilometers,
                        bounds = FINLAND_BOUNDS,
                        boundaryUrl = "",
                        maskUrl = "",
                        description = "",
                        downloadOrder = DownloadOrder(
                            deviceSerialNumber = "022401200117",
                            downloadCompletedAt = 1,
                            status = OfflineRegionStatus.IN_PROGRESS,
                            downloadedSize = 3600,
                            sourceTypeUsed = "TILE_LIST"
                        ),
                        styleIds = persistentListOf("outdoorstyle")
                    ),
                    OfflineRegionResult.OfflineRegion(
                        id = "7",
                        name = "Janka",
                        size = 5873000,
                        transferSize = 5873000,
                        area = 24583.412,
                        areaUnitRes = CR.string.square_kilometers,
                        bounds = FINLAND_BOUNDS,
                        boundaryUrl = "",
                        maskUrl = "",
                        description = "",
                        downloadOrder = DownloadOrder(
                            deviceSerialNumber = "022401200117",
                            downloadCompletedAt = 1,
                            status = OfflineRegionStatus.DELETE_REQUESTED,
                            downloadedSize = 3600,
                            sourceTypeUsed = "TILE_LIST"
                        ),
                        styleIds = persistentListOf("outdoorstyle")
                    )
                )
            ),
            OfflineRegionResult.OfflineRegionGroup(
                id = "4",
                name = "Germany",
                size = 10364000,
                regions = persistentListOf()
            ),
            OfflineRegionResult.OfflineRegionGroup(
                id = "5",
                name = "Italy",
                size = 15873000,
                regions = persistentListOf()
            ),
            OfflineRegionResult.OfflineRegionGroup(
                id = "6",
                name = "Spain",
                size = 12360000,
                regions = persistentListOf()
            ),
            OfflineRegionResult.OfflineRegionGroup(
                id = "7",
                name = "Sweden",
                size = 18223000,
                regions = persistentListOf()
            ),
        )
    }
}
