package com.stt.android.watch.scan

import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.common.ui.BaseBindableItem
import com.stt.android.databinding.DeviceItemBinding
import com.stt.android.watch.WatchHelper
import com.stt.android.watch.WatchItemClickListener
import com.suunto.connectivity.btscanner.ScannedSuuntoBtDevice
import com.suunto.connectivity.repository.PairingState
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.suunto.connectivity.util.getIdentifier

data class DeviceItem(
    val device: ScannedSuuntoBtDevice,
    val listener: WatchItemClickListener
) : BaseBindableItem<DeviceItemBinding>() {
    override fun getLayout() = R.layout.device_item

    @StringRes
    fun deviceNameRes(): Int {
        return WatchHelper.getStringResIdForSuuntoDeviceType(device.deviceType)
    }

    @StringRes
    fun actionButtonTextRes(): Int {
        return when {
            // Show "How to pair?" text for data layer devices that are not paired via Wear OS app yet
            device.suuntoBtDevice.deviceType.isDataLayerDevice && !device.isWearOsNode ->
                R.string.device_ui_how_to_pair_button
            // Show "Connect" for devices that are bonded in the BT adapter or have Wear OS app pairing
            device.phonePairingState == PairingState.Paired || device.isWearOsNode ->
                R.string.connect
            // Show "Pair" for devices that are sending BLE advertisements, but are not bonded
            else ->
                R.string.pair
        }
    }

    fun deviceSerial(): String {
        return when {
            SuuntoDeviceType.advertisementHasSerial(device.deviceType) -> device.serial
            else -> getIdentifier(
                device.name,
                SuuntoDeviceType.getBleAdvertisementPrefix(device.name, device.deviceType)
            )
        }
    }

    val canConnectToDevice: Boolean
        get() = device.deviceType.isDataLayerDevice || device.isFoundByScanner

    fun onPairClick() {
        listener.onWatchItemClick(device)
    }
}
