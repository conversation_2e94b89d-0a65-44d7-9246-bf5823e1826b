package com.stt.android.watch.permission

import androidx.lifecycle.LiveData
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.watch.DeviceAnalyticsUtil
import com.stt.android.watch.DeviceViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import javax.inject.Inject

@HiltViewModel
class DevicePermissionViewModel
@Inject constructor(
    private val analyticsUtil: DeviceAnalyticsUtil,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : DeviceViewModel(ioThread, mainThread) {

    private val _permissionEvent = SingleLiveEvent<PermissionEvent>()
    val permissionEvent: LiveData<PermissionEvent>
        get() = _permissionEvent

    fun onEnableBluetoothClick() {
        _permissionEvent.value = PermissionEvent.ENABLE_BLUETOOTH
        analyticsUtil.onPermissionButtonClick(AnalyticsPropertyValue.PermissionButton.TURN_ON_BLUETOOTH)
    }

    fun onEnableLocationClick() {
        _permissionEvent.value = PermissionEvent.ENABLE_LOCATION
        analyticsUtil.onPermissionButtonClick(AnalyticsPropertyValue.PermissionButton.TURN_ON_LOCATION)
    }

    fun onAllowLocationClick() {
        _permissionEvent.value = PermissionEvent.ALLOW_LOCATION
        analyticsUtil.onPermissionButtonClick(AnalyticsPropertyValue.PermissionButton.ALLOW_LOCATION)
    }

    fun onAllowNearbyDevices() {
        _permissionEvent.value = PermissionEvent.ALLOW_NEARBY_DEVICES
        analyticsUtil.onPermissionButtonClick(AnalyticsPropertyValue.PermissionButton.ALLOW_NEARBY_DEVICES)
    }

    fun onRequestLocationPermissionsResult(result: String) {
        analyticsUtil.onRequestLocationPermissionsResult(AnalyticsPropertyValue.PermissionContext.PAIRING_WATCH, result)
    }

    fun onRequestNearbyDevicesPermissionsResult(result: String) {
        analyticsUtil.onRequestNearbyDevicesPermissionsResult(AnalyticsPropertyValue.PermissionContext.PAIRING_WATCH, result)
    }

    fun onLocationPermissionScreenShowed() {
        analyticsUtil.onLocationPermissionScreenShowed(AnalyticsPropertyValue.PermissionContext.PAIRING_WATCH)
    }

    fun onNearbyDevicesPermissionScreenShowed() {
        analyticsUtil.onNearbyDevicesPermissionScreenShowed(AnalyticsPropertyValue.PermissionContext.PAIRING_WATCH)
    }
}

enum class PermissionEvent {
    ENABLE_BLUETOOTH,
    ENABLE_LOCATION,
    ALLOW_LOCATION,
    ALLOW_NEARBY_DEVICES
}
