package com.stt.android.watch.sportmodes.list

import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.widget.Button
import androidx.navigation.findNavController
import com.stt.android.R
import com.stt.android.common.ui.BaseBindableItem
import com.stt.android.databinding.ItemSportmodesWatchHeaderBinding

data class SportModeHeaderItem(
    var loadFinished: <PERSON>olean,
    var listFull: <PERSON>ole<PERSON>,
    var listEmpty: <PERSON>ole<PERSON>,
    val toolbarDelegate: ToolbarDelegate,
    val deleteInProgressDelegate: DeleteInProgressDelegate
) : BaseBindableItem<ItemSportmodesWatchHeaderBinding>() {
    private lateinit var createSportModeButton: Button
    private lateinit var listFullText: View
    private lateinit var editButton: View
    private lateinit var shortListText: View

    private var watchBusy = false
    private var watchSynchronizing = false

    fun navigateToCreate(view: View) {
        toolbarDelegate.checkActionModeActive { active ->
            if (active) {
                toolbarDelegate.finishActionMode()
            }
        }
        view.findNavController().navigate(SportModeListFragmentDirections.sportModeCreateAction())
    }

    override fun bind(viewBinding: ItemSportmodesWatchHeaderBinding, position: Int) {
        super.bind(viewBinding, position)
        createSportModeButton = viewBinding.newCustomModeButton
        listFullText = viewBinding.sportmodesMaxAmount
        editButton = viewBinding.editButton
        shortListText = viewBinding.shortListText
        updateHeaderState(loadFinished, listFull, listEmpty)
    }

    fun updateHeaderState(loadFinished: Boolean, listFull: Boolean, listEmpty: Boolean) {
        this.loadFinished = loadFinished
        this.listFull = listFull
        this.listEmpty = listEmpty
        if (!isBound()) {
            return
        }
        createSportModeButton.isEnabled = loadFinished && !listFull &&
            !deleteInProgressDelegate.isDeletionInProcess() && !watchBusy && !watchSynchronizing
        shortListText.visibility = if (loadFinished && !listEmpty) VISIBLE else GONE
        editButton.isEnabled = !watchBusy && !watchSynchronizing
        updateEditButtonVisibility()
        listFullText.visibility = if (listFull) VISIBLE else GONE
    }

    fun updateWatchState(watchBusy: Boolean, watchSynchronizing: Boolean) {
        this.watchBusy = watchBusy
        this.watchSynchronizing = watchSynchronizing
        updateHeaderState(loadFinished, listFull, listEmpty)
    }

    override fun getLayout() = R.layout.item_sportmodes_watch_header

    @Suppress("UNUSED_PARAMETER")
    fun startModesSelection(view: View) {
        toolbarDelegate.startActionMode(R.string.sport_mode_selection_text)
        toolbarDelegate.updateSelectedCount()
    }

    fun updateEditButtonVisibility() {
        toolbarDelegate.checkActionModeActive { active ->
            editButton.visibility = if (active || !loadFinished || listEmpty || deleteInProgressDelegate.isDeletionInProcess()) {
                GONE
            } else {
                VISIBLE
            }
        }
    }

    private fun isBound(): Boolean {
        return this::createSportModeButton.isInitialized &&
            this::listFullText.isInitialized &&
            this::editButton.isInitialized &&
            this::shortListText.isInitialized
    }
}
