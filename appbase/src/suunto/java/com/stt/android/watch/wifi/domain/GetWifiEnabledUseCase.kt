package com.stt.android.watch.wifi.domain

import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.watch.wifi.datasource.WifiNetworksDataSource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetWifiEnabledUseCase @Inject constructor(
    private val dataSource: WifiNetworksDataSource,
    private val dispatcherProvider: CoroutinesDispatcherProvider
) {
    suspend fun run(): Flow<Boolean> = withContext(dispatcherProvider.io) {
        dataSource.observeWifiEnabled()
    }
}
