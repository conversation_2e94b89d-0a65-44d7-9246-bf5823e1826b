package com.stt.android.watch.sportmodes.list

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import com.stt.android.R
import com.stt.android.common.ui.observeK
import com.stt.android.di.AppVersionNumberForSupport
import com.stt.android.domain.device.SyncState
import com.stt.android.help.BaseSupportHelper
import com.stt.android.utils.STTConstants
import com.stt.android.watch.sportmodes.SportModeFragment
import com.stt.android.watch.sportmodes.dialogs.SportModeAlertDialog
import com.stt.android.watch.sportmodes.dialogs.SportModeDialogCallback
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class SportModeListFragment : SportModeFragment<SportModesListViewModel>(), SportModeDialogCallback {

    @Inject
    @AppVersionNumberForSupport
    internal lateinit var appVersionNumberForSupport: String

    @Inject
    internal lateinit var supportHelper: BaseSupportHelper

    override fun onOkClicked(tag: String?) {
        if (tag == SportModeAlertDialog.TAG_DELETE) {
            viewModel.deleteSelected()
        }
    }

    override fun onDialogCanceled() {
    }

    override val viewModel: SportModesListViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    @Deprecated("Deprecated in Java")
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.sport_modes_helpshift, menu)
        super.onCreateOptionsMenu(menu, inflater)
    }

    @Deprecated("Deprecated in Java")
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.sport_mode_help -> {
                showHelpshiftArticle()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showHelpshiftArticle() {
        activity?.let {
            supportHelper.showFAQ(
                requireActivity(),
                STTConstants.HelpShiftPublishId.SUUNTO_SPORT_MODE_HELP,
                appVersionNumberForSupport
            )
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        viewModel.sharedViewModel.setTitle(getString(R.string.sport_modes_title))
        viewModel.showDeleteAllAlertEvent.observeK(viewLifecycleOwner) {
            val dialog = SportModeAlertDialog.newInstance(
                R.string.sport_mode_deleting_all_title,
                R.string.sport_mode_deleting_all_text,
                R.string.ok,
                SportModeAlertDialog.NO_VALUE
            )
            dialog.show(childFragmentManager, SportModeAlertDialog.TAG_DELETE_ALL)
        }

        viewModel.sharedViewModel.connectedWatchState.observeK(viewLifecycleOwner) { watchState ->
            watchState?.let {
                viewModel.updateWatchState(
                    it.isBusy,
                    it.connectedWatchSyncState.state != SyncState.NOT_SYNCING
                )
            }
        }

        return super.onCreateView(inflater, container, savedInstanceState)
    }
}
