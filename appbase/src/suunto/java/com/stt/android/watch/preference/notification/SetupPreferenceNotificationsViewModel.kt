package com.stt.android.watch.preference.notification

import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.ui.activities.settings.helper.PredefinedRepliesHelper
import com.stt.android.ui.activities.settings.watch.notifications.WatchNotificationsPermissionsContainer
import com.stt.android.ui.activities.settings.watch.notifications.WatchNotificationsPermissionsViewModel
import com.stt.android.ui.activities.settings.watch.notifications.domain.FetchNotificationsEnabledUseCase
import com.stt.android.ui.activities.settings.watch.notifications.domain.SetNotificationsCategoryEnabledUseCase
import com.stt.android.ui.activities.settings.watch.notifications.domain.SetNotificationsEnabledUseCase
import com.stt.android.watch.SuuntoWatchModel
import com.stt.android.watch.preference.SetupPreference
import com.stt.android.watch.preference.SetupPreferenceReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SetupPreferenceNotificationsViewModel
@Inject constructor(
    predefinedRepliesHelper: PredefinedRepliesHelper,
    setNotificationsEnabledUseCase: SetNotificationsEnabledUseCase,
    setNotificationsCategoryEnabledUseCase: SetNotificationsCategoryEnabledUseCase,
    fetchNotificationsEnabledUseCase: FetchNotificationsEnabledUseCase,
    suuntoWatchModel: SuuntoWatchModel,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers
) : WatchNotificationsPermissionsViewModel(
    predefinedRepliesHelper,
    setNotificationsEnabledUseCase,
    setNotificationsCategoryEnabledUseCase,
    fetchNotificationsEnabledUseCase,
    suuntoWatchModel,
    ioThread,
    mainThread,
    coroutinesDispatchers
) {
    private val _setupPreferenceReducerFlow = MutableSharedFlow<SetupPreferenceReducer>()
    val setupPreferenceReducerFlow = _setupPreferenceReducerFlow.asSharedFlow()

    fun notifyBySetupPreference(setupPreference: SetupPreference) {
        val data = WatchNotificationsPermissionsContainer(
            companionNotAssociated = buildCompanionNotAssociatedItem(false),
            notificationSwitch = buildNotificationSwitchItem(setupPreference.notifications.enable),
            permissionItems = listOf(),
            messageTypes = getMessageTypes(setupPreference.notifications.toNotificationState()),
            supportsPredefinedReplies = true,
            predefinedReplies = setupPreference.notifications.predefinedReplies,
            onPredefinedReplySelected = ::onPredefinedReplySelected,
            bySetupPreference = true
        )
        notifyDataLoaded(data)
    }

    private fun sendReducer(reducer: SetupPreferenceReducer) {
        viewModelScope.launch {
            _setupPreferenceReducerFlow.emit(reducer)
        }
    }

    override fun loadData() {
        // do nothing
    }

    override fun trySetNotificationsEnabled() {
        // do nothing
    }

    override fun trySetCallNotificationsEnabled() {
        // do nothing
    }

    override fun setPredefinedReply(index: Int, reply: String) {
        val reducer = object : SetupPreferenceReducer {
            override val reduce: suspend (SetupPreference) -> SetupPreference = { old ->
                old.copy(
                    notifications = old.notifications.copy(
                        predefinedReplies = old.notifications.predefinedReplies.mapIndexed { i, s ->
                            if (index == i) {
                                reply
                            } else {
                                s
                            }
                        }
                    )
                )
            }
        }
        sendReducer(reducer)
    }

    override fun onNotificationsSwitchEnabled(enabled: Boolean) {
        val reducer = object : SetupPreferenceReducer {
            override val reduce: suspend (SetupPreference) -> SetupPreference = { old ->
                old.copy(
                    notifications = old.notifications.copy(
                        enable = enabled,
                        appEnable = enabled,
                        msgEnable = enabled,
                        callEnable = enabled,
                    )
                )
            }
        }
        sendReducer(reducer)
    }

    override suspend fun tryDisabledNotificationAccess(
        callEnabled: Boolean,
        smsEnabled: Boolean,
        applicationEnabled: Boolean
    ) {
        val reducer: SetupPreferenceReducer = object : SetupPreferenceReducer {
            override val reduce: suspend (SetupPreference) -> SetupPreference = { old ->
                val enable = callEnabled || smsEnabled || applicationEnabled
                old.copy(
                    notifications = old.notifications.copy(
                        enable = enable,
                        callEnable = callEnabled,
                        msgEnable = smsEnabled,
                        appEnable = applicationEnabled,
                    )
                )
            }
        }
        sendReducer(reducer)
    }
}
