package com.stt.android.watch.preference.notification

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.android.InstalledAppsNameAndIconUseCase
import com.stt.android.domain.android.InstalledAppsUseCase
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.ui.activities.settings.watch.notifications.BaseWatchAppNotificationsPermissionsViewModel
import com.stt.android.ui.activities.settings.watch.notifications.InstalledAppItem
import com.stt.android.ui.activities.settings.watch.notifications.WatchAppNotificationsPermissionsContainer
import com.stt.android.watch.SuuntoWatchModel
import com.stt.android.watch.preference.PreferenceAppNotification
import com.stt.android.watch.preference.SetupPreference
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.text.Collator
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class SetupPreferenceAppNotificationsViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    installedAppsUseCase: InstalledAppsUseCase,
    installedAppsNameAndIconUseCase: InstalledAppsNameAndIconUseCase,
    suuntoWatchModel: SuuntoWatchModel,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    private val coroutinesDispatchers: CoroutinesDispatchers
) : BaseWatchAppNotificationsPermissionsViewModel(
    installedAppsUseCase,
    installedAppsNameAndIconUseCase,
    suuntoWatchModel,
    ioThread,
    mainThread,
    coroutinesDispatchers
) {

    var setupPreference: SetupPreference?
        get() = savedStateHandle[KEY_SETUP_PREFERENCE]
        set(value) {
            val shouldLoadData =
                savedStateHandle.get<SetupPreference?>(KEY_SETUP_PREFERENCE) == null
            savedStateHandle[KEY_SETUP_PREFERENCE] = value
            if (!shouldLoadData) {
                loadData()
            }
        }

    override fun loadData() {
        notificationsEnabled = true

        setupPreference?.let {
            interceptedBySetupPreference(it)
        }
    }

    private fun interceptedBySetupPreference(oldPreference: SetupPreference) {
        Timber.d("loadData...")
        viewModelScope.launch {
            val installedApps = withContext(coroutinesDispatchers.io) {
                getInstalledApps(notificationsEnabled)
            }
            if (oldPreference.notifications.appNotifications.none() && installedApps.any()) {
                Timber.d("initial app notifications from view model: ${installedApps.joinToString { it.appName + "," + it.notificationsEnabled }}")
                setupPreference = oldPreference.copy(
                    notifications = oldPreference.notifications.copy(
                        appNotifications = installedApps.map {
                            PreferenceAppNotification(
                                enable = true,
                                packageName = it.applicationInfo.packageName,
                            )
                        }
                    )
                )
            }
            val collator = Collator.getInstance(Locale.getDefault()).apply {
                strength = Collator.TERTIARY
            }
            val data = WatchAppNotificationsPermissionsContainer(
                appNotificationSwitch = buildNotificationSwitchItem(oldPreference.notifications.appEnable),
                installedApps = installedApps.map { item ->
                    oldPreference.notifications.appNotifications.find { it.packageName == item.applicationInfo.packageName }
                        ?.let {
                            item.copy(
                                notificationsEnabled = it.enable
                            )
                        } ?: item
                }.sortedWith(compareBy(collator) { it.appName })
            )
            notifyDataLoaded(data)
        }
    }

    override fun onAppNotificationSwitchEnabled(enabled: Boolean) {
        Timber.d("update all apps notification: %s", enabled)
        setupPreference?.let { oldPreference ->
            setupPreference = oldPreference.copy(
                notifications = oldPreference.notifications.copy(
                    appEnable = enabled
                )
            )
        }
    }

    override fun onPackageEnable(packageName: String, enabled: Boolean) {
        Timber.d("update app notification: %s, %s", packageName, enabled)
        setupPreference?.let { oldPreference ->
            setupPreference = oldPreference.copy(
                notifications = oldPreference.notifications.copy(
                    appNotifications = oldPreference.notifications.appNotifications.map {
                        if (it.packageName == packageName) {
                            it.copy(
                                enable = enabled
                            )
                        } else {
                            it
                        }
                    }
                )
            )
        }
    }

    override fun trackInstalledAppItems(installedAppItems: List<InstalledAppItem>) {
        // do nothing
    }

    companion object {
        const val KEY_SETUP_PREFERENCE = "setup_preference"
    }
}
