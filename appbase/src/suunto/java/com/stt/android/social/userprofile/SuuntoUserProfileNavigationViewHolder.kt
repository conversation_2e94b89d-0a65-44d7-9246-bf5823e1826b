package com.stt.android.social.userprofile

import android.view.View
import com.stt.android.FeatureFlags
import com.stt.android.controllers.CurrentUserController
import com.stt.android.databinding.ItemUserProfileNavigationBinding
import com.stt.android.ui.extensions.setVisible

open class SuuntoUserProfileNavigationViewHolder(
    listener: NavigationClickListener,
    currentUserController: CurrentUserController,
    binding: ItemUserProfileNavigationBinding,
    private val featureFlags: FeatureFlags,
) : BaseUserProfileNavigationViewHolder(listener, currentUserController, binding) {

    override fun setupNavigationItems() {
        // setup Suunto specific item
        binding.connectedServices.visibility = View.VISIBLE
        binding.servicesDivider.visibility = View.VISIBLE
        binding.connectedServices.setOnClickListener {
            listener.onPartnerServicesClicked()
        }
        if (featureFlags.isCustomerSupportChatBotEnabled) {
            binding.chatBot.visibility = View.VISIBLE
            binding.chatBotDivider.visibility = View.VISIBLE
            binding.chatBot.setOnClickListener {
                listener.onChatBotClicked()
            }
        }
        with(binding.feedback) {
            setOnClickListener {
                listener.onFeedbackClicked()
            }
        }
        binding.personalRecord.visibility = View.VISIBLE
        binding.personalRecordDivider.visibility = View.VISIBLE
        binding.personalRecord.setOnClickListener {
            listener.onPersonalRecord()
        }
        super.setupNavigationItems()
    }

    override fun setupMyHeadsetItem() {
        val headsetRedDot = listener.provideHeadsetRedDot()
        binding.ivMyHeadsetRedDot.visibility =
            if (headsetRedDot.showHeadsetRedDot()) View.VISIBLE else View.GONE
        binding.settingsHeadsetGroup.setVisible()
        binding.settingsHeadset.setOnClickListener {
            headsetRedDot.markHeadsetRedDotShow()
            listener.onMyHeadsetClicked()
        }
    }
}
