package com.stt.android.home.settings.hrintensityzones

import com.stt.android.watch.GetWatchCapabilities
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class IsHrIntensityZonesSupportedUseCase @Inject constructor(
    private val getWatchCapabilities: GetWatchCapabilities,
) {

    fun isHrIntensityZonesSupported(): Flow<Boolean> =
        getWatchCapabilities.getCurrentCapabilitiesAsFlow().map {
            it.capabilities?.hrIntensityZonesSupported ?: false
        }
}
