package com.stt.android.home.settings.v2

import android.content.Context
import android.util.AttributeSet
import com.stt.android.BuildConfig
import com.stt.android.home.settings.v2.ui.SettingItemType
import com.stt.android.usecases.startup.LowPriorityStartupUseCase.Companion.STORE_NAME_CHINA

class EditUserProfilePreference @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.preferenceStyle,
    defStyleRes: Int = defStyleAttr
) : BaseEditUserProfilePreference(context, attrs, defStyleAttr, defStyleRes)  {
    override fun getSettingTypes(): List<SettingItemType> {
        val commonItems = listOf(
            SettingItemType.REAL_NAME,
            SettingItemType.USER_NAME,
            SettingItemType.LOCATION_SWITCH,
            SettingItemType.BIO,
            SettingItemType.COVER_PHOTO
        )
        if (BuildConfig.FLAVOR_store.equals(STORE_NAME_CHINA)) {
            return commonItems.toMutableList().apply {
                remove(SettingItemType.LOCATION_SWITCH)
            }
        }
        return commonItems
    }
}
