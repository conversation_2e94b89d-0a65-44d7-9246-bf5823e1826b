package com.stt.android.home.diary.analytics

/**
 * Helper data class for storing formatted Suunto 247 sleep analytics data.
 */
data class AnalyticsSleepProperties(
    val sleepHours: String? = null,
    val deepSleep: String? = null,
    val awakeTime: String? = null,
    val fellAsleep: String? = null,
    val wokeUp: String? = null,
    val sleepGoalAchieved: Boolean? = null,
    val sleepGoal: String = "",
    val quality: Int? = null, // 0..100
    val averageHr: Int? = null,
)
