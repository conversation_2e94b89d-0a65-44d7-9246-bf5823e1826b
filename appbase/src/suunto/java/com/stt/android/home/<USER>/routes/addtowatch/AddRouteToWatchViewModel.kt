package com.stt.android.home.explore.routes.addtowatch

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.routes.Route
import com.stt.android.domain.routes.ToggleAddRouteToWatchUseCase
import com.stt.android.home.explore.routes.SuuntoRouteAnalytics
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel to handle the toggling of 'Add to watch' switch(es). It has several roles:
 *
 * <ul>
 *  <li>Update watchEnabled flag in database and trigger watch sync</li>
 *  <li>Warn user if enabling watchEnabled and watch is not connected</li>
 *  <li>Send analytics event</li>
 * </ul>
 *
 * It is used in multiple places: Route Planner, Route Details, Explore Routes.
 */
@HiltViewModel
class AddRouteToWatchViewModel @Inject constructor(
    private val toggleAddRouteToWatchUseCase: ToggleAddRouteToWatchUseCase,
    private val routeAnalyticsTracker: SuuntoRouteAnalytics,
) : ViewModel() {
    /**
     * This is the main API entry to this view model. Views that include 'Add to Watch' view, should
     * call this method when the switch is toggled.
     *
     * @param isChecked true if the switched is currently checked
     * @param route optional [Route] instance that is related to this toggle event
     * @param analyticsScreen string that represents the screen which triggered this toggle event
     */
    fun onAddToWatchToggled(
        isChecked: Boolean,
        route: Route?,
        analyticsScreen: String,
    ) {
        // When we create a new route in Route Planner, route argument will be null since there's no route instance
        // created until the user saves the route.
        route?.let {
            viewModelScope.launch {
                runSuspendCatching {
                    toggleAddRouteToWatch(route, isChecked, analyticsScreen)
                }.onFailure { e ->
                    Timber.w(e, "Failed to toggle add route to watch")
                }
            }
        }
    }

    private suspend fun toggleAddRouteToWatch(
        route: Route,
        isChecked: Boolean,
        analyticsScreen: String,
    ) {
        toggleAddRouteToWatchUseCase.toggleAddToWatch(route.id, isChecked)

        routeAnalyticsTracker.trackTooManyRoutesErrorIfNeeded(
            route.id,
            route.watchEnabled,
            analyticsScreen,
        )

        routeAnalyticsTracker.trackRouteToggleAnalyticsEvent(route.watchEnabled)
    }
}
