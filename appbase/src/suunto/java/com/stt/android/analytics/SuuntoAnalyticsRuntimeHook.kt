package com.stt.android.analytics

import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_WATCH_DAYS_OF_OWNERSHIP
import com.stt.android.data.source.local.DaoFactory
import com.suunto.connectivity.repository.AnalyticsRuntimeHook
import com.suunto.connectivity.repository.AnalyticsUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.Instant
import java.time.temporal.ChronoUnit
import javax.inject.Inject

/**
 * This class implements Suunto Connectivity module interface to handle analytics events and user properties.
 * Expect these methods to be called from connectivity process.
 */
class SuuntoAnalyticsRuntimeHook
@Inject constructor(
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val daoFactory: DaoFactory
) : AnalyticsRuntimeHook {

    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    override fun requestTrackWatchSyncResult(properties: AnalyticsProperties) {
        scope.launch(IO) {
            kotlin.runCatching {
                val serial =
                    properties.map[AnalyticsEventProperty.SUUNTO_WATCH_SERIAL_NUMBER] as? String
                if (serial != null) {
                    val firstSyncDate = daoFactory.gearDao.fetchFirstSyncDate(serial)
                    if (firstSyncDate != null) {
                        val daysOwned = ChronoUnit.DAYS.between(
                            Instant.ofEpochMilli(firstSyncDate),
                            Instant.now()
                        )
                        properties.put(SUUNTO_WATCH_DAYS_OF_OWNERSHIP, daysOwned)
                    }
                }
                requestTrackAmplitudeEvent(AnalyticsEvent.SUUNTO_SYNC_WATCH_RESULT, properties)
            }.onFailure { Timber.w(it, "Sending WatchSyncResult event failed.") }
        }
    }

    override fun requestTrackAmplitudeEvent(eventName: String, properties: AnalyticsProperties?) {
        if (properties != null) {
            amplitudeAnalyticsTracker.trackEvent(eventName, properties)
        } else {
            amplitudeAnalyticsTracker.trackEvent(eventName)
        }
    }

    override fun requestTrackAmplitudeUserProperties(userProperties: AnalyticsProperties) {
        amplitudeAnalyticsTracker.trackUserProperties(userProperties)
    }

    override fun requestTrackFirebaseUserProperty(propertyKey: String, propertyValue: String) {
        firebaseAnalyticsTracker.trackUserProperty(propertyKey, propertyValue)
    }

    override fun requestTrackFirebaseEvent(eventName: String, properties: AnalyticsProperties?) {
        firebaseAnalyticsTracker.trackEvent(eventName, properties?.toBundle())
    }

    override fun setAnalyticsUUID(uuid: String?) {
        // In UserSettings default value for the analytics UID is "".
        // Using it here as well for null uuid.
        FirebaseCrashlytics.getInstance().setUserId(uuid ?: "")
        if (amplitudeAnalyticsTracker.setUUID(uuid)) {
            AnalyticsUtils.trackDebugAndroidConnectivityDataCollected()
        }
    }

    override fun getAnalyticsUUID(): String? {
        return amplitudeAnalyticsTracker.getUUID()
    }

    override fun analyticsInitialised(): Boolean {
        return amplitudeAnalyticsTracker.analyticsInitialised()
    }
}
