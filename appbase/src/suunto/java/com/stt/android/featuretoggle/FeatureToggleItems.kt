package com.stt.android.featuretoggle

import com.stt.android.BuildConfig
import com.stt.android.utils.STTConstants.FeatureTogglePreferences
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_COMPANION_LINKING_TEST_MODE_FEATURE_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_DISABLE_SUUNTO_PLUS_WATCH_SYNC_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_DIVE_PLANNER_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_HEADSET_RUNNING_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_SELECT_SPORTS_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_WATCH_WIDGETS_WITHOUT_CAPABILITY_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ONLY_FOR_TESTING_PURPOSES_HRV_GRAPH_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_OTA_UPDATE_STOP_BUTTON_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_SHOW_AI_TRAINING_PLANNER_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_SHOW_SUUNTO_HEADSET_MAC_DISPLAY_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_SHOW_WATCH_DEBUG_LOCATION_OPTION_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_SIGN_UP_QUESTIONNAIRE_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_TEST_MENSTRUAL_CYCLE_REMINDER_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_USE_HELPSHIFT_TEST_APP_DEFAULT

fun featureKeys(isFieldTester: Boolean) = listOfNotNull(
    FeatureTogglePreferences.KEY_USE_HELPSHIFT_TEST_APP,
    FeatureTogglePreferences.KEY_COMPANION_LINKING_TEST_MODE_FEATURE,
    FeatureTogglePreferences.KEY_OTA_UPDATE_STOP_BUTTON,
    FeatureTogglePreferences.KEY_SIGN_UP_QUESTIONNAIRE,
    FeatureTogglePreferences.KEY_DIVE_PLANNER,
    FeatureTogglePreferences.KEY_ENABLE_SELECT_SPORTS,
    FeatureTogglePreferences.KEY_ENABLE_WATCH_WIDGETS_WITHOUT_CAPABILITY,
    FeatureTogglePreferences.KEY_DISABLE_SUUNTO_PLUS_WATCH_SYNC,
    FeatureTogglePreferences.KEY_ONLY_FOR_TESTING_PURPOSES_HRV_GRAPH,
    FeatureTogglePreferences.KEY_SHOW_SUUNTO_HEADSET_MAC_DISPLAY.takeIf { isFieldTester },
    FeatureTogglePreferences.KEY_SHOW_WATCH_DEBUG_LOCATION_OPTION,
    FeatureTogglePreferences.KEY_TEST_MENSTRUAL_CYCLE_REMINDER,
    FeatureTogglePreferences.KEY_SHOW_AI_TRAINING_PLANNER,
    FeatureTogglePreferences.KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES,
    FeatureTogglePreferences.KEY_ENABLE_NEW_HOME_UI.takeUnless { isFieldTester },
    FeatureTogglePreferences.KEY_ENABLE_NEW_HOME_UI_FIELD_TESTER.takeIf { isFieldTester },
    FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGET_CHART,
    FeatureTogglePreferences.KEY_ENABLE_ZONE_SENSE_DEBUG_INFO,
    FeatureTogglePreferences.KEY_ENABLE_HEADSET_RUNNING,
    FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS,
    FeatureTogglePreferences.KEY_ENABLE_MARKETING_BANNER_API_RATE,
    FeatureTogglePreferences.KEY_ENABLE_QUICK_NAVIGATION,
    FeatureTogglePreferences.KEY_NEW_TRAINING_ZONE,
    FeatureTogglePreferences.KEY_TRAINING_PLAN,
    FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES,
    FeatureTogglePreferences.KEY_ENABLE_NEW_USER_PROFILE,
    FeatureTogglePreferences.KEY_ENABLE_SU09,
    FeatureTogglePreferences.KEY_GMAIL_LOGIN,
    FeatureTogglePreferences.KEY_NEW_STRUCTURED_WORKOUT.takeIf { isFieldTester },
    FeatureTogglePreferences.KEY_ENABLE_NEW_FRIENDS,
    FeatureTogglePreferences.KEY_ENABLE_FIELD_TESTER_FOR_DEBUG.takeIf { BuildConfig.DEBUG }, // We want to make sure this is only available for Debug build for obvious reasons.
)

// Suunto app specific FeatureItems
fun getStaticFeatures(key: String): FeatureItem {
    return when (key) {
        FeatureTogglePreferences.KEY_USE_HELPSHIFT_TEST_APP ->
            FeatureItem("Helpshift test mode", key, KEY_USE_HELPSHIFT_TEST_APP_DEFAULT, true)

        FeatureTogglePreferences.KEY_COMPANION_LINKING_TEST_MODE_FEATURE ->
            FeatureItem(
                "Companion linking test mode",
                key,
                KEY_COMPANION_LINKING_TEST_MODE_FEATURE_DEFAULT,
                false
            )

        FeatureTogglePreferences.KEY_OTA_UPDATE_STOP_BUTTON ->
            FeatureItem("OTA update stop button", key, KEY_OTA_UPDATE_STOP_BUTTON_DEFAULT, false)

        FeatureTogglePreferences.KEY_SIGN_UP_QUESTIONNAIRE ->
            FeatureItem(
                "Always show questionnaire in sign-up",
                key,
                KEY_SIGN_UP_QUESTIONNAIRE_DEFAULT,
                requireProcessKill = false
            )

        FeatureTogglePreferences.KEY_DIVE_PLANNER ->
            FeatureItem("Dive planner", key, KEY_DIVE_PLANNER_DEFAULT, requireProcessKill = true)

        FeatureTogglePreferences.KEY_ENABLE_SELECT_SPORTS ->
            FeatureItem(
                "Enable 'Select sports' in onboarding",
                key,
                KEY_ENABLE_SELECT_SPORTS_DEFAULT,
                requireProcessKill = false
            )

        FeatureTogglePreferences.KEY_ENABLE_WATCH_WIDGETS_WITHOUT_CAPABILITY ->
            FeatureItem(
                "Enable watch widgets for unsupported devices",
                key,
                KEY_ENABLE_WATCH_WIDGETS_WITHOUT_CAPABILITY_DEFAULT,
                requireProcessKill = false
            )

        FeatureTogglePreferences.KEY_DISABLE_SUUNTO_PLUS_WATCH_SYNC ->
            FeatureItem(
                "Disable syncing SuuntoPlus™ apps to watch",
                key,
                KEY_DISABLE_SUUNTO_PLUS_WATCH_SYNC_DEFAULT,
                requireProcessKill = true
            )

        FeatureTogglePreferences.KEY_ONLY_FOR_TESTING_PURPOSES_HRV_GRAPH ->
            FeatureItem(
                name = "Show HRV graph",
                key = key,
                enabled = KEY_ONLY_FOR_TESTING_PURPOSES_HRV_GRAPH_DEFAULT,
                requireProcessKill = false
            )

        FeatureTogglePreferences.KEY_SHOW_SUUNTO_HEADSET_MAC_DISPLAY ->
            FeatureItem("mac display for eagle feature", key, KEY_SHOW_SUUNTO_HEADSET_MAC_DISPLAY_DEFAULT)

        FeatureTogglePreferences.KEY_SHOW_WATCH_DEBUG_LOCATION_OPTION ->
            FeatureItem("Show 'Debug location' menu option", key, KEY_SHOW_WATCH_DEBUG_LOCATION_OPTION_DEFAULT
            )

        FeatureTogglePreferences.KEY_TEST_MENSTRUAL_CYCLE_REMINDER ->
            FeatureItem("Test menstrual cycle reminder", key, KEY_TEST_MENSTRUAL_CYCLE_REMINDER_DEFAULT)

        FeatureTogglePreferences.KEY_SHOW_AI_TRAINING_PLANNER ->
            FeatureItem("Enable AI training planner", key, KEY_SHOW_AI_TRAINING_PLANNER_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES ->
            FeatureItem("Enable DiLu onboarding watch faces", key, KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_NEW_HOME_UI ->
            FeatureItem("Enable new home UI", key, FeatureTogglePreferences.KEY_ENABLE_NEW_HOME_UI_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_NEW_HOME_UI_FIELD_TESTER ->
            FeatureItem("Enable new home UI", key, FeatureTogglePreferences.KEY_ENABLE_NEW_HOME_UI_FIELD_TESTER_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGET_CHART ->
            FeatureItem("Enable new widget chart", key, FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGET_CHART_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_ZONE_SENSE_DEBUG_INFO ->
            FeatureItem("Enable ZoneSense debug info", key, FeatureTogglePreferences.KEY_ENABLE_ZONE_SENSE_DEBUG_INFO_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_HEADSET_RUNNING ->
            FeatureItem("Enable Headset Running", key, KEY_ENABLE_HEADSET_RUNNING_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS ->
            FeatureItem("Enable new widgets", key, FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_MARKETING_BANNER_API_RATE ->
            FeatureItem("Enable marketing banner API rate limitation", key, FeatureTogglePreferences.KEY_ENABLE_MARKETING_BANNER_API_RATE_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_QUICK_NAVIGATION ->
            FeatureItem("Enable quick navigation", key, FeatureTogglePreferences.KEY_ENABLE_QUICK_NAVIGATION_DEFAULT)

        FeatureTogglePreferences.KEY_NEW_TRAINING_ZONE ->
            FeatureItem("Enable new training zone", key, FeatureTogglePreferences.KEY_NEW_TRAINING_ZONE_DEFAULT)

        FeatureTogglePreferences.KEY_TRAINING_PLAN ->
            FeatureItem("Enable training plan", key, FeatureTogglePreferences.KEY_TRAINING_PLAN_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES ->
            FeatureItem("Enable TopRoutes features", key, FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_NEW_USER_PROFILE ->
            FeatureItem("Enable new user profile", key, FeatureTogglePreferences.KEY_ENABLE_NEW_USER_PROFILE_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_SU09 ->
            FeatureItem("Enable SU09", key, FeatureTogglePreferences.KEY_ENABLE_SU09_DEFAULT)

        FeatureTogglePreferences.KEY_GMAIL_LOGIN ->
            FeatureItem("Enable Gmail login", key, FeatureTogglePreferences.KEY_GMAIL_LOGIN_DEFAULT)

        FeatureTogglePreferences.KEY_NEW_STRUCTURED_WORKOUT ->
            FeatureItem("Enable New structured workout", key, FeatureTogglePreferences.KEY_NEW_STRUCTURED_WORKOUT_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_NEW_FRIENDS ->
            FeatureItem("Enable new friends", key, FeatureTogglePreferences.KEY_ENABLE_NEW_FRIENDS_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_FIELD_TESTER_FOR_DEBUG ->
            FeatureItem(
                name = "Enable field tester role (for debugging only)",
                key = key,
                enabled = FeatureTogglePreferences.KEY_ENABLE_FIELD_TESTER_FOR_DEBUG_DEFAULT,
                requireProcessKill = true,
            )

        else -> throw IllegalArgumentException("Unknown key")
    }
}
