package com.stt.android.ui.rating

import android.content.Context
import android.content.SharedPreferences
import androidx.test.core.app.ApplicationProvider
import app.cash.turbine.test
import com.google.android.play.core.review.testing.FakeReviewManager
import com.google.common.truth.Truth.assertThat
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.user.UserSettings
import com.stt.android.help.LoadSupportMetadataUseCase
import com.stt.android.inappreview.InAppRatingFragmentViewModel
import com.stt.android.inappreview.InAppRatingType
import com.stt.android.testutils.NewCoroutinesTestRule
import com.stt.android.utils.STTConstants
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_ASK_ME_LATER_CLICKED
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_NEVER_SHOW_AGAIN_CLICKED
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class InAppRatingFragmentViewModelTest {
    @Rule
    @JvmField
    val coroutinesTestRule = NewCoroutinesTestRule()

    @Mock
    private lateinit var sharedPreferences: SharedPreferences

    @Mock
    private lateinit var editor: SharedPreferences.Editor

    @Mock
    private lateinit var emarsysAnalytics: EmarsysAnalytics

    @Mock
    private lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    @Mock
    private lateinit var userSettingsController: UserSettingsController

    @Mock
    private lateinit var userSettings: UserSettings

    private lateinit var context: Context
    private lateinit var viewModel: InAppRatingFragmentViewModel

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()

        whenever(sharedPreferences.edit()).thenReturn(editor)

        whenever(userSettingsController.settings).thenReturn(userSettings)

        viewModel = InAppRatingFragmentViewModel(
            sharedPreferences = sharedPreferences,
            reviewManager = FakeReviewManager(context),
            emarsysAnalytics = emarsysAnalytics,
            amplitudeAnalyticsTracker = amplitudeAnalyticsTracker,
            loadSupportMetadataUseCase = LoadSupportMetadataUseCase(
                appContext = context,
                appVersionNumberForSupport = "123",
                isSTFlavor = false,
                subscriptionItemController = mock()
            ),
            userSettingsController = userSettingsController
        )
    }

    @Test
    fun negativeOrNaturalRatingAsksTheUserToGiveFeedback() = runTest {
        viewModel.uiState.test {
            viewModel.onNegativeOrNaturalRatingClick()
            assertThat(expectMostRecentItem().inAppRatingType).isEqualTo(InAppRatingType.AskForFeedback)
        }
    }

    @Test
    fun positiveRatingSelectionDefferShowingTheDialogToAskTheUserToRateTheApp() = runTest {
        viewModel.onPositiveRatingClick()
        verify(sharedPreferences.edit()).putBoolean(
            STTConstants.InAppReviewPreferences.KEY_POSITIVE_FEELING_CLICKED,
            true
        )
        verify(sharedPreferences.edit()).putLong(
            eq(STTConstants.InAppReviewPreferences.KEY_TIMESTAMP_WHEN_POSITIVE_FEELING_CLICKED),
            any()
        )
    }

    @Test
    fun userClicksOnAskMeLaterMustSetTheAskMeLaterFlagToTrue() {
        viewModel.onAskMeLaterClicked()
        verify(sharedPreferences.edit()).putBoolean(
            KEY_ASK_ME_LATER_CLICKED,
            true
        )
    }

    @Test
    fun userClicksOnCloseMeansHeIsNotHappyAndDialogMustNotBeShownToHimAgain() {
        viewModel.onCloseClicked()
        verify(sharedPreferences.edit()).putBoolean(
            KEY_NEVER_SHOW_AGAIN_CLICKED,
            true
        )
    }

    @Test
    fun userClicksOnNeverShowAgain_mustUpdateUSerSettings() {
        viewModel.onNeverAskAgainClicked()
        verify(userSettingsController.settings).disabledAppRatingSuggestions = listOf(
            "SUUNTO_APP_ANDROID"
        ).toTypedArray()
    }
}
