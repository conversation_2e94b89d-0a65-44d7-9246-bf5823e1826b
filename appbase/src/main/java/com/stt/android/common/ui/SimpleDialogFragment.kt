package com.stt.android.common.ui

import android.app.Activity
import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import com.stt.android.R
import com.stt.android.common.ui.SimpleDialogFragment.Callback

/**
 * SimpleDialogFragment is generic DialogFragment implementation which simplifies dialog usage.
 * If used from an activity, make sure Activity implements [Callback] interface.
 * If used from a fragment, before showing call [SimpleDialogFragment.setTargetFragment] and listen to events
 * in the [Fragment.onActivityResult] method.
 */
class SimpleDialogFragment : DialogFragment() {

    var canceledOnTouchOutside = true

    /**
     * Callback interface methods are called when user interacts with the SimpleDialogFragment instance.
     * Activities using SimpleDialogFragment need to implement this interface.
     */
    interface Callback {
        /**
         * Dialog button pressed
         *
         * @param tag Tag associated with this DialogFragment
         * @param which Button that was pressed, one of [DialogInterface.BUTTON_POSITIVE],
         * [DialogInterface.BUTTON_NEUTRAL], [DialogInterface.BUTTON_NEGATIVE]
         */
        fun onDialogButtonPressed(tag: String?, which: Int)

        /**
         * Dialog dismissed
         *
         * @param tag Tag associated with this DialogFragment
         */
        fun onDialogDismissed(tag: String?)
    }

    private var listener: Callback? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        listener = activity as? Callback
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        arguments?.let { bundle ->
            val title = bundle.getCharSequence(TITLE)
            val message = bundle.getCharSequence(MESSAGE)
            val positiveButton = bundle.getCharSequence(POSITIVE_TEXT)
            val negativeButton = bundle.getCharSequence(NEGATIVE_TEXT)
            val cancellable = bundle.getBoolean(CANCELLABLE)

            val builder = AlertDialog.Builder(requireContext())
                .setView(R.layout.custom_alert_dialog)
                .setMessage(message)
                .setCancelable(cancellable)

            title?.let {
                builder.setTitle(it)
            }

            positiveButton?.let {
                builder.setPositiveButton(it) { _, which ->
                    listener?.onDialogButtonPressed(tag, which)
                    activity?.let { activity ->
                        targetFragment?.onActivityResult(targetRequestCode, RESULT_POSITIVE, activity.intent)
                    }
                }
            }

            negativeButton?.let {
                builder.setNegativeButton(it) { _, which ->
                    listener?.onDialogButtonPressed(tag, which)
                    activity?.let { activity ->
                        targetFragment?.onActivityResult(targetRequestCode, RESULT_NEGATIVE, activity.intent)
                    }
                }
            }
            return builder.create().apply {
                setCanceledOnTouchOutside(canceledOnTouchOutside)
            }
        }
        throw IllegalStateException("Cannot create dialog, arguments cannot be null")
    }

    override fun onCancel(dialog: DialogInterface) {
        activity?.let { activity ->
            targetFragment?.onActivityResult(targetRequestCode, RESULT_CANCELED, activity.intent)
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        listener?.onDialogDismissed(tag)
    }

    companion object {
        private const val TITLE = "title"
        private const val MESSAGE = "message"
        private const val POSITIVE_TEXT = "positive_button_text"
        private const val NEGATIVE_TEXT = "negative_button_text"
        private const val CANCELLABLE = "cancellable"
        const val RESULT_POSITIVE: Int = Activity.RESULT_OK // -1
        const val RESULT_NEGATIVE: Int = -2
        const val RESULT_CANCELED: Int = Activity.RESULT_CANCELED // 0

        @JvmStatic
        @JvmOverloads
        fun newInstance(
            message: CharSequence,
            title: CharSequence? = null,
            positiveButtonText: CharSequence? = null,
            negativeButtonText: CharSequence? = null,
            cancellable: Boolean = true
        ): SimpleDialogFragment {
            val dialogFragment = SimpleDialogFragment()
            dialogFragment.arguments = Bundle().apply {
                putCharSequence(TITLE, title) // putString(key, null) is fine
                putCharSequence(MESSAGE, message)
                putCharSequence(POSITIVE_TEXT, positiveButtonText)
                putCharSequence(NEGATIVE_TEXT, negativeButtonText)
                putBoolean(CANCELLABLE, cancellable)
            }

            return dialogFragment
        }
    }
}
