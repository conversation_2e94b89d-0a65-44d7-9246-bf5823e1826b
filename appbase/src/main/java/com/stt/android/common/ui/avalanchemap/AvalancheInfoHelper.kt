package com.stt.android.common.ui.avalanchemap

import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.fragment.app.FragmentManager
import com.stt.android.di.MapPreferences
import com.stt.android.domain.user.AVALANCHE
import com.stt.android.domain.user.MapType
import com.stt.android.utils.STTConstants.MapPreferences.KEY_SEEN_AVALANCHE_INFO
import javax.inject.Inject

class AvalancheInfoHelper @Inject constructor(
    @MapPreferences private val mapPreferences: SharedPreferences
) {
    private val seenAvalancheInfo: Boolean
        get() = mapPreferences.getBoolean(KEY_SEEN_AVALANCHE_INFO, false)

    fun showAvalancheInfoIfNeeded(mapType: MapType, fm: FragmentManager) {
        if (!seenAvalancheInfo && mapType == AVALANCHE) {
            mapPreferences.edit { putBoolean(KEY_SEEN_AVALANCHE_INFO, true) }
            AvalancheInfoPopupFragment.newInstance().show(fm, null)
        }
    }
}
