package com.stt.android.social.workoutlist

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import androidx.activity.SystemBarStyle
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.material3.header
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.databinding.ComposeMapSnapshotterBinding
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.maps.MapSnapshotter
import com.stt.android.social.userprofile.BaseUserProfileActivity
import com.stt.android.social.workoutlist.search.SearchWorkoutActivity
import com.stt.android.social.workoutlist.ui.PhotoViewerScreen
import com.stt.android.social.workoutlist.ui.WorkoutListTab
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class AllWorkoutActivity : AppCompatActivity() {
    @Inject
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    @Inject
    lateinit var mapSnapshotter: MapSnapshotter

    private val binding: ComposeMapSnapshotterBinding by lazy {
        ComposeMapSnapshotterBinding.inflate(layoutInflater)
    }

    private val allWorkoutViewModel: AllWorkoutViewModel by viewModels()

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                mapSnapshotter.runSnapshotterEngine(this@AllWorkoutActivity)
            }
        }
        val user = intent.getParcelableExtra<User>(BaseUserProfileActivity.Companion.KEY_USER)
        allWorkoutViewModel.initUserInfo(user?.username)
        setContentView(binding.root)
        binding.composeView.setContentWithM3Theme {
            var selectedPhotoIndex by remember { mutableStateOf<Int?>(null) }

            val viewState by allWorkoutViewModel.viewState.collectAsState()
            val photos = (viewState as? AllWorkoutViewModel.ViewData.Loaded)?.images
                ?: emptyList()
            LaunchedEffect(photos) {
                if (photos.none()) {
                    selectedPhotoIndex = null
                }
            }

            Box(modifier = Modifier.fillMaxSize()){
                WorkoutListScreen(
                    onBackClicked = ::finish,
                    openWorkout = { workoutHeader ->
                        rewriteNavigator.navigate(
                            context = this@AllWorkoutActivity,
                            username = workoutHeader.username,
                            workoutId = workoutHeader.id,
                            workoutKey = workoutHeader.key,
                        )
                    },
                    onPhotoClick = { index, photo ->
                        selectedPhotoIndex = index
                    },
                )
                // Full screen photo viewer
                selectedPhotoIndex?.let { index ->
                    enableEdgeToEdge(
                        statusBarStyle = SystemBarStyle.dark(Color.TRANSPARENT),
                        navigationBarStyle = SystemBarStyle.dark(Color.TRANSPARENT),
                    )
                    PhotoViewerScreen(
                        photos = photos,
                        initialPhotoIndex = index,
                        onBackClick = {
                            selectedPhotoIndex = null
                            enableEdgeToEdge(
                                statusBarStyle = SystemBarStyle.light(Color.TRANSPARENT, Color.BLACK),
                                navigationBarStyle = SystemBarStyle.light(Color.TRANSPARENT, Color.BLACK),
                            )
                        },
                        onCheckActivities = {
                            rewriteNavigator.navigate(
                                context = this@AllWorkoutActivity,
                                username = it.username,
                                workoutId = it.workoutId,
                                workoutKey = it.workoutKey,
                            )
                        }
                    )
                }
            }
        }
    }

    @Composable
    fun WorkoutListScreen(
        onBackClicked: () -> Unit,
        openWorkout: (WorkoutHeader) -> Unit,
        modifier: Modifier = Modifier,
        onPhotoClick: ((index: Int, photo: ImageInformation) -> Unit)? = null
    ) {
        val viewState by allWorkoutViewModel.viewState.collectAsState()
        Scaffold(
            topBar = {
                WorkoutListToolbar(
                    onBackClicked = onBackClicked,
                )
            },
            modifier = modifier,
        ) { paddingValues ->
            WorkoutListTab(
                viewState = viewState,
                openWorkout = openWorkout,
                modifier = Modifier.padding(paddingValues),
                onPhotoClick = onPhotoClick,
                onRetryClicked = allWorkoutViewModel::load,
                measurementUnit = allWorkoutViewModel.measurementUnit,
                lastOperationHistory = allWorkoutViewModel.lastOperationHistory,
                onTabSelected = allWorkoutViewModel::onTabSelected,
                onFilterSelected = allWorkoutViewModel::onFilterSelected,
            )
        }
    }
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    private fun WorkoutListToolbar(
        onBackClicked: () -> Unit,
        modifier: Modifier = Modifier
    ) {
        TopAppBar(
            title = {
                Text(
                    text = stringResource(id = R.string.all_activities_title).uppercase(),
                    style = MaterialTheme.typography.header,
                )
            },
            modifier = modifier,
            navigationIcon = {
                SuuntoIconButton(
                    icon = SuuntoIcons.ActionBack,
                    onClick = onBackClicked,
                    contentDescription = stringResource(R.string.back),
                )
            },
            actions = {
                IconButton(
                    onClick = {
                        startActivity(
                            SearchWorkoutActivity.newStartIntent(
                                this@AllWorkoutActivity,
                                allWorkoutViewModel.userName
                            )
                        )
                    },
                ) {
                    Icon(
                        painter = painterResource(com.stt.android.compose.ui.R.drawable.search_outline),
                        contentDescription = stringResource(R.string.search),
                    )
                }
            }
        )
    }

    companion object {
        private const val KEY_USER = "user"
        @JvmStatic
        fun newStartIntent(context: Context, user: User): Intent {
            return Intent(context, AllWorkoutActivity::class.java).putExtra(KEY_USER, user)
        }
    }
}
