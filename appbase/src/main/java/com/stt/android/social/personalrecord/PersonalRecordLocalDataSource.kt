package com.stt.android.social.personalrecord

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.utils.STTConstants
import com.stt.android.utils.awaitFirstNonNull
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class PersonalRecordLocalDataSource @Inject constructor(
    @SuuntoSharedPrefs private val suuntoSharedPrefs: SharedPreferences,
    private val personalRecordLocalMapper: PersonalRecordLocalMapper,
    private val workoutHeaderController: WorkoutHeaderController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val currentUserController: CurrentUserController,
): PersonalRecordDataSource {

    override suspend fun getPersonalRecord(firstDelayNeeded: Boolean): List<PersonalRecordResultItem> {
        return suuntoSharedPrefs.getString(
            STTConstants.SuuntoPreferences.KEY_PERSONAL_RECORD, null
        )?.toPersonalRecordList() ?: emptyList()
    }

    suspend fun savePersonalRecord(personalRecordResultItemList: List<PersonalRecordResultItem>) {
        getLatestUpdatedWorkoutTimestamp().let {
            suuntoSharedPrefs.edit {
                putLong(
                    STTConstants.SuuntoPreferences.KEY_PERSONAL_RECORD_TIMESTAMP,
                    it
                )
                putString(
                    STTConstants.SuuntoPreferences.KEY_PERSONAL_RECORD,
                    personalRecordResultItemList.toJsonString()
                )
            }
        }
    }

    private fun String.toPersonalRecordList(): List<PersonalRecordResultItem> {
        return personalRecordLocalMapper.toDomainEntity()(this)
    }

    private fun List<PersonalRecordResultItem>.toJsonString(): String {
        return personalRecordLocalMapper.toDataEntity()(this)
    }

    suspend fun currentUserWorkoutUpdated():Boolean {
        val latestOwnWorkoutTimestamp = getLatestUpdatedWorkoutTimestamp()
        val lastRecordTimestamp = suuntoSharedPrefs.getLong(
            STTConstants.SuuntoPreferences.KEY_PERSONAL_RECORD_TIMESTAMP,
            0L
        )
        return latestOwnWorkoutTimestamp > lastRecordTimestamp
    }

    private suspend fun getLatestUpdatedWorkoutTimestamp() = withContext(coroutinesDispatchers.io) {
        runCatching {
            workoutHeaderController.currentUserWorkoutUpdatesAsObservable
                .filter { it.operation == WorkoutHeaderController.WorkoutUpdate.SYNCED }
                .timeout(1L, TimeUnit.MILLISECONDS)
                .awaitFirstNonNull()
                .workoutHeader
                .stopTime
        }.getOrElse {
            workoutHeaderController
                .getLatestWorkoutStopTime(listOf(currentUserController.username))
        }

    }
}
