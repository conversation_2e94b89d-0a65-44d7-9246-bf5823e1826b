package com.stt.android.social.userprofileV2

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.view.ContextThemeWrapper
import androidx.activity.SystemBarStyle
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScaffoldDefaults
import androidx.compose.material3.SnackbarHost
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.material3.Surface
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.helpshift.support.Support
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.compose.widgets.M3ConfirmationDialog
import com.stt.android.core.utils.EventThrottler
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.databinding.ComposeMapSnapshotterBinding
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.user.User
import com.stt.android.exceptions.BackendException
import com.stt.android.featuretoggle.OpenFeatureToggleHandlerV2
import com.stt.android.follow.FollowStatus
import com.stt.android.follow.UserFollowStatus
import com.stt.android.help.LoadSupportMetadataUseCase
import com.stt.android.home.explore.WorkoutMapNavigator
import com.stt.android.home.people.formatRevokeFollowerMessage
import com.stt.android.home.settings.SettingsActivity
import com.stt.android.maps.MapSnapshotter
import com.stt.android.social.following.PeopleActivity
import com.stt.android.social.personalrecord.PersonalRecordsActivity
import com.stt.android.social.userprofile.NavigationClickListener
import com.stt.android.social.userprofile.followlist.FollowListActivity
import com.stt.android.social.userprofile.followlist.FollowListType
import com.stt.android.social.userprofileV2.ui.FeatureToggle
import com.stt.android.social.userprofileV2.ui.UserProfileScreen
import com.stt.android.social.workoutlist.AllWorkoutActivity
import com.stt.android.social.workoutlist.AllWorkoutViewModel
import com.stt.android.ui.utils.DialogHelper
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

abstract class BaseUserProfileActivity : AppCompatActivity(), NavigationClickListener,
    SimpleDialogFragment.Callback {

    private val viewModel: UserProfileViewModel by viewModels()
    private val allWorkoutViewModel: AllWorkoutViewModel by viewModels()

    @Inject
    lateinit var mapSnapshotter: MapSnapshotter

    @Inject
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    protected val clickEventThrottler = EventThrottler()

    @Inject
    lateinit var loadSupportMetadataUseCase: LoadSupportMetadataUseCase

    @Inject
    lateinit var workoutMapNavigator: WorkoutMapNavigator

    private val binding: ComposeMapSnapshotterBinding by lazy {
        ComposeMapSnapshotterBinding.inflate(layoutInflater)
    }
    private val openFeatureToggleHandlerV2: OpenFeatureToggleHandlerV2 by lazy { OpenFeatureToggleHandlerV2() }

    override fun onCreate(savedInstanceState: Bundle?) {
        val isPortrait = resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT
        if (isPortrait) {
            enableEdgeToEdge(statusBarStyle = SystemBarStyle.dark(Color.TRANSPARENT))
        }
        super.onCreate(savedInstanceState)
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                mapSnapshotter.runSnapshotterEngine(this@BaseUserProfileActivity)
            }
        }
        setContentView(binding.root)
        binding.composeView.setContentWithM3Theme {
            Surface {
                val snackbarHostState = remember { SnackbarHostState() }

                val removedMessage = stringResource(R.string.user_removed_tips)
                val reportedMessage = stringResource(R.string.user_reported_tips)
                val duplicatedReportedMessage = stringResource(R.string.user_duplicate_reported_tips)
                val genericErrorMessage = stringResource(R.string.error_generic)
                val resources = LocalContext.current.resources
                val packageName = LocalContext.current.packageName

                fun UserProfileError.message() = if (throwable is BackendException) {
                    throwable.error.getLocalizedMessage(resources, packageName)
                } else genericErrorMessage

                var confirmDialogEvent by remember {
                    mutableStateOf<ConfirmDialogEvent?>(null)
                }

                LaunchedEffect(Unit) {
                    viewModel.eventFlow.collect { event ->
                        val message = when (event) {
                            FollowerRemoved -> removedMessage
                            UserReported -> reportedMessage
                            UserDuplicateReported -> duplicatedReportedMessage
                            is UserProfileError -> event.message()
                            is ConfirmDialogEvent -> {
                                confirmDialogEvent = event
                                return@collect
                            }
                        }
                        snackbarHostState.showSnackbar(
                            message = message,
                            duration = SnackbarDuration.Short,
                        )
                    }
                }

                FeatureToggle(
                    onFeatureToggleClick = {
                        openFeatureToggleHandlerV2.onClick(this)
                    },
                )

                Scaffold(
                    snackbarHost = { SnackbarHost(hostState = snackbarHostState) },
                    contentWindowInsets = if (isPortrait) WindowInsets.Companion.navigationBars else ScaffoldDefaults.contentWindowInsets
                ) { paddingValues ->
                    UserProfileScreen(
                        viewModel = viewModel,
                        allWorkoutViewModel = allWorkoutViewModel,
                        menuList = getSettingMenuList(),
                        onBackClick = ::finish,
                        onEditClick = {
                            startActivity(
                                SettingsActivity.newScreenStartIntent(
                                    this,
                                    R.string.user_settings_category
                                )
                            )
                        },
                        onAllActivityClick = {
                            startActivity(AllWorkoutActivity.newStartIntent(this, it))
                        },
                        onPersonalRecordsClick = ::onPersonalRecord,
                        onFollowersClicked = { count ->
                            onFollowCountClicked(FollowListType.FOLLOWERS, count)
                        },
                        onFollowingClicked = { count ->
                            onFollowCountClicked(FollowListType.FOLLOWING, count)
                        },
                        onFollowButtonClicked = {
                            viewModel.userFollowStatusFlow.value?.let { userFollowStatus ->
                                when (userFollowStatus.status) {
                                    FollowStatus.FOLLOWING ->
                                        // Currently FOLLOWING so the user wants to stop following
                                        showUnfollowDialog(userFollowStatus)

                                    FollowStatus.UNFOLLOWING ->
                                        viewModel.follow(
                                            userFollowStatus,
                                            AnalyticsPropertyValue.FollowSourceProperty.PROFILE_VIEW
                                        )

                                    FollowStatus.PENDING ->
                                        // Currently pending approval by other user, let's unfollow
                                        showUnfollowDialog(userFollowStatus)

                                    else -> {}
                                }
                            }
                        },
                        onFollowButtonClickedV2 = {
                            viewModel.onFollowButtonClicked()
                        },
                        onMenuClick = ::onMenuClick,
                        onPremiumSubscriptionClick = ::onPremiumSubscriptionClicked,
                        openWorkout = { workoutHeader ->
                            rewriteNavigator.navigate(
                                context = this,
                                username = workoutHeader.username,
                                workoutId = workoutHeader.id,
                                workoutKey = workoutHeader.key,
                            )
                        },
                        onMoreMenuItemClick = ::onMoreMenuClick,
                        modifier = Modifier
                            .padding(paddingValues),
                    )
                }

                confirmDialogEvent?.run {
                    M3ConfirmationDialog(
                        title = stringResource(title),
                        text = stringResource(message),
                        cancelButtonText = stringResource(R.string.cancel),
                        confirmButtonText = stringResource(confirmText),
                        onDismissRequest = { confirmDialogEvent = null },
                        onConfirm = {
                            confirmDialogEvent = null
                            onConfirm()
                        }
                    )
                }
            }
        }
        observeViewModel()
        allWorkoutViewModel.initUserInfo(viewModel.user.username)
    }

    override fun onResume() {
        super.onResume()
        viewModel.reloadUser()
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                if (viewModel.newFriendsEnabled) return@repeatOnLifecycle
                viewModel.dialogTipsEvent.collect {
                    when (it) {
                        is UserProfileViewModel.DialogTips.OnUserBlocked -> {
                            showUserBlockedDialog()
                        }

                        UserProfileViewModel.DialogTips.OnUserReported -> {
                            showUserReportedDialog()
                        }
                    }
                }
            }
        }
    }

    private fun showUserBlockedDialog() {
        val singleDialogFragment = SimpleDialogFragment.newInstance(
            getString(R.string.user_unblock_hint),
            getString(R.string.user_blocked, viewModel.user.realNameOrUsername),
            getString(R.string.ok),
            null
        )
        singleDialogFragment.show(supportFragmentManager, USER_BLOCKED_DIALOG_TAG)
    }

    private fun showUserReportedDialog() {
        val singleDialogFragment = SimpleDialogFragment.newInstance(
            getString(R.string.user_reported_message),
            getString(R.string.user_reported_title, viewModel.user.realNameOrUsername),
            getString(R.string.ok),
            null
        )
        singleDialogFragment.show(supportFragmentManager, USER_REPORTED_DIALOG_TAG)
    }

    fun onMenuClick(menu: SettingMenuInfo) {
        when (menu.type) {
            SettingMenuType.HEADPHONES -> onMyHeadsetClicked()
            SettingMenuType.SETTING -> onSettingsClicked()
            SettingMenuType.FIND_PEOPLE -> onFollowersClicked()
            SettingMenuType.FEEDBACK -> onFeedbackClicked()
            SettingMenuType.REPAIR_SERVICE -> onAfterSalesServiceClicked()
            SettingMenuType.PARTNER_SERVICE -> onPartnerServicesClicked()
            SettingMenuType.SUPPORT -> onSupportClicked()
            SettingMenuType.CHAT_BOT -> onChatBotClicked()
            SettingMenuType.CONTACT_CUSTOMER_SERVICE -> onContactCustomerServiceClicked()
        }
    }

    fun onMoreMenuClick(menu: MoreMenu) {
        if (viewModel.newFriendsEnabled) {
            viewModel.onMoreMenuClick(menu)
            return
        }
        when (menu) {
            MoreMenu.MAP -> viewModel.user.let { openWorkoutMap(it) }
            MoreMenu.REMOVE_FOLLOWER -> setShowRevokeFollowerConfirmation()
            MoreMenu.BLOCK -> showBlockUserConfirmationDialog()
            MoreMenu.UNBLOCK_USER -> showUnBlockUserConfirmationDialog()
            MoreMenu.REPORT -> showReportUserConfirmationDialog()
        }
    }

    private fun showBlockUserConfirmationDialog() {
        val singleDialogFragment = SimpleDialogFragment.newInstance(
            getString(R.string.block_user_message),
            getString(R.string.block_user_title, viewModel.user.realNameOrUsername),
            getString(R.string.block),
            getString(R.string.cancel)
        )
        singleDialogFragment.show(supportFragmentManager, CONFIRM_BLOCK_USER_DIALOG_TAG)
    }

    internal fun showUnBlockUserConfirmationDialog() {
        val singleDialogFragment = SimpleDialogFragment.newInstance(
            getString(R.string.unBlock_user_message),
            getString(R.string.unBlock_user_title, viewModel.user.realNameOrUsername),
            getString(R.string.unBlock),
            getString(R.string.cancel)
        )
        singleDialogFragment.show(supportFragmentManager, CONFIRM_UNBLOCK_USER_DIALOG_TAG)
    }

    private fun showReportUserConfirmationDialog() {
        val singleDialogFragment = SimpleDialogFragment.newInstance(
            getString(R.string.report_user_message),
            getString(R.string.report_user_title, viewModel.user.realNameOrUsername),
            getString(R.string.report),
            getString(R.string.cancel)
        )
        singleDialogFragment.show(supportFragmentManager, CONFIRM_REPORT_USER_DIALOG_TAG)
    }

    private fun setShowRevokeFollowerConfirmation() {
        val latestFollowerStatus = viewModel.userFollowStatusFlow.value
        if (latestFollowerStatus == null) {
            Timber.w("Cannot ask follower revoke confirmation, latestFollowerStatus=null")
            return
        }

        if (supportFragmentManager.findFragmentByTag(CONFIRM_REVOKE_DIALOG_TAG) != null) {
            Timber.d("Already showing revoke confirmation")
            return
        }

        val title = getString(R.string.revoke_dialog_title)
        val message = formatRevokeFollowerMessage(
            resources,
            latestFollowerStatus
        )

        val simpleDialogFragment = SimpleDialogFragment.newInstance(
            message,
            title,
            getString(R.string.revoke_dialog_confirm),
            getString(R.string.cancel)
        )

        simpleDialogFragment.show(supportFragmentManager, CONFIRM_REVOKE_DIALOG_TAG)
    }

    private fun openWorkoutMap(user: User) {
        workoutMapNavigator.startWorkoutMapActivity(
            this,
            user,
            AnalyticsEvent.USER_PROFILE_SCREEN,
            true
        )
    }

    override fun onDialogButtonPressed(tag: String?, which: Int) {
        if (CONFIRM_REVOKE_DIALOG_TAG == tag && which == DialogInterface.BUTTON_POSITIVE) {
            val latestFollowerStatus = viewModel.userFollowStatusFlow.value
            if (latestFollowerStatus != null) {
                viewModel.revokeFollower(latestFollowerStatus)
            } else {
                Timber.w("Cannot revoke follower, latestFollowerStatus=null")
            }
        }

        if (CONFIRM_BLOCK_USER_DIALOG_TAG == tag && which == DialogInterface.BUTTON_POSITIVE) {
            viewModel.blockUser()
        }

        if (CONFIRM_UNBLOCK_USER_DIALOG_TAG == tag && which == DialogInterface.BUTTON_POSITIVE) {
            viewModel.unblockUser()
        }

        if (CONFIRM_REPORT_USER_DIALOG_TAG == tag && which == DialogInterface.BUTTON_POSITIVE) {
            viewModel.reportUser()
        }
    }

    override fun onDialogDismissed(tag: String?) {
    }

    protected fun showUnfollowDialog(userFollowStatus: UserFollowStatus) {
        // from FollowActionViewHelper.showUnfollowDialog(this,)
        DialogHelper.showDialog(
            ContextThemeWrapper(this, R.style.WhiteTheme),
            true,
            0,
            R.string.unfollow_dialog_message,
            R.string.unfollow_dialog_confirm,
            DialogInterface.OnClickListener() { onClick, which ->
                viewModel.unfollow(userFollowStatus)
            },
            R.string.cancel,
            null
        )
    }

    abstract fun getSettingMenuList(): List<SettingMenuInfo>

    override fun onSupportClicked() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        lifecycleScope.launch {
            runSuspendCatching {
                val apiConfig = loadSupportMetadataUseCase.run()
                Support.showFAQs(this@BaseUserProfileActivity, apiConfig)
            }.onFailure { e ->
                Timber.w(e, "Failed to load valid subscription")
            }
        }
    }

    override fun onSettingsClicked() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        startActivity(SettingsActivity.newStartIntent(this))
    }

    override fun onFollowersClicked() {
        // Find people
        if (!clickEventThrottler.checkAcceptEvent()) return
        startActivity(PeopleActivity.newIntent(this))
    }

    override fun onPartnerServicesClicked() {
    }

    override fun onFollowCountClicked(
        followListType: FollowListType,
        followCount: Int
    ) {
        if (!clickEventThrottler.checkAcceptEvent()) return
        val username = viewModel.user.username
        if (viewModel.isCurrentUser) {
            when (followListType) {
                FollowListType.FOLLOWING -> startActivity(
                    PeopleActivity.newIntent(
                        context = this,
                        showFollowingTab = true
                    )
                )

                FollowListType.FOLLOWERS -> startActivity(
                    PeopleActivity.newIntent(
                        context = this,
                        showPendingRequests = true
                    )
                )
            }
        } else if (followCount > 0) {
            startActivity(FollowListActivity.newIntent(this, followListType, username))
        }
    }

    override fun onPremiumSubscriptionClicked(isSubscribed: Boolean) {
    }

    override fun onMyHeadsetClicked() {
    }

    override fun onContactCustomerServiceClicked() {
    }

    override fun onChatBotClicked() {
    }

    protected open fun trackAnalytics(event: String) {
    }

    override fun onFeedbackClicked() {
    }

    override fun onPersonalRecord() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        startActivity(PersonalRecordsActivity.newIntent(this, ANALYTICS_USER_PROFILE))
    }

    override fun onAfterSalesServiceClicked() {
    }

    companion object {
        const val KEY_USER = "com.stt.android.KEY_USER"
        const val KEY_USER_NAME = "com.stt.android.KEY_USER_NAME"
        const val KEY_FROM_NOTIFICATION = "com.stt.android.KEY_FROM_NOTIFICATION"
        private const val ANALYTICS_USER_PROFILE = "UserProfile"
        private const val CONFIRM_REVOKE_DIALOG_TAG = "confirm_revoke_dlg"
        private const val CONFIRM_BLOCK_USER_DIALOG_TAG = "confirm_block_user_dlg"
        private const val CONFIRM_REPORT_USER_DIALOG_TAG = "confirm_report_user_dlg"
        private const val CONFIRM_UNBLOCK_USER_DIALOG_TAG = "confirm_unblock_user_dlg"
        private const val USER_REPORTED_DIALOG_TAG = "user_reported_dlg"
        private const val USER_BLOCKED_DIALOG_TAG = "user_blocked_dlg"

        @JvmStatic
        fun newStartIntent(context: Context): Intent {
            return Intent(context, UserProfileActivity::class.java)
        }

        @JvmStatic
        fun newStartIntent(
            context: Context,
            userName: String,
            fromNotification: Boolean
        ): Intent {
            return Intent(
                context,
                UserProfileActivity::class.java
            )
                .putExtra(KEY_USER_NAME, userName)
                .putExtra(KEY_FROM_NOTIFICATION, fromNotification)
        }

        @JvmStatic
        fun newStartIntent(context: Context, user: User): Intent {
            return Intent(context, UserProfileActivity::class.java).putExtra(KEY_USER, user)
        }
    }
}

