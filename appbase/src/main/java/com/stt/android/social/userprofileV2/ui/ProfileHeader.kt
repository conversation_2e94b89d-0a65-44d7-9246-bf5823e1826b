package com.stt.android.social.userprofileV2.ui

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.core.graphics.drawable.toDrawable
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.stt.android.R
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.theme.veryLightGray
import com.stt.android.domain.user.User
import com.stt.android.social.userprofileV2.MoreMenu
import com.stt.android.core.R as CoreR

private val expandedHeaderWithoutStatusBarHeight = 154.dp
private val collapsedHeaderWithoutStatusBarHeight = 52.dp
private val maxAvatarSize = 98.dp
private val minAvatarSize = 36.dp

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun ProfileHeader(
    user: User,
    location: String,
    scrollState: LazyListState,
    modifier: Modifier = Modifier,
    coverPhotoResId: Int? = R.drawable.profile_cove_photo_default,
    isShowBack: Boolean = true,
    isEdit: Boolean = true,
    onEditClick: () -> Unit = {},
    onBackClick: () -> Unit = {},
    moreMenuList: List<MoreMenu>? = null,
    onMoreMenuItemClick: (MoreMenu) -> Unit = {},
    content: @Composable (PaddingValues) -> Unit
) {
    val density = LocalDensity.current
    val statusBarHeight = WindowInsets.systemBars.getTop(density)

    val expandedHeight =
        with(density) { (statusBarHeight + expandedHeaderWithoutStatusBarHeight.toPx()).toDp() }
    val collapsedHeight =
        with(density) { (statusBarHeight + collapsedHeaderWithoutStatusBarHeight.toPx()).toDp() }

    val scrollProgress by remember {
        derivedStateOf {
            val offset = scrollState.firstVisibleItemScrollOffset.toFloat()
            val scrolledItems = scrollState.firstVisibleItemIndex

            when {
                scrolledItems > 0 -> 1f
                else -> {
                    val maxScroll = with(density) { expandedHeaderWithoutStatusBarHeight.toPx() }
                    (offset / maxScroll).coerceIn(0f, 1f)
                }
            }
        }
    }

    val backgroundColor = MaterialTheme.colorScheme.surface.copy(alpha = scrollProgress)
    val useDarkIcons = scrollProgress > 0.8f

    val systemUiController = rememberSystemUiController()
    SideEffect {
        systemUiController.setStatusBarColor(
            color = Color.Transparent,
            darkIcons = useDarkIcons,
        )
    }
    val avatarSize = lerp(maxAvatarSize, minAvatarSize, scrollProgress)

    val maxAvatarOffsetY = expandedHeight - maxAvatarSize / 2
    val minAvatarOffsetY = collapsedHeight - minAvatarSize - MaterialTheme.spacing.small
    val avatarOffsetY = lerp(maxAvatarOffsetY, minAvatarOffsetY, scrollProgress)

    val imageAlpha = 1f - scrollProgress
    val gradientAlpha = 1f - scrollProgress

    BoxWithConstraints(
        modifier = modifier.fillMaxSize()
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(expandedHeight)
                .background(backgroundColor)
        ) {
            BackgroundImage(
                user = user,
                coverPhotoResId = coverPhotoResId,
                alpha = imageAlpha,
            )
            TopGradient(
                statusBarHeight = statusBarHeight,
                alpha = gradientAlpha,
            )
        }

        content(
            PaddingValues(top = expandedHeight + maxAvatarSize / 2)
        )

        HeaderTopBar(
            location = location,
            isShowBack = isShowBack,
            isEdit = isEdit,
            useDarkIcons = useDarkIcons,
            onBackClick = onBackClick,
            onEditClick = onEditClick,
            moreMenuList = moreMenuList,
            onMoreMenuItemClick = onMoreMenuItemClick,
            scrollProgress = scrollProgress,
        )

        FloatingAvatar(
            user = user,
            size = avatarSize,
            offsetY = avatarOffsetY,
            borderColor = if (useDarkIcons) {
                MaterialTheme.colorScheme.nearWhite
            } else {
                MaterialTheme.colorScheme.surface
            },
        )
    }
}

private fun lerp(start: Dp, end: Dp, fraction: Float): Dp {
    return start + ((end - start) * fraction)
}

@Composable
private fun BackgroundImage(
    user: User,
    coverPhotoResId: Int?,
    alpha: Float,
    modifier: Modifier = Modifier,
) {
    val imageData = if (user.coverImageUrl.isNullOrEmpty() && coverPhotoResId != null) {
        coverPhotoResId
    } else {
        user.coverImageUrl
    }

    AsyncImage(
        model = ImageRequest.Builder(LocalContext.current)
            .data(imageData)
            .crossfade(true)
            .apply {
                if (coverPhotoResId != null && imageData != coverPhotoResId) {
                    placeholderWithFallback(
                        LocalContext.current, coverPhotoResId
                    )
                } else if (imageData != user.coverImageUrl) {
                    val color = MaterialTheme.colorScheme.dividerColor
                    val colorDrawable = color.toArgb().toDrawable()
                    placeholderWithFallback(
                        colorDrawable
                    )
                }
            }
            .build(),
        contentDescription = null,
        modifier = modifier
            .fillMaxSize()
            .graphicsLayer {
                this.alpha = alpha
            },
        contentScale = ContentScale.Crop,
    )
}

@Composable
private fun TopGradient(
    statusBarHeight: Int,
    alpha: Float,
    modifier: Modifier = Modifier,
) {
    val density = LocalDensity.current
    val gradientHeight =
        with(density) { (statusBarHeight + collapsedHeaderWithoutStatusBarHeight.toPx()).toDp() }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(gradientHeight)
            .graphicsLayer {
                this.alpha = alpha
            }
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.nearBlack.copy(alpha = 0.45f),
                        Color.Transparent,
                    )
                )
            )
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun HeaderTopBar(
    location: String,
    isShowBack: Boolean,
    isEdit: Boolean,
    useDarkIcons: Boolean,
    scrollProgress: Float,
    onBackClick: () -> Unit,
    onEditClick: () -> Unit,
    moreMenuList: List<MoreMenu>?,
    onMoreMenuItemClick: (MoreMenu) -> Unit,
    modifier: Modifier = Modifier,
) {
    var menuExpanded by remember { mutableStateOf(false) }
    val locationAlpha = 1f - scrollProgress
    val backgroundColor = Color.White.copy(alpha = scrollProgress)
    TopAppBar(
        expandedHeight = 52.dp,
        colors = TopAppBarDefaults.topAppBarColors(backgroundColor),
        title = {
            if (location.isNotEmpty()) {
                Box(
                    modifier = Modifier
                        .graphicsLayer { alpha = locationAlpha }
                        .background(
                            color = MaterialTheme.colorScheme.surface.copy(alpha = 0.3f),
                            shape = CircleShape,
                        )
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .height(MaterialTheme.iconSizes.small)
                            .padding(horizontal = MaterialTheme.spacing.small),
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.ic_svg_loaction),
                            contentDescription = null,
                            tint = Color.Unspecified,
                        )
                        Text(
                            text = location,
                            style = MaterialTheme.typography.bodySmall,
                        )
                    }
                }
            }
        },
        navigationIcon = {
            if (isShowBack) {
                SuuntoIconButton(
                    icon = SuuntoIcons.ActionBack,
                    onClick = onBackClick,
                    contentDescription = stringResource(R.string.back),
                    tint = if (useDarkIcons) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.surface
                    },
                )
            }
        },
        actions = {
            ProfileTopBarAction(
                isEdit = isEdit,
                onEditClick = onEditClick,
                moreMenuList = moreMenuList,
                menuExpanded = menuExpanded,
                onMenuExpanded = { menuExpanded = it },
                onMoreMenuItemClick = onMoreMenuItemClick,
                modifier = Modifier.padding(end = MaterialTheme.spacing.smaller),
            )
        },
        modifier = modifier.fillMaxWidth(),
    )
}

@Composable
private fun ProfileTopBarAction(
    isEdit: Boolean,
    onEditClick: () -> Unit,
    moreMenuList: List<MoreMenu>?,
    menuExpanded: Boolean,
    onMenuExpanded: (Boolean) -> Unit,
    onMoreMenuItemClick: (MoreMenu) -> Unit,
    modifier: Modifier = Modifier,
) {
    val screenWidthDp = with(LocalDensity.current) {
        LocalWindowInfo.current.containerSize.width.toDp()
    }
    Surface(
        shape = CircleShape,
        shadowElevation = 2.dp,
        color = MaterialTheme.colorScheme.veryLightGray,
        modifier = modifier,
    ) {
        if (isEdit) {
            IconButton(
                onClick = onEditClick,
                modifier = Modifier.size(MaterialTheme.iconSizes.small),
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_svg_edit),
                    tint = Color.Unspecified,
                    contentDescription = null,
                )
            }
        } else if (!moreMenuList.isNullOrEmpty()) {
            IconButton(
                onClick = { onMenuExpanded(true) },
                modifier = Modifier.size(MaterialTheme.iconSizes.small),
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_svg_more),
                    tint = Color.Unspecified,
                    contentDescription = null,
                )
            }
            DropdownMenu(
                expanded = menuExpanded,
                onDismissRequest = { onMenuExpanded(false) },
                containerColor = MaterialTheme.colorScheme.surface,
                offset = DpOffset(screenWidthDp, MaterialTheme.spacing.small),
            ) {
                moreMenuList.forEach { menu ->
                    DropdownMenuItem(
                        text = {
                            Text(
                                text = stringResource(menu.label),
                                style = MaterialTheme.typography.bodyLarge,
                            )
                        },
                        onClick = {
                            onMenuExpanded(false)
                            onMoreMenuItemClick(menu)
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun FloatingAvatar(
    user: User,
    size: Dp,
    offsetY: Dp,
    borderColor: Color,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .zIndex(10f)
    ) {
        ProfileAvatar(
            user = user,
            size = size,
            borderWidth = 2.dp,
            borderColor = borderColor,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .offset(y = offsetY)
        )
    }
}

@Composable
private fun ProfileAvatar(
    user: User,
    size: Dp,
    borderWidth: Dp,
    borderColor: Color,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(size)
            .clip(CircleShape)
            .background(borderColor)
            .padding(borderWidth)
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current).data(user.profileImageUrl)
                .crossfade(true).placeholderWithFallback(
                    LocalContext.current,
                    CoreR.drawable.ic_default_profile_image_light
                ).transformations(CircleCropTransformation()).build(),
            contentDescription = null,
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentScale = ContentScale.Crop,
        )
    }
}

@Preview(backgroundColor = 0x88888888, showBackground = true)
@Composable
private fun HeaderTopBarPreview() {
    M3AppTheme {
        HeaderTopBar(
            location = "China",
            isShowBack = true,
            isEdit = false,
            useDarkIcons = true,
            scrollProgress = 0f,
            onBackClick = {},
            onEditClick = {},
            moreMenuList = MoreMenu.entries,
            onMoreMenuItemClick = {}
        )
    }
}
