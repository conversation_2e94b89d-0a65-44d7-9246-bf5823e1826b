package com.stt.android.social.userprofileV2.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.stt.android.R
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.User
import com.stt.android.domain.user.subscription.CurrentPremiumSubscriptionStatus
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.follow.UserFollowStatus
import com.stt.android.social.friends.Friend
import com.stt.android.social.userprofileV2.DeviceType
import com.stt.android.social.userprofileV2.FollowCountStats
import com.stt.android.social.userprofileV2.MoreMenu
import com.stt.android.social.userprofileV2.SettingMenuInfo
import com.stt.android.social.userprofileV2.SettingMenuType
import com.stt.android.social.userprofileV2.WorkoutSummaryStats
import com.stt.android.social.workoutlist.AllWorkoutViewModel
import com.stt.android.social.workoutlist.ui.PhotoViewerScreen
import com.stt.android.social.workoutlist.ui.WorkoutListTab
import com.stt.android.utils.LocaleUtils

@Composable
internal fun UserProfileContent(
    user: User,
    isCurrentUser: Boolean,
    measurementUnit: MeasurementUnit,
    premiumSubscriptionStatus: CurrentPremiumSubscriptionStatus?,
    allWorkoutViewState: AllWorkoutViewModel.ViewData,
    followCountSummary: FollowCountStats?,
    userWorkoutSummaryState: WorkoutSummaryStats?,
    userFollowStatus: UserFollowStatus?,
    deviceTypeList: List<DeviceType>?,
    moreMenuList: List<MoreMenu>?,
    modifier: Modifier = Modifier,
    settingMenuList: List<SettingMenuInfo>? = null,
    onBackClick: () -> Unit = {},
    onEditClick: () -> Unit = {},
    onAllActivityClick: () -> Unit = {},
    onPersonalRecordsClick: () -> Unit = {},
    onFollowersClicked: (Int) -> Unit = {},
    onFollowingClicked: (Int) -> Unit = {},
    onFollowButtonClicked: () -> Unit = {},
    onMenuClick: (SettingMenuInfo) -> Unit = {},
    openWorkout: (WorkoutHeader) -> Unit = {},
    onMoreMenuItemClick: (MoreMenu) -> Unit = {},
    onPremiumSubscriptionClick: () -> Unit = {},
    onRetryClicked: () -> Unit = {},
    friend: Friend? = null,
    blocked: Boolean = false,
) {
    val lazyListState = rememberLazyListState()
    val context = LocalContext.current

    val location = remember(user) {
        if (!user.country.isNullOrBlank()) {
            val locale = LocaleUtils.fromCountryCode(user.country ?: "")
            val displayCountry = locale?.let { LocaleUtils.getDisplayCountry(it, context) }
            if (!user.city.isNullOrBlank()) {
                "$displayCountry ${user.city}"
            } else {
                displayCountry ?: ""
            }
        } else {
            ""
        }
    }

    var selectedPhotoIndex by remember { mutableStateOf<Int?>(null) }

    val isUserValid = user.username.isNotEmpty()

    val photos = (allWorkoutViewState as? AllWorkoutViewModel.ViewData.Loaded)?.images
        ?: emptyList()
    LaunchedEffect(photos) {
        if (photos.none()) {
            selectedPhotoIndex = null
        }
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .narrowContentWithBgColors(
                backgroundColor = MaterialTheme.colorScheme.surface,
                outerBackgroundColor = MaterialTheme.colorScheme.background
            )
    ) {
        ProfileHeader(
            user = user,
            location = location,
            scrollState = lazyListState,
            coverPhotoResId = if (isCurrentUser) {
                R.drawable.profile_cove_photo_default
            } else if (isUserValid) {
                R.drawable.profile_cove_photo_friend_default
            } else null,
            onBackClick = onBackClick,
            onEditClick = onEditClick,
            isEdit = isCurrentUser,
            moreMenuList = moreMenuList,
            onMoreMenuItemClick = onMoreMenuItemClick,
        ) { paddingValues ->
            LazyColumn(
                state = lazyListState,
                modifier = Modifier.fillMaxSize(),
                contentPadding = paddingValues,
            ) {
                item(key = "profile_info_${user.username}") {
                    ProfileInfo(
                        isCurrentUser = isCurrentUser,
                        username = user.realName ?: user.username,
                        description = user.description,
                        followersSummary = followCountSummary,
                        userFollowStatus = userFollowStatus,
                        deviceTypeList = deviceTypeList.takeIf { isUserValid },
                        onFollowersClicked = onFollowersClicked,
                        onFollowingClicked = onFollowingClicked,
                        onFollowButtonClicked = onFollowButtonClicked,
                        friend = friend,
                        blocked = blocked,
                    )
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
                }
                if (isCurrentUser) {
                    premiumSubscriptionStatus?.let {
                        item(key = "premium_subscription_${user.username}") {
                            PremiumSubscriptionView(
                                premiumSubscriptionStatus = premiumSubscriptionStatus,
                                onClick = onPremiumSubscriptionClick,
                                modifier = Modifier
                                    .padding(bottom = MaterialTheme.spacing.medium)
                                    .padding(horizontal = MaterialTheme.spacing.medium),
                            )
                        }
                    }
                    item(key = "activity_stats_${user.username}") {
                        ActivityStatsSection(
                            unit = measurementUnit,
                            workoutSummaryStats = userWorkoutSummaryState,
                            onActivityClick = onAllActivityClick
                        )
                    }
                    item(key = "achievements_${user.username}") {
                        AchievementsSection(
                            onPersonalRecordsClick = onPersonalRecordsClick,
                            modifier = Modifier.padding(top = MaterialTheme.spacing.medium),
                        )
                    }
                    item(key = "menu_section_${user.username}") {
                        if (settingMenuList?.isNotEmpty() == true) {
                            MenuSection(
                                settingMenuList = settingMenuList,
                                onClick = onMenuClick
                            )
                        } else {
                            Spacer(modifier = Modifier.height(1.dp))
                        }
                    }
                } else if (!blocked && isUserValid) {
                    if (userWorkoutSummaryState?.activityTypeStats?.isNotEmpty() == true) {
                        item(key = "top_activities_${user.username}") {
                            Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                            TopActivitiesSection(
                                activities = userWorkoutSummaryState.activityTypeStats,
                            )
                        }
                    }
                    item(key = "workout_list_${user.username}") {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = MaterialTheme.spacing.medium),
                        ) {
                            val nestedScrollConnection = remember {
                                object : NestedScrollConnection {
                                    override fun onPreScroll(
                                        available: Offset,
                                        source: NestedScrollSource
                                    ): Offset {
                                        val canScrollForward = lazyListState.canScrollForward
                                        if (canScrollForward) {
                                            lazyListState.dispatchRawDelta(-available.y)
                                            return available
                                        }
                                        return Offset.Zero
                                    }
                                }
                            }
                            WorkoutListTab(
                                viewState = allWorkoutViewState,
                                openWorkout = openWorkout,
                                workoutSummaryStats = userWorkoutSummaryState,
                                measurementUnit = measurementUnit,
                                nestedScrollConnection = nestedScrollConnection,
                                onPhotoClick = { index, photo ->
                                    selectedPhotoIndex = index
                                },
                                onRetryClicked = onRetryClicked
                            )
                        }
                    }
                } else if (blocked) {
                    item(key = "blocked_${user.username}") {
                        BlockedView(
                            modifier = Modifier.fillMaxWidth(),
                        )
                    }
                }
            }
        }
        selectedPhotoIndex?.let { index ->
            PhotoViewerScreen(
                photos = photos,
                initialPhotoIndex = index,
                modifier = Modifier.zIndex(100f),
                onBackClick = {
                    selectedPhotoIndex = null
                },
                onCheckActivities = {
                    if (it.hasWorkout()) {
                        openWorkout(
                            WorkoutHeader.builder()
                                .userName(it.username)
                                .id(it.workoutId)
                                .key(it.workoutKey)
                                .build()
                        )
                    }
                }
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun CurrentUserEmptyPreview() {
    M3AppTheme {
        UserProfileContent(
            user = User.ANONYMOUS,
            isCurrentUser = true,
            measurementUnit = MeasurementUnit.IMPERIAL,
            premiumSubscriptionStatus = null,
            allWorkoutViewState = AllWorkoutViewModel.ViewData.Loading,
            settingMenuList = listOf(
                SettingMenuInfo(
                    type = SettingMenuType.SETTING,
                    icon = R.drawable.ic_settings,
                    title = stringResource(R.string.settings)
                ),
            ),
            followCountSummary = null,
            userWorkoutSummaryState = null,
            userFollowStatus = null,
            deviceTypeList = null,
            moreMenuList = null
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun OthersEmptyPreview() {
    M3AppTheme {
        UserProfileContent(
            user = User.INVALID,
            isCurrentUser = false,
            measurementUnit = MeasurementUnit.IMPERIAL,
            premiumSubscriptionStatus = null,
            allWorkoutViewState = AllWorkoutViewModel.ViewData.Loading,
            followCountSummary = null,
            userWorkoutSummaryState = null,
            userFollowStatus = null,
            deviceTypeList = null,
            moreMenuList = null
        )
    }
}
