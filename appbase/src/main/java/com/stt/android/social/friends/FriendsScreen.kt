package com.stt.android.social.friends

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.compose.LifecycleStartEffect
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.stt.android.R
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.widgets.LoadingDialog
import com.stt.android.social.friends.discover.DiscoverFriendsContent
import com.stt.android.social.friends.followers.FollowersContent
import com.stt.android.social.friends.following.FollowingContent
import kotlinx.coroutines.launch

@Composable
fun FriendsScreen(
    viewModel: FriendsViewModel,
    onFriendClick: (Friend) -> Unit,
    onPhoneContactsClick: () -> Unit,
    onFacebookFriendsClick: () -> Unit,
    onInvitePeopleClick: () -> Unit,
    modifier: Modifier = Modifier,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
) {
    val friendsTabs = FriendsTab.entries.toTypedArray()
    val pagerState =
        rememberPagerState(initialPage = viewModel.currentPage) { friendsTabs.size }
    val facebookLoading by viewModel.facebookLoading.collectAsState()
    val coroutineScope = rememberCoroutineScope()
    val discoverFriendsState by viewModel.discoverFriendsStateFlow.collectAsState()
    val followingState by viewModel.followingStateFlow.collectAsState()
    val followersState by viewModel.followersStateFlow.collectAsState()
    LaunchedEffect(pagerState.currentPage) {
        viewModel.onCurrentPageChanged(pagerState.currentPage)
    }
    LifecycleStartEffect(key1 = lifecycleOwner) {
        viewModel.loadCurrentPageData()
        onStopOrDispose {}
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface),
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
        ) {
            ScrollableTabRow(
                selectedTabIndex = pagerState.currentPage,
                modifier = Modifier.height(48.dp),
                containerColor = Color.Transparent,
                contentColor = MaterialTheme.colorScheme.primary,
                edgePadding = 0.dp,
                divider = { },
            ) {
                friendsTabs.forEachIndexed { index, tab ->
                    Tab(
                        selected = pagerState.currentPage == index,
                        onClick = {
                            coroutineScope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        text = {
                            Text(
                                text = stringResource(tab.title),
                                style = MaterialTheme.typography.bodyLargeBold,
                            )
                        },
                        selectedContentColor = MaterialTheme.colorScheme.primary,
                        unselectedContentColor = MaterialTheme.colorScheme.secondary,
                    )
                }
            }

            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize(),
            ) { page ->
                when (friendsTabs[page]) {
                    FriendsTab.DISCOVER -> DiscoverFriendsContent(
                        state = discoverFriendsState,
                        onFriendClick = onFriendClick,
                        onStatusClick = viewModel::onStatusClick,
                        onPhoneContactsClick = onPhoneContactsClick,
                        onFacebookFriendsClick = onFacebookFriendsClick,
                        onInvitePeopleClick = onInvitePeopleClick,
                        modifier = Modifier.fillMaxSize(),
                    )

                    FriendsTab.FOLLOWING -> FollowingContent(
                        state = followingState,
                        onFriendClick = onFriendClick,
                        onStatusClick = viewModel::onStatusClick,
                        modifier = Modifier.fillMaxSize(),
                    )
                    FriendsTab.FOLLOWERS -> FollowersContent(
                        state = followersState,
                        onFriendClick = {
                            if (!viewModel.onFriendClick(it)) {
                                onFriendClick(it)
                            }
                        },
                        onStatusClick = viewModel::onStatusClick,
                        onEditClick = { viewModel.setEditingMode(true) },
                        modifier = Modifier.fillMaxSize(),
                    )
                }
            }
        }
        if (facebookLoading) {
            LoadingDialog(
                hintText = stringResource(R.string.loading_content),
                progressIndicatorColor = MaterialTheme.colorScheme.primary,
            )
        }
    }
}

enum class FriendsTab(
    @StringRes val title: Int,
) {
    DISCOVER(R.string.discover),
    FOLLOWING(R.string.following),
    FOLLOWERS(R.string.followers),
}
