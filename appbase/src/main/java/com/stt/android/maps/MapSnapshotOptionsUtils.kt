package com.stt.android.maps

import android.content.Context
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import com.google.android.gms.maps.model.LatLng
import com.stt.android.R
import com.stt.android.cardlist.PolylineType
import com.stt.android.domain.Point
import com.stt.android.domain.routes.WaypointTools
import com.stt.android.domain.user.MapTypes
import com.stt.android.home.explore.routes.RouteUtils.calculateWaypointHeading
import com.stt.android.maps.snapshotter.SuuntoMapSnapshotterOptions
import com.stt.android.core.R as CR

internal object MapSnapshotOptionsUtils {
    fun createMapSnapshotterOptions(
        spec: MapSnapshotSpec,
        provider: MapsProvider,
        mapView: SuuntoMapView?
    ): SuuntoMapSnapshotterOptions {
        val mapType = spec.explicitMapType ?: MapTypes.DEFAULT_MAP_TYPE
        val options = if (mapView != null) {
            SuuntoMapSnapshotterOptions(mapView)
        } else {
            SuuntoMapSnapshotterOptions(spec.width, spec.height)
        }

        return options
            .mapType(mapType.getBaseMapType(provider.name))
    }
}

internal fun SuuntoMapSnapshotterOptions.addRoute(
    points: List<LatLng>,
    polylineType: PolylineType,
    context: Context,
) = addRoute(
    points,
    getRouteColor(context, polylineType),
    context.resources.getDimension(R.dimen.route_map_stroke_width),
)

@ColorInt
private fun getRouteColor(
    context: Context,
    polylineType: PolylineType
) = ContextCompat.getColor(
    context,
    if (polylineType == PolylineType.ROUTE) {
        CR.color.route_primary
    } else {
        R.color.map_route
    }
)

internal fun SuuntoMapSnapshotterOptions.addRoute(
    points: List<LatLng>,
    @ColorRes colorRes: Int,
    context: Context,
) = addRoute(
    points,
    ContextCompat.getColor(context, colorRes),
    context.resources.getDimension(R.dimen.route_map_stroke_width),
)

internal fun SuuntoMapSnapshotterOptions.addColorfulRoute(
    points: List<LatLng>,
    @ColorRes colorRes: Int,
    context: Context,
) = addRoute(
    points,
    ContextCompat.getColor(context, colorRes),
    context.resources.getDimension(R.dimen.colorful_route_map_stroke_width),
)

private fun SuuntoMapSnapshotterOptions.addRoute(
    points: List<LatLng>,
    @ColorInt color: Int,
    routeStrokeWidth: Float,
) = polyline(
    SuuntoPolylineOptions(points)
        .color(color)
        .width(maxOf(routeStrokeWidth, MapSnapshotter.ROUTE_MIN_WIDTH_PIXELS))
)

internal fun SuuntoMapSnapshotterOptions.addPolyline(
    points: List<LatLng>,
    @ColorRes colorRes: Int,
    lineWidthPx: Float,
    context: Context
) {
    @ColorInt val color: Int = ContextCompat.getColor(context, colorRes)

    polyline(
        SuuntoPolylineOptions(points)
            .color(color)
            .width(lineWidthPx)
    )
}

internal fun SuuntoMapSnapshotterOptions.addPolyline(
    polylineOptions: List<SuuntoPolylineOptions>
) {
    polylineOptions.forEach { polyline(it) }
}

internal fun SuuntoMapSnapshotterOptions.addMarkers(
    markerOptions: List<SuuntoMarkerOptions>
) {
    markerOptions.forEach { marker(it) }
}

internal fun SuuntoMapSnapshotterOptions.addStartEndMarkers(
    startPoint: LatLng?,
    endPoint: LatLng?,
    polylineType: PolylineType,
    context: Context
) {
    val routeSize = when {
        startPoint == null -> return
        endPoint == null -> 1
        startPoint == endPoint -> 1
        else -> 2
    }

    val (startMarker, endMarker, startAnchorY) = when (polylineType) {
        PolylineType.WORKOUT -> {
            // Show a map pin for workouts that only contain a single geo-point in route.
            // Usually these would be indoor/dive workouts with manual/automatic location.
            Triple(
                if (routeSize == 1) R.drawable.map_pin else R.drawable.workout_starting_point_play,
                if (routeSize > 1) R.drawable.workout_ending_point_stop else null,
                if (routeSize == 1) 1.0f else 0.5f,
            )
        }

        PolylineType.ROUTE ->
            Triple(R.drawable.route_start_point_a, R.drawable.route_end_point_b, 0.5f)
    }
    // Handle end marker first so that start marker shows in case they overlap
    endMarker?.let {
        marker(
            SuuntoMarkerOptions()
                .position(requireNotNull(endPoint))
                .anchor(MapSnapshotter.MARKER_ANCHOR_X, MapSnapshotter.MARKER_ANCHOR_Y)
                .icon(SuuntoBitmapDescriptorFactory(context).fromResource(it))
        )
    }

    marker(
        SuuntoMarkerOptions()
            .position(startPoint)
            .anchor(MapSnapshotter.MARKER_ANCHOR_X, startAnchorY)
            .icon(SuuntoBitmapDescriptorFactory(context).fromResource(startMarker))
    )
}

internal fun SuuntoMapSnapshotterOptions.addWaypoints(
    route: List<Point>,
    showTurnByTurnWaypoints: Boolean,
    waypointTools: WaypointTools,
    context: Context
) {
    val rotations = mutableMapOf<Point, Float>()
    val (turnByTurnWaypoints, normalWaypoints) = route.mapIndexed { index, point ->
        val turnByTurnWaypointType = waypointTools.isTurnByTurnWaypointType(point.type)
        if (point.type != null && (showTurnByTurnWaypoints || !turnByTurnWaypointType)) {
            rotations[point] = calculateRotation(index, turnByTurnWaypointType, route)
            point
        } else {
            null
        }
    }
        .filterNotNull()
        .partition { point ->
            waypointTools.isTurnByTurnWaypointType(point.type)
        }

    // Draws normal waypoints on top of turn-by-turn waypoints
    (turnByTurnWaypoints + normalWaypoints).forEach { waypoint ->
        addWaypoint(
            waypoint = waypoint,
            iconScale = 0.5f,
            rotation = rotations[waypoint] ?: 0f,
            context = context,
            waypointTools = waypointTools
        )
    }
}

private fun calculateRotation(
    index: Int,
    turnByTurnWaypointType: Boolean,
    route: List<Point>
): Float {
    val rotation = if (index > 0 && turnByTurnWaypointType) {
        calculateWaypointHeading(route.subList(index - 1, index + 1))
    } else {
        0f
    }
    return rotation
}

private fun SuuntoMapSnapshotterOptions.addWaypoint(
    waypoint: Point,
    iconScale: Float = 1f,
    rotation: Float = 0f,
    context: Context,
    waypointTools: WaypointTools
) {
    waypoint.type?.let { type ->
        marker(
            SuuntoMarkerOptions()
                .position(LatLng(waypoint.latitude, waypoint.longitude))
                .anchor(MapSnapshotter.MARKER_ANCHOR_X, MapSnapshotter.MARKER_ANCHOR_Y)
                .rotation(rotation)
                .icon(
                    SuuntoBitmapDescriptorFactory(context).fromVectorDrawableResource(
                        waypointTools.getWaypointIconResId(type)
                    )
                )
                .iconScale(iconScale)
        )
    }
}
