package com.stt.android.ui.utils;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.content.res.AppCompatResources;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;
import com.stt.android.R;
import com.stt.android.ThemeColors;

/**
 * @deprecated Use {@link com.stt.android.common.ui.SimpleDialogFragment} instead which preserves
 * dialogs across configuration changes.
 */
@Deprecated
public class DialogHelper {
    public static void showDialog(Context context, @StringRes int message) {
        showDialog(context, 0, message, false, null, 0);
    }

    public static void showDialog(Context context, @StringRes int message,
        DialogInterface.OnClickListener onPositive) {
        showDialog(context, 0, message, false, onPositive, 0);
    }

    public static void showDialog(Context context, @StringRes int message, boolean cancelable,
        DialogInterface.OnClickListener onPositive) {
        showDialog(context, 0, message, cancelable, onPositive, 0);
    }

    public static void showDialog(Context context, @StringRes int title, @StringRes int message) {
        showDialog(context, title, message, false, null, 0);
    }

    public static void showDialog(Context context, @StringRes int title, @StringRes int message,
        @StringRes int positiveButtonText) {
        showDialog(context, title, message, false, null, positiveButtonText);
    }

    public static void showDialog(Context context, @StringRes int title, @StringRes int message,
        DialogInterface.OnClickListener onPositive) {
        showDialog(context, title, message, false, onPositive, 0);
    }

    public static void showDialog(Context context, @StringRes int title, @StringRes int message,
        boolean cancelable, DialogInterface.OnClickListener onPositive,
        @StringRes int positiveButtonText) {
        if (title == 0 && message == 0) {
            throw new IllegalArgumentException("Missing both title and message");
        }

        int uiMode = getConfigUiMode(context);

        AlertDialog.Builder builder = new AlertDialog.Builder(context)
            .setView(R.layout.custom_alert_dialog)
            .setCancelable(cancelable)
            .setPositiveButton(positiveButtonText != 0 ? positiveButtonText : R.string.ok,
                onPositive);
        if (title != 0) {
            builder.setTitle(title);
        }
        if (message != 0) {
            builder.setMessage(message);
        }

        try {
            builder.show();
        } catch (Exception ignored) {
        }

        restoreConfigUiMode(context, uiMode);
    }

    public static void showDialog(Context context, CharSequence message) {
        showDialog(context, 0, message, null);
    }

    public static void showDialog(Context context, CharSequence message,
        DialogInterface.OnClickListener onPositive) {
        showDialog(context, 0, message, onPositive);
    }

    public static void showDialog(Context context, @StringRes int title, CharSequence message) {
        showDialog(context, title, message, null);
    }

    public static void showDialog(Context context, @StringRes int title, CharSequence message,
        DialogInterface.OnClickListener onPositive) {
        if (title == 0 && TextUtils.isEmpty(message)) {
            throw new IllegalArgumentException("Missing both title and message");
        }

        int uiMode = getConfigUiMode(context);

        AlertDialog.Builder builder = new AlertDialog.Builder(context).setCancelable(false)
            .setPositiveButton(R.string.ok, onPositive);
        if (title != 0) {
            builder.setTitle(title);
        }
        if (!TextUtils.isEmpty(message)) {
            builder.setMessage(message);
        }

        try {
            builder.show();
        } catch (Exception ignored) {
        }

        restoreConfigUiMode(context, uiMode);
    }

    public static void showDialog(Context context, @StringRes int message,
        DialogInterface.OnClickListener onPositive, DialogInterface.OnClickListener onNegative) {
        showDialog(context, message, R.string.yes, onPositive, R.string.no, onNegative);
    }

    public static void showDialog(Context context, @StringRes int message, @StringRes int positive,
        DialogInterface.OnClickListener onPositive, @StringRes int negative,
        DialogInterface.OnClickListener onNegative) {

        int uiMode = getConfigUiMode(context);

        new AlertDialog.Builder(context).setMessage(message)
            .setPositiveButton(positive, onPositive)
            .setNegativeButton(negative, onNegative)
            .show();

        restoreConfigUiMode(context, uiMode);
    }

    public static void showDialog(Context context, @StringRes int title, @StringRes int message,
        DialogInterface.OnClickListener onPositive, DialogInterface.OnClickListener onNegative) {
        showDialog(context, true, title, message, R.string.ok, onPositive, R.string.cancel,
            onNegative);
    }

    public static void showDialog(Context context, boolean cancelable, @StringRes int title,
        @StringRes int message, @StringRes int positive, DialogInterface.OnClickListener onPositive,
        @StringRes int negative, DialogInterface.OnClickListener onNegative) {

        int uiMode = getConfigUiMode(context);

        AlertDialog.Builder builder = new AlertDialog.Builder(context).setCancelable(cancelable);
        if (title != 0) {
            builder.setTitle(title);
        }
        if (message != 0) {
            builder.setMessage(message);
        }
        builder.setPositiveButton(positive, onPositive);
        builder.setNegativeButton(negative, onNegative);
        builder.show();

        restoreConfigUiMode(context, uiMode);
    }

    public static AlertDialog createDialog(Context context, @StringRes int title,
        @StringRes int message, View view, @StringRes int negativeButtonText,
        DialogInterface.OnClickListener onNegativeClick) {
        int uiMode = getConfigUiMode(context);

        AlertDialog dialog = new AlertDialog.Builder(context).setTitle(title)
            .setMessage(message)
            .setView(view)
            .setNegativeButton(negativeButtonText, onNegativeClick)
            .create();

        restoreConfigUiMode(context, uiMode);

        return dialog;
    }

    public static Dialog createDialog(Context context, @StringRes int title, @StringRes int message,
        @StringRes int positive, DialogInterface.OnClickListener onPositive,
        @StringRes int negative, DialogInterface.OnClickListener onNegative,
        DialogInterface.OnCancelListener onCancel) {
        int uiMode = getConfigUiMode(context);

        AlertDialog.Builder builder = new AlertDialog.Builder(context).setCancelable(true);
        if (title != 0) {
            builder.setTitle(title);
        }
        if (message != 0) {
            builder.setMessage(message);
        }
        if (positive != 0) {
            builder.setPositiveButton(positive, onPositive);
        }
        if (negative != 0) {
            builder.setNegativeButton(negative, onNegative);
        }
        builder.setOnCancelListener(onCancel);
        Dialog dialg = builder.create();

        restoreConfigUiMode(context, uiMode);

        return dialg;
    }

    public static void showDialog(Context context, @StringRes int title, CharSequence[] items,
        @Nullable int[] icons, int selected, DialogInterface.OnClickListener onClicked) {
        int uiMode = getConfigUiMode(context);

        new AlertDialog.Builder(context).setTitle(title)
            .setCancelable(true)
            .setSingleChoiceItems(new SingleChoiceListAdapter(context, items, icons, selected),
                selected, onClicked)
            .show();

        restoreConfigUiMode(context, uiMode);
    }

    public interface OnMultiChoiceClickListener {
        public void onItemClicked(int which, boolean[] selected);

        public void onDismissed(boolean[] selected);
    }

    public static void showMultiSelectionDialog(Context context, @StringRes int title,
        CharSequence[] items, int[] icons, boolean[] selected,
        final OnMultiChoiceClickListener onMultiChoiceClicked) {
        int uiMode = getConfigUiMode(context);

        final MultiChoiceListAdapter adapter =
            new MultiChoiceListAdapter(context, items, icons, selected);
        new AlertDialog.Builder(context).setTitle(title)
            .setCancelable(true)
            .setSingleChoiceItems(adapter, 0, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    adapter.onClicked(which);
                    if (onMultiChoiceClicked != null) {
                        onMultiChoiceClicked.onItemClicked(which, adapter.getSelected());
                    }
                    adapter.notifyDataSetChanged();
                }
            })
            .setPositiveButton(R.string.ok, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    if (onMultiChoiceClicked != null) {
                        onMultiChoiceClicked.onDismissed(adapter.getSelected());
                    }
                }
            })
            .show();

        restoreConfigUiMode(context, uiMode);
    }

    /**
        These two methods for getting and restoring the configuration uiMode
        are used to work around some bugs in the Android Support Library
        night mode. Simply constructing an AppCompatDialog can in some cases remove
        the night-modifier from the resources, so always get and restore the
        state when a dialog is created.
        See TP tickets #88583, #92262 and others related to them.
     */
    private static int getConfigUiMode(Context context) {
        return context.getResources().getConfiguration().uiMode;
    }

    private static void restoreConfigUiMode(Context context, int uiMode) {
        Resources resources = context.getResources();
        Configuration config = resources.getConfiguration();
        config.uiMode = uiMode;
        resources.updateConfiguration(config, resources.getDisplayMetrics());
    }

    static class SingleChoiceListAdapter extends BaseAdapter {
        private final LayoutInflater inflater;
        private final CharSequence[] texts;
        private final int[] icons;
        private final int selected;

        SingleChoiceListAdapter(Context context, CharSequence[] texts, int[] icons, int selected) {
            inflater = LayoutInflater.from(context);
            this.texts = texts;
            this.icons = icons;
            this.selected = selected;
        }

        @Override
        public int getCount() {
            return texts == null ? 0 : texts.length;
        }

        @Override
        public CharSequence getItem(int position) {
            return texts[position];
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            TextView root;
            if (convertView == null) {
                root = (TextView) inflater.inflate(R.layout.dialog_selection_item, parent, false);
            } else {
                root = (TextView) convertView;
            }

            root.setText(texts[position]);

            Context context = root.getContext();
            Drawable leftDrawable =
                icons != null ? AppCompatResources.getDrawable(context, icons[position]) : null;
            if (leftDrawable != null) {
                leftDrawable.setTint(ThemeColors.resolveColor(context, android.R.attr.colorForeground));
            }
            Drawable rightDrawable = position == selected ? AppCompatResources.getDrawable(context,
                R.drawable.icon_checkmark) : null;
            root.setCompoundDrawablesWithIntrinsicBounds(leftDrawable, null, rightDrawable, null);

            return root;
        }
    }

    static class MultiChoiceListAdapter extends BaseAdapter {
        private final LayoutInflater inflater;
        private final CharSequence[] texts;
        private final int[] icons;
        private final boolean[] selected;

        MultiChoiceListAdapter(Context context, CharSequence[] texts, int[] icons,
            boolean[] selected) {
            inflater = LayoutInflater.from(context);
            this.texts = texts;
            this.icons = icons;
            this.selected = selected;
        }

        @Override
        public int getCount() {
            return texts == null ? 0 : texts.length;
        }

        @Override
        public CharSequence getItem(int position) {
            return texts[position];
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            TextView root;
            if (convertView == null) {
                root = (TextView) inflater.inflate(R.layout.dialog_selection_item, parent, false);
            } else {
                root = (TextView) convertView;
            }

            root.setText(texts[position]);

            Context context = root.getContext();
            int iconId = icons != null ? icons[position] : 0;
            Drawable leftDrawable =
                iconId != 0 ? AppCompatResources.getDrawable(context, iconId) : null;
            if (leftDrawable != null) {
                leftDrawable.setTint(ThemeColors.resolveColor(context, android.R.attr.colorForeground));
            }
            Drawable rightDrawable = AppCompatResources.getDrawable(context,
                selected[position] ? R.drawable.ic_check_box : R.drawable.ic_check_box_outline);
            root.setCompoundDrawablesWithIntrinsicBounds(leftDrawable, null, rightDrawable, null);

            return root;
        }

        void onClicked(int position) {
            selected[position] = !selected[position];
        }

        boolean[] getSelected() {
            return selected;
        }
    }
}
