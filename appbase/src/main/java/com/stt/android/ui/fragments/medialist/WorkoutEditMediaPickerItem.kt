package com.stt.android.ui.fragments.medialist

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import coil3.load
import coil3.request.error
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.CallbackProp
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.stt.android.R
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.databinding.ItemMediaPickerBinding
import com.stt.android.domain.review.ReviewState
import com.stt.android.multimedia.picker.MediaInfoForPicker
import com.stt.android.multimedia.picker.PickedMediaInfoForPicker
import com.stt.android.multimedia.picker.PictureInfoForPicker
import com.stt.android.multimedia.picker.VideoInfoForPicker
import com.stt.android.multimedia.picker.getThumbnailUri

@ModelView(autoLayout = ModelView.Size.WRAP_WIDTH_MATCH_HEIGHT)
class WorkoutEditMediaPickerItem
@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    @set:[ModelProp]
    var mediaInfoForPicker: MediaInfoForPicker? = null

    @set:[ModelProp]
    var showSelectionIcon = false

    @set:[ModelProp]
    var itemSelected = false

    @set:[ModelProp]
    var showRemoveIcon = false

    @set:[ModelProp]
    var enableRightPadding = false

    @set:[CallbackProp]
    var onClicked: OnClickListener? = null

    @set:[CallbackProp]
    var onRemoveClicked: OnClickListener? = null

    val binding = ItemMediaPickerBinding.inflate(LayoutInflater.from(context), this)

    private val rightPadding = resources.getDimensionPixelOffset(R.dimen.size_spacing_small)
    private val imageHeight =
        resources.getDimensionPixelSize(R.dimen.height_activity_save_imagepicker)

    @AfterPropsSet
    fun setup() = with(binding) {
        val imageWidth = imageHeight // Always use 1:1 aspect ratio

        val thumbnailUri = mediaInfoForPicker?.getThumbnailUri(context)
        thumbnail.scaleType = ImageView.ScaleType.CENTER_CROP
        thumbnail.load(thumbnailUri) {
            placeholderWithFallback(thumbnail.context, R.drawable.media_grid_cell_placeholder)
            error(R.drawable.media_grid_broken_item)
            size(imageWidth, imageHeight)
        }

        if (showRemoveIcon) {
            removalIndication.visibility = VISIBLE
            removalIndication.setOnClickListener(onRemoveClicked)
        } else {
            removalIndication.visibility = GONE
            removalIndication.setOnClickListener(null)
        }

        if (showSelectionIcon) {
            touchArea.setOnClickListener(onClicked)
            selectionIndication.visibility = VISIBLE
            selectionIndication.setImageResource(
                if (itemSelected) {
                    R.drawable.ic_selected_green
                } else {
                    R.drawable.ic_circle_outline
                }
            )
        } else {
            touchArea.setOnClickListener(null)
            touchArea.isClickable = false
            selectionIndication.visibility = GONE
        }

        root.setPadding(0, 0, if (enableRightPadding) rightPadding else 0, 0)

        val isVideo = mediaInfoForPicker?.isVideo ?: false

        play.visibility = if (isVideo) View.VISIBLE else View.GONE
        contentReviewState.visibility = if (illegalMedia()) View.VISIBLE else View.GONE
    }

    /**
     * the image or video default state is legal
     */
    private fun illegalMedia(): Boolean {
        return when (val picker = mediaInfoForPicker) {
            is PictureInfoForPicker -> {
                picker.image.reviewState == ReviewState.FAIL
            }

            is VideoInfoForPicker -> {
                picker.video.reviewState == ReviewState.FAIL
            }
            is PickedMediaInfoForPicker,
            null -> false
        }
    }
}
