package com.stt.android.ui.components

import android.annotation.SuppressLint
import android.app.Dialog
import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.setFragmentResult
import com.stt.android.R

class IntegerValueDialogFragment : DialogFragment(), InlineEditor.InlineEditorActionListener<Int> {

    private var selectedListener: IntegerValueSelectedListener? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        selectedListener = (activity as? IntegerValueSelectedListener)
    }

    @SuppressLint("InflateParams")
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val view = layoutInflater.inflate(R.layout.dialog_workout_value, null)
        val integerEditor = view.findViewById<GenericIntegerEditor>(R.id.genericIntEditor)
        var title = ""

        arguments?.apply {
            getString(TITLE)?.let { title = it }
            integerEditor.setMinMax(getInt(MIN_VALUE), getInt(MAX_VALUE))
            integerEditor.value = getInt(INITIALVALUE)
        }

        integerEditor.setInlineEditorActionListener(this)

        return AlertDialog.Builder(requireContext())
            .setPositiveButton(R.string.ok) { _, _ ->
                onDialogOkClicked(integerEditor.value)
            }
            .setNegativeButton(R.string.cancel, null)
            .setView(view)
            .setTitle(title)
            .create()
    }

    private fun onDialogOkClicked(value: Int?) {
        if (value != null) {
            // selectedListener in case this Dialog called from an activity
            selectedListener?.valueUpdatedFromDialog(tag, value)
            tag?.let {
                setFragmentResult(it, bundleOf(it to value))
            }
            dismiss()
        }
    }

    interface IntegerValueSelectedListener {
        fun valueUpdatedFromDialog(tag: String?, value: Int)
    }

    companion object {
        private const val TITLE = "title"
        private const val MIN_VALUE = "minValue"
        private const val MAX_VALUE = "maxValue"
        private const val INITIALVALUE = "initialvalue"

        @JvmStatic
        @JvmOverloads
        fun newInstance(
            title: String? = null,
            workoutValueType: WorkoutDialogValueType,
            initialValue: Int
        ): IntegerValueDialogFragment {
            return newInstance(
                title,
                workoutValueType.min,
                workoutValueType.max,
                initialValue
            )
        }

        @JvmStatic
        @JvmOverloads
        fun newInstance(
            title: String? = null,
            minValue: Int,
            maxValue: Int,
            initialValue: Int
        ): IntegerValueDialogFragment {
            val dialogFragment = IntegerValueDialogFragment()
            dialogFragment.arguments = Bundle().apply {
                putString(TITLE, title)
                putInt(MIN_VALUE, minValue)
                putInt(MAX_VALUE, maxValue)
                putInt(INITIALVALUE, initialValue)
            }
            return dialogFragment
        }
    }

    override fun onInlineEditorDoneClicked(value: Int?) {
        onDialogOkClicked(value)
    }
}
