@file:Suppress("DEPRECATION")

package com.stt.android.ui.fragments.login.terms

import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.R
import com.stt.android.ui.tasks.LogoutTask
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.disposables.CompositeDisposable
import timber.log.Timber
import javax.inject.Inject

// TODO get rid of this
@Deprecated("to be removed")
@AndroidEntryPoint
class TermsUpdatedFragment : LegacyBaseTermsFragment() {

    @Inject
    lateinit var logoutTask: dagger.Lazy<LogoutTask>

    private val disposable = CompositeDisposable()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (activity as AppCompatActivity).supportActionBar?.setDisplayHomeAsUpEnabled(false)
        setHasOptionsMenu(true)
        webView?.loadUrl(getString(R.string.terms_updated_url))
        termsPresenter.trackTermsScreen(false)
    }

    override fun onAgreeClick() {
        termsPresenter.trackAcceptTermsEvent(false)
        termsPresenter.acceptTerms(true)
    }

    @Deprecated("Deprecated in Java")
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        inflater.inflate(R.menu.terms, menu)
    }

    @Deprecated("Deprecated in Java")
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        termsPresenter.signOut()
        return super.onOptionsItemSelected(item)
    }

    override fun onTermsAccepted() {
        termsListener.onProceed()
    }

    override fun onSignOut() {
        onLogoutClicked()
    }

    private fun onLogoutClicked() {
        val dialogTitle = getString(
            R.string.dialog_title_settings_service_sign_out,
            currentUserController.username,
        )
        context?.let {
            AlertDialog.Builder(it)
                .setCancelable(true)
                .setTitle(dialogTitle)
                .setMessage(R.string.dialog_message_settings_service_sign_out)
                .setPositiveButton(R.string.positive_button_settings_service_sign_out) { _, _ -> handleLogoutConfirm() }
                .setNegativeButton(R.string.negative_button_settings_service_sign_out, null)
                .show()
        }
    }

    private fun handleLogoutConfirm() {
        disposable.add(
            logoutTask.get().logoutWithProgressDialog(requireContext(), childFragmentManager)
                .subscribe(
                    { Timber.d("Logout succeeded") },
                    { Timber.w(it, "Logout failed") }
                )
        )
    }

    companion object {
        const val FRAGMENT_TAG = "com.stt.android.ui.fragments.login.terms.TermsUpdatedFragment"

        fun newInstance(): TermsUpdatedFragment {
            return TermsUpdatedFragment()
        }
    }
}
