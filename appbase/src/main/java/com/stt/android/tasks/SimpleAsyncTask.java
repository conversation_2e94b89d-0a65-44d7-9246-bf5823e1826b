package com.stt.android.tasks;

import android.os.AsyncTask;

import java.util.concurrent.RejectedExecutionException;

import timber.log.Timber;

public abstract class SimpleAsyncTask<Params, Progress, Result> extends AsyncTask<Params,
        Progress, Result> {
    public void start(Params... params) {
        if (getStatus() != Status.PENDING) {
            // currently running or already executed, do nothing
            return;
        }

        try {
            executeOnExecutor(THREAD_POOL_EXECUTOR, params);
        } catch (RejectedExecutionException e) {
            Timber.e(e, "Unable to add async task to thread pool executor");
        }
    }
}
