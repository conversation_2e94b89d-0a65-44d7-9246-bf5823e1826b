package com.stt.android.home.diary;

import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.database.DataSetObserver;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.View.OnCreateContextMenuListener;
import android.view.ViewGroup;
import android.widget.ExpandableListView;
import android.widget.ExpandableListView.ExpandableListContextMenuInfo;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.lifecycle.ViewModelProvider;
import com.stt.android.R;
import com.stt.android.analytics.AmplitudeAnalyticsTracker;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.analytics.AnalyticsEventProperty;
import com.stt.android.analytics.AnalyticsProperties;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.WorkoutHeaderController;
import com.stt.android.controllers.WorkoutHeaderController.WorkoutUpdate;
import com.stt.android.databinding.DiaryListFragmentBinding;
import com.stt.android.databinding.ViewholderViewTotalsBinding;
import com.stt.android.databinding.WorkoutTotalsViewBinding;
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator;
import com.stt.android.domain.summaries.SummaryHighlightedProperty;
import com.stt.android.domain.summaries.SummaryTimeFrameUnit;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.home.dashboard.startworkout.StartWorkoutPresenter;
import com.stt.android.home.workouts.DiaryTotalsActivity;
import com.stt.android.session.SignInFlowHook;
import com.stt.android.ui.activities.WorkoutEditDetailsActivity;
import com.stt.android.ui.adapters.ExpandableWorkoutListAdapter;
import com.stt.android.ui.components.workout.WorkoutCardViewModel;
import com.stt.android.ui.utils.TextFormatter;
import com.stt.android.ui.utils.ThrottlingOnClickListener;
import com.stt.android.utils.STTConstants;
import com.stt.android.utils.WindowsSubsystemForAndroidUtils;
import com.stt.android.workouts.remove.RemoveWorkoutService;
import dagger.hilt.android.AndroidEntryPoint;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.inject.Inject;
import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;
import timber.log.Timber;

@AndroidEntryPoint
public class DiaryWorkoutListFragment extends FilterableExpandableListFragment {
    public static final String FRAGMENT_TAG =
        "com.stt.android.ui.fragments.DiaryWorkoutListFragment.FRAGMENT_TAG";

    public static final String ARG_WORKOUT_IDS = "WORKOUT_IDS";
    public static final String ARG_HIDE_GROUP_HEADER = "HIDE_GROUP_HEADER";
    public static final String ARG_ANALYTICS_VIEW_ID = "ANALYTICS_VIEW_ID";
    public static final String ARG_SUB_VIEW = "SUB_VIEW";

    @NonNull
    public static DiaryWorkoutListFragment newInstance() {
        return newInstance(false, null, false);
    }

    public static DiaryWorkoutListFragment newInstanceWithWorkoutIds(
        ArrayList<Integer> workoutIds,
        boolean hideGroupHeader,
        String analyticsViewId,
        boolean isSubView) {
        DiaryWorkoutListFragment fragment =
            newInstance(hideGroupHeader, analyticsViewId, isSubView);
        fragment.getArguments().putIntegerArrayList(ARG_WORKOUT_IDS, workoutIds);
        return fragment;
    }

    public static DiaryWorkoutListFragment newInstance(boolean hideGroupHeader, String
        analyticsViewId, boolean isSubView) {
        DiaryWorkoutListFragment fragment = new DiaryWorkoutListFragment();

        Bundle args = new Bundle();
        args.putBoolean(ARG_HIDE_GROUP_HEADER, hideGroupHeader);
        args.putString(ARG_ANALYTICS_VIEW_ID, analyticsViewId);
        args.putBoolean(ARG_SUB_VIEW, isSubView);
        fragment.setArguments(args);

        return fragment;
    }

    private WorkoutTotalsViewBinding totalsViewBinding;
    private ViewholderViewTotalsBinding viewTotalsBinding;
    private DiaryListFragmentBinding binding;

    @Nullable
    private Subscription updatedWorkoutSubscription;

    private static final int DELETE_ACTION = 1;

    @Inject
    CurrentUserController currentUserController;

    @Inject
    WorkoutHeaderController workoutHeaderController;

    @Inject
    StartWorkoutPresenter startWorkoutPresenter;

    @Inject
    SignInFlowHook signInFlowHook;

    @Inject
    WorkoutDetailsRewriteNavigator rewriteNavigator;

    @Inject
    AmplitudeAnalyticsTracker amplitudeAnalyticsTracker;

    @Inject
    SharedPreferences defaultSharedPreferences;

    private WorkoutCardViewModel workoutCardViewModel;
    private Subscription loadWorkoutsSubscription;
    private Subscription addPhotosClicksSubscription;
    private boolean deleteDialogIsOpened = false;
    private AlertDialog alertDialog = null;
    private int groupPos = 0;
    private int childPos = 0;
    private final OnCreateContextMenuListener workoutContextMenuListener =
        (menu, v, menuInfo) -> {
            if (!(menuInfo instanceof ExpandableListContextMenuInfo)) {
                // skip it if it's a context menu for an unknown type (e.g. list header view)
                return;
            }
            ExpandableListContextMenuInfo info = (ExpandableListContextMenuInfo) menuInfo;

            int type = ExpandableListView.getPackedPositionType(info.packedPosition);
            // Only create a context menu for child items
            if (type == ExpandableListView.PACKED_POSITION_TYPE_CHILD) {
                int group = ExpandableListView.getPackedPositionGroup(info.packedPosition);
                int child = ExpandableListView.getPackedPositionChild(info.packedPosition);
                WorkoutHeader workoutHeader =
                    (WorkoutHeader) getListAdapter().getChild(group, child);

                String title =
                    TextFormatter.formatDateTime(getActivity(), workoutHeader.getStartTime());
                menu.setHeaderTitle(title);
                menu.add(Menu.NONE, DELETE_ACTION, 0, R.string.delete);
            }
        };

    @Nullable
    private ArrayList<Integer> workoutIds;

    boolean hideGroupHeader;

    @Nullable
    private String analyticsViewId;

    private boolean isSubView;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Bundle bundle = savedInstanceState != null ? savedInstanceState : getArguments();
        if (bundle != null) {
            workoutIds = bundle.getIntegerArrayList(ARG_WORKOUT_IDS);
            hideGroupHeader = bundle.getBoolean(ARG_HIDE_GROUP_HEADER, false);
            analyticsViewId = bundle.getString(ARG_ANALYTICS_VIEW_ID);
            isSubView = bundle.getBoolean(ARG_SUB_VIEW);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
        Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);

        totalsViewBinding = WorkoutTotalsViewBinding.inflate(inflater);
        totalsViewBinding.getRoot().setVisibility(View.GONE);

        viewTotalsBinding = ViewholderViewTotalsBinding.inflate(inflater);
        viewTotalsBinding.getRoot().setVisibility(View.GONE);

        binding = DiaryListFragmentBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
        totalsViewBinding = null;
        viewTotalsBinding = null;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        workoutCardViewModel = new ViewModelProvider(this).get(WorkoutCardViewModel.class);

        binding.startWorkout.setPresenter(startWorkoutPresenter);
        binding.startWorkout.setAnalyticsSourceView(AnalyticsEventProperty.EMPTY_WORKOUT_LIST);

        boolean isWsa = WindowsSubsystemForAndroidUtils.isWindowsSubsystemForAndroid;
        binding.noWorkoutTitle.setText(
            isWsa ? R.string.no_activities_no_tracking : R.string.no_activities);
        binding.connectText.setText(
            isWsa ? R.string.connect_to_sync_no_tracking : R.string.connect_to_sync);

        setUpAdapter();
        setUpExpandableListView();

        totalsViewBinding.distanceUnit.setText(
            userSettingsController.getSettings().getMeasurementUnit().getDistanceUnit());
        ExpandableListView expandableListView = getExpandableListView();

        expandableListView.addHeaderView(totalsViewBinding.getRoot());
        expandableListView.addHeaderView(viewTotalsBinding.getRoot());

        updatedWorkoutSubscription =
            workoutHeaderController.getCurrentUserWorkoutUpdatesAsObservable()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(workoutUpdate -> handleWorkoutUpdate(workoutUpdate),
                    throwable -> Timber.w(throwable, "Unable to process workout update"));

        viewTotalsBinding.setOnClicked(new ThrottlingOnClickListener(v -> {
                requireContext().startActivity(
                    DiaryTotalsActivity.Companion.newStartIntent(requireContext())
                );
                trackTotalsScreenAnalytics();
            })
        );

        loadWorkouts();
    }

    void handleWorkoutUpdate(WorkoutUpdate workoutUpdate) {
        // FIXME: Why do we need to check if is added?
        if (!isAdded()) {
            return;
        }

        WorkoutHeader updatedWorkoutHeader = workoutUpdate.workoutHeader;
        switch (workoutUpdate.operation) {
            case WorkoutUpdate.DELETED:
                updateDeletedWorkout(updatedWorkoutHeader.getId());
                break;
            case WorkoutUpdate.UPDATED:
                updateWorkout(updatedWorkoutHeader.getId(), updatedWorkoutHeader);
                break;
            case WorkoutUpdate.CREATED:
                updateWorkout(updatedWorkoutHeader.getId(), updatedWorkoutHeader);
                break;
            case WorkoutUpdate.SYNCED:
                updateWorkout(workoutUpdate.nonSyncedWorkoutInternalId, updatedWorkoutHeader);
                break;
        }
    }

    @Override
    public void onStart() {
        super.onStart();

        binding.startWorkout.onStart();
    }

    @Override
    public void onStop() {
        super.onStop();
        binding.startWorkout.onStop();
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);

        // Trying to store large list of headers in saved state can cause TransactionTooLargeExceptions
        if (workoutIds != null) {
            outState.putIntegerArrayList(ARG_WORKOUT_IDS, workoutIds);
        }
        outState.putBoolean(ARG_HIDE_GROUP_HEADER, hideGroupHeader);
        if (analyticsViewId != null) {
            outState.putString(ARG_ANALYTICS_VIEW_ID, analyticsViewId);
        }
    }

    @Override
    public void onDestroy() {
        unsubscribeLoadWorkoutSubscription();

        if (updatedWorkoutSubscription != null) {
            updatedWorkoutSubscription.unsubscribe();
        }
        if (addPhotosClicksSubscription != null) {
            addPhotosClicksSubscription.unsubscribe();
        }

        super.onDestroy();
    }

    private void unsubscribeLoadWorkoutSubscription() {
        if (loadWorkoutsSubscription != null) {
            loadWorkoutsSubscription.unsubscribe();
            loadWorkoutsSubscription = null;
        }
    }

    void loadWorkouts() {
        unsubscribeLoadWorkoutSubscription();

        Observable<List<WorkoutHeader>> observable;
        if (workoutIds != null) {
            observable = workoutHeaderController.find(workoutIds);
        } else {
            observable = workoutHeaderController.loadWorkouts(currentUserController.getUsername());
        }
        loadWorkoutsSubscription = observable.subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(new Subscriber<List<WorkoutHeader>>() {
                @Override
                public void onCompleted() {
                    // do nothing
                }

                @Override
                public void onError(Throwable throwable) {
                    showConnectViews();
                }

                @Override
                public void onNext(List<WorkoutHeader> workoutHeaders) {
                    onWorkoutsLoaded(workoutHeaders);
                }
            });
    }

    private void setUpAdapter() {
        // Set up our adapter
        ExpandableWorkoutListAdapter adapter = new ExpandableWorkoutListAdapter(
            getActivity(), hideGroupHeader, workoutCardViewModel, rewriteNavigator);
        setListAdapter(adapter);
        // Every time the data set is changed we want to open the top group (useful when
        // filtering and adding workouts)
        adapter.registerDataSetObserver(new DataSetObserver() {
            @Override
            public void onChanged() {
                if (binding == null) {
                    return;
                }

                ExpandableWorkoutListAdapter adapter =
                    (ExpandableWorkoutListAdapter) getListAdapter();
                if (hideGroupHeader) {
                    // Expand all groups if headers are hidden
                    for (int i = 0; i < adapter.getGroupCount(); i++) {
                        getExpandableListView().expandGroup(i);
                    }
                } else {
                    // Otherwise expand only the first group if there are no expanded groups already
                    if (adapter.getExpandedGroups().isEmpty()
                        && getListAdapter().getGroupCount() > 0) {
                        getExpandableListView().expandGroup(0);
                    }
                }

                // sets totals
                List<WorkoutHeader> workouts = adapter.getFilteredWorkouts();
                int workoutCount = workouts.size();
                totalsViewBinding.totalWorkouts.setText(Integer.toString(workoutCount));
                double totalTime = 0.0;
                double totalDistance = 0.0;
                double totalEnergy = 0.0;
                for (int i = 0; i < workoutCount; ++i) {
                    WorkoutHeader workoutHeader = workouts.get(i);
                    totalTime += workoutHeader.getTotalTime();
                    totalDistance += workoutHeader.getTotalDistance();
                    totalEnergy += workoutHeader.getEnergyConsumption();
                }
                totalsViewBinding.totalTime.setText(
                    Long.toString(Math.round(totalTime / 3600.0)));
                totalsViewBinding.totalDistance.setText(Long.toString(Math.round(
                    userSettingsController.getSettings()
                        .getMeasurementUnit()
                        .toDistanceUnit(totalDistance))));
                totalsViewBinding.totalEnergy.setText(
                    Long.toString(Math.round(totalEnergy)));
            }
        });
        adapter.notifyDataSetChanged();
        // Reset the search filter if the list has changed. It will trigger a perform filter.
        resetFilter();

        if (addPhotosClicksSubscription != null) {
            addPhotosClicksSubscription.unsubscribe();
        }
        addPhotosClicksSubscription = adapter.getAddPhotosClicks()
            .subscribe(workoutHeader -> {
                Intent intent = WorkoutEditDetailsActivity.newStartIntentForEditing(
                    getActivity(),
                    workoutHeader.getId(),
                    AnalyticsPropertyValue.SourceProperty.WORKOUT_CARD_EDIT_BUTTON_IN_DIARY
                );
                startActivity(intent);
            }, throwable -> {
                // Ignore, should never signal error.
            });
    }

    private void setUpExpandableListView() {
        ExpandableListView expandableView = getExpandableListView();
        expandableView.setOnCreateContextMenuListener(workoutContextMenuListener);
    }

    void onWorkoutsLoaded(List<WorkoutHeader> workouts) {
        if (!isAdded() || binding == null) {
            return;
        }

        if (workouts == null || workouts.isEmpty()) {
            showConnectViews();
        } else {
            ExpandableWorkoutListAdapter listAdapter =
                (ExpandableWorkoutListAdapter) getListAdapter();
            int totalWorkouts = workouts.size();
            // If there wasn't any list before or the number of workouts has
            // changed update the list otherwise do nothing
            int currentTotalWorkouts =
                listAdapter == null ? -1 : listAdapter.getTotalOriginalValues();
            Timber.d("Read %d workouts from DB and adapter already has %d", totalWorkouts,
                currentTotalWorkouts);
            if (currentTotalWorkouts != totalWorkouts) {
                ((ExpandableWorkoutListAdapter) getExpandableListAdapter()).setWorkouts(workouts);
                // Make sure we hide the connect views
                hideConnectViews();
            }
        }
    }

    protected void showConnectViews() {
        filterView.setVisibility(View.GONE);
        totalsViewBinding.getRoot().setVisibility(View.GONE);
        binding.empty.setVisibility(View.GONE);
        binding.noWorkoutSection.setVisibility(View.VISIBLE);
        if (!currentUserController.isLoggedIn()) {
            binding.connectText.setVisibility(View.VISIBLE);
            binding.connectBt.setVisibility(View.VISIBLE);
            binding.connectBt.setOnClickListener(v -> {
                // Show the progress again while we log in
                binding.noWorkoutSection.setVisibility(View.GONE);
                binding.connectText.setVisibility(View.GONE);
                binding.connectBt.setVisibility(View.GONE);
                binding.empty.setVisibility(View.VISIBLE);
                startActivity(signInFlowHook.newStartIntent(requireContext(), null));
            });
        }
    }

    protected void hideConnectViews() {
        filterView.setVisibility(View.VISIBLE);
        totalsViewBinding.getRoot().setVisibility(View.VISIBLE);
        viewTotalsBinding.getRoot().setVisibility(View.VISIBLE);
        binding.empty.setVisibility(View.GONE);
        binding.noWorkoutSection.setVisibility(View.GONE);
        binding.connectText.setVisibility(View.GONE);
        binding.connectBt.setVisibility(View.GONE);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == STTConstants.RequestCodes.EDIT_WORKOUT) {
            switch (resultCode) {
                case STTConstants.RequestResult.WORKOUT_EDITED: {
                    WorkoutHeader workoutHeader =
                        data.getParcelableExtra(STTConstants.ExtraKeys.WORKOUT_HEADER);
                    updateWorkout(workoutHeader.getId(), workoutHeader);
                    getActivity().setResult(STTConstants.RequestResult.WORKOUT_EDITED);
                    break;
                }
                case STTConstants.RequestResult.WORKOUT_DELETED: {
                    WorkoutHeader workoutHeader =
                        data.getParcelableExtra(STTConstants.ExtraKeys.WORKOUT_HEADER);
                    getActivity().setResult(STTConstants.RequestResult.WORKOUT_DELETED);
                    updateDeletedWorkout(workoutHeader.getId());
                    break;
                }
                default:
                    Timber.w("Ignoring unknown result code %d for editing request", resultCode);
                    break;
            }
        }
    }

    @Override
    public boolean onContextItemSelected(MenuItem item) {
        ExpandableListContextMenuInfo info = (ExpandableListContextMenuInfo) item.getMenuInfo();

        final int groupPos;
        final int childPos;
        int type = ExpandableListView.getPackedPositionType(info.packedPosition);
        if (type == ExpandableListView.PACKED_POSITION_TYPE_CHILD) {
            groupPos = ExpandableListView.getPackedPositionGroup(info.packedPosition);
            childPos = ExpandableListView.getPackedPositionChild(info.packedPosition);
        } else {
            return super.onContextItemSelected(item);
        }

        switch (item.getItemId()) {
            case DELETE_ACTION:
                showDeleteDialog(groupPos, childPos);
                return true;
            default:
                return super.onContextItemSelected(item);
        }
    }

    private void showDeleteDialog(int groupPos, int childPos) {
        deleteDialogIsOpened = true;
        alertDialog = new AlertDialog.Builder(getActivity())
            .setView(R.layout.custom_alert_dialog)
            .setTitle(R.string.delete)
            .setMessage(R.string.delete_workout)
            .setPositiveButton(R.string.ok,
                (dialog, i) -> {
                    this.groupPos = groupPos;
                    this.childPos = childPos;
                    ExpandableWorkoutListAdapter listAdapter =
                        (ExpandableWorkoutListAdapter) getListAdapter();
                    WorkoutHeader workoutHeader = listAdapter.getChild(groupPos, childPos);
                    listAdapter.deleteChild(groupPos, childPos);
                    updateLocalWorkoutList(workoutHeader.getId());
                    /*
                     * After updating the list it closes the group so we have to
                     * re-open it.
                     * But be careful to not expand a non-existing group, that is, if
                     * the workout we deleted was the only one in the last group of
                     * workouts.
                     */
                    int groupToExpand = groupPos;
                    int groupCount = listAdapter.getGroupCount();
                    if (groupCount > 0) {
                        if (groupCount <= groupPos) {
                            groupToExpand--;
                        }
                        getExpandableListView().expandGroup(groupToExpand);
                    } else {
                        if (isSubView) {
                            getActivity().onBackPressed();
                        } else {
                            showConnectViews();
                        }
                    }

                    RemoveWorkoutService.enqueueWork(getContext(), workoutHeader);
                })
            .setNegativeButton(R.string.cancel, (dialog, i) -> dialog.dismiss())
            .setOnDismissListener(dialogInterface -> {
                deleteDialogIsOpened = false;
            })
            .create();
        alertDialog.show();
    }

    void updateDeletedWorkout(int deletedWorkoutId) {
        ExpandableWorkoutListAdapter adapter = (ExpandableWorkoutListAdapter) getListAdapter();
        if (adapter == null) {
            // There's no adapter so we're still loading the list or there's a refresh going on.
            return;
        }
        adapter.removeWorkout(deletedWorkoutId);
        if (adapter.getGroupCount() == 0) {
            showConnectViews();
        }
        updateLocalWorkoutList(deletedWorkoutId);
    }

    /**
     * If the current list is built out of a list of workouts provided in the arguments then update
     * this list
     */
    void updateLocalWorkoutList(int deletedWorkoutId) {
        if (workoutIds != null) {
            workoutIds.remove(Integer.valueOf(deletedWorkoutId));
        }
    }

    void updateWorkout(int oldWorkoutId, WorkoutHeader updatedWorkout) {
        ExpandableWorkoutListAdapter adapter = (ExpandableWorkoutListAdapter) getListAdapter();
        if (adapter == null) {
            // There's no adapter so we're still loading the list or there's a refresh going on.
            return;
        }
        hideConnectViews();
        Set<Integer> groupsToOpen = adapter.updateWorkout(oldWorkoutId, updatedWorkout);
        // This is retarded. We need to open the groups from here because
        // it's not possible to do it inside the adapter itself
        if (groupsToOpen != null) {
            for (Integer groupPos : groupsToOpen) {
                getListView().expandGroup(groupPos);
            }
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (deleteDialogIsOpened && (alertDialog == null || !alertDialog.isShowing())) {
            showDeleteDialog(this.groupPos, this.childPos);
        }
    }

    // Renamed to "Totals" (formerly "Summaries"). But to track the impact of this change, analytics names are unchanged.
    // "Summary" now points to @TrainingZoneSummaryFragment with tables and graphs.
    private void trackTotalsScreenAnalytics() {
        SummaryHighlightedProperty highlightedProperty = SummaryHighlightedProperty.fromOrdinal(
            defaultSharedPreferences.getInt(
                DiarySummariesFragment.HIGHLIGHTED_PROPERTY_SPINNER_SELECTION_ARG,
                SummaryHighlightedProperty.DEFAULT.ordinal()
            )
        );

        SummaryTimeFrameUnit timeFrameUnit = SummaryTimeFrameUnit.fromOrdinal(
            defaultSharedPreferences.getInt(
                DiarySummariesFragment.TIME_FRAME_SPINNER_SELECTION_ARG,
                SummaryTimeFrameUnit.DEFAULT.ordinal()
            )
        );

        AnalyticsProperties properties = new AnalyticsProperties();
        if (highlightedProperty != null) {
            properties.put(
                AnalyticsEventProperty.ST_DIARY_SUMMARY_TYPE,
                highlightedProperty.getAnalyticsPropertyValue()
            );
        }

        if (timeFrameUnit != null) {
            properties.put(
                AnalyticsEventProperty.CALENDAR_LEVEL,
                timeFrameUnit.getAnalyticsPropertyValue()
            );
        }

        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.DIARY_SUMMARY_SCREEN, properties);
    }
}
