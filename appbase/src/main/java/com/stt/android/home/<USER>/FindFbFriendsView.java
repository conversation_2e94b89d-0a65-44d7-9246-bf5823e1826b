package com.stt.android.home.people;

import android.view.View;
import com.stt.android.follow.UserFollowStatus;
import java.util.List;

public interface FindFbFriendsView extends FollowStatusView {

    void onFbFriendsLoaded(List<UserFollowStatus> facebookFriends);

    void createAddAllSnackBar(int numOfFriendsToAdd, View.OnClickListener undoAction);

    void showLoading();

    void hideLoading();

    void showActionError(View.OnClickListener tryAgainAction);
}