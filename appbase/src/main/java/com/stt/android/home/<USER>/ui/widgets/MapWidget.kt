package com.stt.android.home.dashboardv2.ui.widgets

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.bodySmallBold
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.home.dashboardv2.ui.widgets.common.PremiumForeground
import com.stt.android.home.dashboardv2.ui.widgets.common.RemoveWidgetButton
import com.stt.android.home.dashboardv2.ui.widgets.common.WidgetText
import com.stt.android.home.dashboardv2.widgets.MapWidgetInfo
import com.stt.android.ui.compose.DashboardMapSnapshot

@Composable
internal fun MapWidget(
    widgetInfo: MapWidgetInfo,
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    if (widgetInfo.premiumRequired) {
        BuyPremiumMapWidget(
            editMode = editMode,
            onClick = onClick,
            onLongClick = onLongClick,
            onRemoveClick = onRemoveClick,
            modifier = modifier,
        )
    } else {
        NormalMapWidget(
            widgetInfo = widgetInfo,
            editMode = editMode,
            onClick = onClick,
            onLongClick = onLongClick,
            onRemoveClick = onRemoveClick,
            modifier = modifier,
        )
    }
}

@Composable
private fun NormalMapWidget(
    widgetInfo: MapWidgetInfo,
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    val snapshotData = remember { widgetInfo.snapshotData }

    Box(
        modifier = modifier
            .fillMaxSize()
            .clickable(
                enabled = !editMode,
                onClick = onClick,
                onLongClick = onLongClick,
            ),
    ) {
        DashboardMapSnapshot(
            snapshotInfo = snapshotData,
            modifier = Modifier.fillMaxSize(),
        )

        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(MaterialTheme.spacing.medium)
        ) {
            MapActivities(
                activityCount = widgetInfo.activityCount,
                modifier = Modifier.align(Alignment.BottomStart),
            )

            if (editMode && onRemoveClick != null) {
                RemoveWidgetButton(
                    onClick = onRemoveClick,
                    modifier = Modifier.align(Alignment.TopEnd),
                )
            } else {
                widgetInfo.calendarIconRes?.let { icon ->
                    Image(
                        painter = painterResource(icon),
                        contentDescription = null,
                        modifier = Modifier
                            .size(MaterialTheme.iconSizes.small)
                            .align(Alignment.TopEnd),
                    )
                }
            }
        }
    }
}

@Composable
private fun MapActivities(
    activityCount: Int,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(MaterialTheme.spacing.medium))
            .background(MaterialTheme.colorScheme.nearWhite)
            .border(
                width = MaterialTheme.spacing.xxxsmall,
                color = MaterialTheme.colorScheme.cloudyGrey,
                shape = RoundedCornerShape(MaterialTheme.spacing.medium),
            )
            .padding(
                horizontal = MaterialTheme.spacing.small,
                vertical = MaterialTheme.spacing.xsmall,
            ),
        contentAlignment = Alignment.Center,
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            WidgetText(
                text = activityCount.toString(),
                style = MaterialTheme.typography.bodySmallBold,
                modifier = Modifier.alignByBaseline(),
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))
            WidgetText(
                text = pluralStringResource(
                    R.plurals.workouts_plural_without_quantity,
                    activityCount
                ).lowercase(),
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.alignByBaseline(),
            )
        }
    }
}

@Composable
private fun BuyPremiumMapWidget(
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .fillMaxSize()
            .clickable(
                onClick = onClick,
                onLongClick = onLongClick,
            )
    ) {
        Image(
            painter = painterResource(R.drawable.buy_premium_widget_map_placeholder_v2),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier.fillMaxSize(),
        )

        PremiumForeground(
            editMode = editMode,
            onRemoveClick = onRemoveClick,
        )
    }
}

@Preview
@Preview(locale = "zh")
@Composable
private fun MapActivitiesPreview() {
    M3AppTheme {
        MapActivities(7)
    }
}

@Preview(widthDp = 170, heightDp = 170)
@Composable
private fun BuyPremiumMapWidgetPreview() {
    M3AppTheme {
        BuyPremiumMapWidget(
            editMode = false,
            onClick = {},
            onLongClick = {},
            onRemoveClick = {},
        )
    }
}
