package com.stt.android.home.settings.v2

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.compose.base.BaseViewModel
import com.stt.android.controllers.BackendController
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.userSettings
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.STTErrorCodes
import com.stt.android.exceptions.BackendException
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.ui.utils.BitmapUtils
import com.stt.android.utils.FileUtils
import com.stt.android.utils.STTConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import javax.inject.Inject

@HiltViewModel
class UserSettingsViewModel @Inject constructor(
    private val userSettingsController: UserSettingsController,
    private val currentUserController: CurrentUserController,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    val backendController: BackendController,

    private val fileUtils: FileUtils
) : BaseViewModel() {
    val tempProfilePictureFile: File
        @Throws(IllegalStateException::class)
        get() = fileUtils.getCachedFilePath(STTConstants.DIRECTORY_MISC, "temp_profile.jpg")

    val tempCoverPictureFile: File
        @Throws(IllegalStateException::class)
        get() = fileUtils.getCachedFilePath(STTConstants.DIRECTORY_MISC, "temp_cover_photo.jpg")

    private val _profileImageUrl =
        MutableStateFlow(currentUserController.currentUser.profileImageUrl)
    val profileImageUrl = _profileImageUrl.asStateFlow()

    private val _uploadImageStatus = MutableStateFlow(UploadImageUIState())
    val uploadImageStatus: StateFlow<UploadImageUIState> = _uploadImageStatus

    data class UploadImageUIState(
        val isLoading: Boolean = false,
        val errorCode: Int? = null
    )

    private val _realName = MutableStateFlow<String?>(userSettingsController.settings.realName)
    val realName = _realName.asStateFlow()

    private val _userName = MutableStateFlow(currentUserController.currentUser.username)
    val userName = _userName.asStateFlow()

    private val _location = MutableStateFlow(userSettingsController.settings.country)
    val location = _location.asStateFlow()

    private val _countrySubdivision =
        MutableStateFlow(userSettingsController.settings.countrySubdivision)
    val countrySubdivision = _countrySubdivision.asStateFlow()

    private val _showLocale = MutableStateFlow(userSettingsController.settings.isShowLocale)
    val showLocale = _showLocale.asStateFlow()

    private val _bio = MutableStateFlow<String?>(userSettingsController.settings.description)
    val bio = _bio.asStateFlow()

    private val _coverPhoto = MutableStateFlow(currentUserController.currentUser.coverImageUrl)
    val coverPhoto = _coverPhoto.asStateFlow()

    init {
        viewModelScope.launch {
            userSettingsController.userSettings().collect { settings ->
                _realName.value = settings.realName
                _bio.value = settings.description
                _location.value = settings.country
                _countrySubdivision.value = settings.countrySubdivision
                _showLocale.value = settings.isShowLocale
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
    }

    fun updateUserProfileDescription(newDescription: String) {
        sendDescriptionAnalytics(hasDescription = !userSettingsController.settings.description.isNullOrBlank())
        userSettingsController.storeSettings(
            userSettingsController.settings.setProfileDescription(newDescription)
        )
        _bio.value = newDescription
    }

    fun resetUploadImageStatus() {
        _uploadImageStatus.update { it.copy(isLoading = false, errorCode = null) }
    }

    private suspend fun uploadProfilePicture(
        applicationContext: Context,
        width: Int,
        height: Int,
        file: File,
        pictureUri: Uri,
        isCoverPhoto: Boolean = false
    ) = withContext(
        Dispatchers.IO
    ) {
        val bitmap = BitmapUtils.decodeAndRotate(
            applicationContext,
            pictureUri,
            width,
            height
        ) ?: throw FileNotFoundException("Picture not found, URI: $pictureUri")
        val profilePicture = file
        ByteArrayOutputStream().use { baos ->
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, baos)
            FileOutputStream(profilePicture).use { fos ->
                fos.write(baos.toByteArray())
            }
        }
        currentUserController.session
            ?.let { userSession ->
                if (isCoverPhoto) {
                    backendController.pushUserCoverImage(userSession, profilePicture.absolutePath)
                } else {
                    backendController.pushUserProfileImage(userSession, profilePicture.absolutePath)
                }
                val user = backendController.fetchSessionUser(userSession).user
                currentUserController.store(user)
                _uploadImageStatus.update { it.copy(isLoading = false) }
                _profileImageUrl.value = user.profileImageUrl
                _coverPhoto.value = user.coverImageUrl
            }
    }


    suspend fun updateProfilePicture(
        data: Intent, applicationContext: Context,
        file:File,
        width: Int,
        height: Int,
        isCoverPhoto: Boolean = false
    ) = runSuspendCatching {
        _uploadImageStatus.update { it.copy(isLoading = true) }
        // some app returns the URI in data.getData(), while others simply put the cropped
        // image file in the path set by the MediaStore.EXTRA_OUTPUT extra in the request Intent
        val uri: Uri = data.data ?: Uri.fromFile(file)
        // some picture chooser apps will not respect mime types and still return GIFs'
        if (MediaStoreUtils.isSupportedImageMimeType(applicationContext, uri)) {
            runSuspendCatching {
                uploadProfilePicture(
                    applicationContext,
                    width,
                    height,
                    file,
                    uri,
                    isCoverPhoto = isCoverPhoto
                )
            }.onFailure { e ->
                Timber.w(e, "Failed to upload profile picture")
                if (e is BackendException) {
                    _uploadImageStatus.update {
                        it.copy(
                            isLoading = false,
                            errorCode = e.error.code
                        )
                    }
                } else {
                    _uploadImageStatus.update {
                        it.copy(
                            isLoading = false,
                            errorCode = STTErrorCodes.UNKNOWN.code
                        )
                    }
                }
            }
        } else {
            _uploadImageStatus.update {
                it.copy(
                    isLoading = false,
                    errorCode = STTErrorCodes.UNKNOWN.code
                )
            }
        }
    }.onFailure { e ->
        Timber.e(e, "Failed to decode profile image")
    }

    fun updateUserProfileLocation(newLocation: String) {
        userSettingsController.storeSettings(
            userSettingsController.settings.setCountry(newLocation)
        )
        _location.value = newLocation
    }

    fun updateUserProfileCountrySubdivision(newCountrySubdivision: String) {
        userSettingsController.storeSettings(
            userSettingsController.settings.setCountrySubdivision(newCountrySubdivision)
        )
        _countrySubdivision.value = newCountrySubdivision
    }

    fun updateUserProfileShowLocale(newShowLocale: Boolean) {
        userSettingsController.storeSettings(
            userSettingsController.settings.setShowLocale(newShowLocale)
        )
        _showLocale.value = newShowLocale
    }
    fun updateUserProfileRealName(newRealName: String) {
        userSettingsController.storeSettings(
            userSettingsController.settings.setRealName(newRealName)
        )
        _realName.value = newRealName
    }
    private fun sendDescriptionAnalytics(hasDescription: Boolean) {
        if (hasDescription) {
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.PROFILE_DESCRIPTION_EDIT
            )
        } else {
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.PROFILE_DESCRIPTION_ADD
            )
        }
    }


}

