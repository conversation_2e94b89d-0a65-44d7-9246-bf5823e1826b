<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- App preferences theme that can be customized if needed -->
    <style name="AppPreferencesTheme" parent="PreferenceThemeOverlay">
    </style>

    <style name="AppOverflowButtonStyle" parent="Widget.AppCompat.ActionButton.Overflow">
        <item name="android:tint">?android:textColorPrimary</item>
    </style>

    <style name="WhiteThemeBase" parent="Theme.MaterialComponents.DayNight.Bridge">
        <item name="colorAccent">@color/newAccent</item>
        <item name="colorPrimary">@color/white</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondaryAccent">@color/secondary_accent</item>
        <item name="colorDarkAccent">@color/dark_accent</item>
        <item name="colorDarkSecondaryAccent">@color/dark_secondary_accent</item>
        <item name="android:colorBackground">@color/white</item>
        <item name="android:colorForeground">@color/black</item>
        <item name="android:textColorPrimaryInverse">@color/white</item>
        <item name="android:textColorPrimary">@color/near_black</item>
        <item name="android:textColorSecondary">@color/middle_gray</item>
        <item name="android:textColorSecondaryInverse">@color/middle_gray</item>
        <item name="android:windowBackground">@color/white</item>
        <item name="suuntoDividerColor">@color/light_grey</item>
        <item name="suuntoProgressBarColor">@color/black</item>
        <item name="android:navigationBarColor">@color/black</item>
        <item name="android:statusBarColor">@color/black</item>
        <item name="suuntoProgressBarBackgroundColor">@color/light_grey</item>
        <item name="suuntoItemBackgroundColor">@color/white</item>
        <item name="suuntoDiaryAccentColor">@color/newAccent</item>
        <item name="suuntoBackground">@color/light_grey</item>
        <item name="suuntoEditTextBackgroundColor">@color/white</item>
        <item name="android:timePickerStyle">@style/TimePicker</item>
        <item name="suuntoFrameTintColor">@color/black</item>
        <item name="newAccentColor">@color/newAccent</item>
        <!--We must define preferenceTheme because we are using PreferenceFragmentCompat for app preferences.
        This can be modified / removed when we refactor the Settings view-->
        <item name="preferenceTheme">@style/AppPreferencesTheme</item>
        <item name="dividerBackground">@drawable/divider_white</item>
        <item name="windowActionModeOverlay">true</item>
        <item name="actionModeStyle">@style/ActionModeStyle</item>
        <item name="actionModeCloseDrawable">@drawable/ic_close_white</item>
        <item name="actionOverflowButtonStyle">@style/AppOverflowButtonStyle</item>

        <item name="analysisChartLabelColor">?android:textColorPrimary</item>
        <item name="analysisChartLineColor">?android:textColorPrimary</item>
        <item name="analysisChartHeartRateLineColor">@color/bright_red</item>
        <item name="analysisChartHeartRateThresholdColor">@color/middle_gray</item>
        <item name="analysisAverageLineColor">?android:textColorPrimary</item>
        <item name="analysisGridColor">@color/light_grey</item>
        <item name="selectedBulletColor">?android:textColorPrimary</item>
        <item name="bulletColor">@color/medium_grey</item>
        <item name="sectionDividerGradientColor">@color/very_very_light_gray</item>
        <item name="android:windowContentTransitions">true</item>

        <!-- There is no such theme as Theme.MaterialComponents.DayNight.Bridge.NoActionBar so
        disable action bar explicitly here -->
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>

        <item name="bottomSheetDialogTheme">@style/BottomSheetDialogTheme</item>

        <!-- Android 12 OS splash screen setup. The splash shown on older Android versions
             by ProxyActivity uses WhiteTheme.Launcher. These need to be included in DarkThemeBase
             too as they are read before STTApplication has a chance to force night mode off. -->
        <item name="android:windowSplashScreenBackground" tools:targetApi="s">@color/splash_background</item>

        <!-- these are needed for com.stt.android.compose.widgets.DateRangePicker to open material calendar -->
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
        <item name="materialCalendarFullscreenTheme">@style/CustomThemeOverlay_MaterialCalendar_Fullscreen</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
        <!-- end of needed by com.stt.android.compose.widgets.DateRangePicker -->

        <item name="alertDialogTheme">@style/MyAlertDialogTheme</item>
    </style>

    <!-- Dark app theme. Don't use directly unless required, instead use WhiteTheme night-mode variant. -->

    <style name="DarkThemeBase" parent="Theme.MaterialComponents.DayNight.Bridge">
        <item name="colorAccent">@color/newAccent</item>
        <item name="colorPrimary">@color/black</item>
        <item name="colorSecondaryAccent">@color/secondary_accent</item>
        <item name="colorDarkAccent">@color/dark_accent</item>
        <item name="colorDarkSecondaryAccent">@color/dark_secondary_accent</item>
        <item name="android:colorBackground">@color/black</item>
        <item name="android:colorForeground">@color/white</item>
        <item name="android:textColorPrimaryInverse">@color/black</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/middle_gray</item>
        <item name="android:navigationBarColor">@color/black</item>
        <item name="android:statusBarColor">@color/black</item>
        <item name="android:textColorSecondaryInverse">@color/middle_gray</item>
        <item name="android:windowBackground">@color/black</item>
        <item name="suuntoDividerColor">@color/black</item>
        <item name="suuntoProgressBarBackgroundColor">@color/quite_dark_gray</item>
        <item name="suuntoProgressBarColor">@color/newAccent</item>
        <item name="suuntoItemBackgroundColor">@color/very_dark_gray</item>
        <item name="suuntoDiaryAccentColor">@color/newAccent</item>
        <item name="suuntoBackground">@color/black</item>
        <item name="suuntoEditTextBackgroundColor">@color/quite_dark_gray</item>
        <item name="suuntoFrameTintColor">@color/quite_dark_gray</item>
        <item name="newAccentColor">@color/newAccent</item>
        <!--We must define preferenceTheme because we are using PreferenceFragmentCompat for app preferences.
        This can be modified / removed when we refactor the Settings view-->
        <item name="preferenceTheme">@style/AppPreferencesTheme</item>
        <item name="android:timePickerStyle">@style/TimePicker</item>
        <item name="dividerBackground">@drawable/divider_dark</item>
        <item name="windowActionModeOverlay">true</item>
        <item name="actionModeStyle">@style/ActionModeStyle</item>
        <item name="actionModeCloseDrawable">@drawable/ic_close_white</item>
        <item name="actionOverflowButtonStyle">@style/AppOverflowButtonStyle</item>

        <item name="analysisChartLabelColor">?android:textColorSecondary</item>
        <item name="analysisChartLineColor">@color/analysis_chart_trend_line_color</item>
        <item name="analysisChartHeartRateLineColor">?android:textColorPrimary</item>
        <item name="analysisChartHeartRateThresholdColor">?android:textColorPrimary</item>
        <item name="analysisAverageLineColor">?android:textColorPrimary</item>
        <item name="analysisGridColor">@color/quite_dark_gray</item>
        <item name="selectedBulletColor">@color/active_bullet_on_dark</item>
        <item name="bulletColor">@color/inactive_bullet_on_dark</item>
        <item name="sectionDividerGradientColor">@color/quite_dark_gray</item>
        <item name="android:windowContentTransitions">true</item>

        <!-- There is no such theme as Theme.MaterialComponents.DayNight.Bridge.NoActionBar so
        disable action bar explicitly here -->
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>

        <!-- Android 12 splash screen setup, keep in sync with WhiteThemeBase -->
        <!-- The light colored splash background isn't the best for dark theme, but the app practically
             never launches directly to dark themed Activity, and the Android 12 splash seems to check
             for this value before the STTApplication has a chance to force night mode off.
             Having a separate dark splash background would matter mostly when the app launches after
             process death directly to WorkoutActivity or to an Activity that specifically uses DarkTheme. -->
        <item name="android:windowSplashScreenBackground" tools:targetApi="s">@color/splash_background</item>

        <!-- these are needed for com.stt.android.compose.widgets.DateRangePicker to open material calendar -->
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
        <item name="materialCalendarFullscreenTheme">@style/CustomThemeOverlay_MaterialCalendar_Fullscreen</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
        <!-- end of needed by com.stt.android.compose.widgets.DateRangePicker -->
    </style>

    <!-- these are needed for com.stt.android.compose.widgets.DateRangePicker to open material calendar -->
    <style name="CustomThemeOverlay_MaterialCalendar_Fullscreen" parent="@style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen">
        <item name="materialCalendarStyle">@style/Custom_MaterialCalendar.Fullscreen</item>
    </style>

    <style name="Custom_MaterialCalendar.Fullscreen" parent="@style/Widget.MaterialComponents.MaterialCalendar.Fullscreen">
        <!-- <item name="android:windowFullscreen">false</item> enable if calendar should load fullscreen -->
        <item name="rangeFillColor">@color/newAccent</item>
        <item name="daySelectedStyle">@style/Custom_MaterialCalendar_DaySelected</item>
    </style>

    <style name="Custom_MaterialCalendar_DaySelected" parent="@style/Widget.Material3.MaterialCalendar.Day.Selected">
        <item name="itemFillColor">@color/dark_accent</item>
    </style>
    <!-- end of needed by com.stt.android.compose.widgets.DateRangePicker -->

    <style name="WhiteTheme" parent="WhiteThemeBase">
        <item name="android:statusBarColor">@color/white</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>

    <style name="DarkTheme" parent="DarkThemeBase">
        <item name="android:statusBarColor">@color/black</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>

    <style name="DarkTheme.ActionBar">
        <item name="windowActionBar">true</item>
        <item name="windowNoTitle">false</item>
    </style>

    <style name="WhiteTheme.ActionBar">
        <item name="windowActionBar">true</item>
        <item name="windowNoTitle">false</item>
    </style>

    <!-- A theme for BottomSheet dialogs with rounded corners without using Shapes
         A workaround for non-round corners on expanded sheets:
         https://github.com/material-components/material-components-android/issues/1278#issuecomment-844979762
         Specify @drawable/bottom_sheet_rounded_top_corners_background.xml as the background for
         the bottom sheet when rounded corners are needed.
    -->
    <style name="BottomSheetDialogTheme" parent="ThemeOverlay.MaterialComponents.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/ModalBottomSheetDialogStyle</item>
        <item name="android:colorBackground">@color/bottom_sheet_background</item>
    </style>

    <!-- Themes for diary tabs.
    The diary changes it's accent color depending on the tab. -->

    <!-- With white parent theme -->
    <style name="WhiteTheme.DiaryWorkouts" parent="WhiteTheme">
        <item name="suuntoDiaryAccentColor">@color/functional_aqua</item>
    </style>

    <style name="WhiteTheme.DiarySteps" parent="WhiteTheme">
        <item name="suuntoDiaryAccentColor">@color/functional_cyan</item>
    </style>

    <style name="WhiteTheme.DiaryCalories" parent="WhiteTheme">
        <item name="suuntoDiaryAccentColor">@color/functional_yellow</item>
    </style>

    <style name="WhiteTheme.DiarySleep" parent="WhiteTheme">
        <item name="suuntoDiaryAccentColor">@color/functional_purple</item>
    </style>

    <style name="WhiteTheme.DiaryRecovery" parent="WhiteTheme">
        <item name="suuntoDiaryAccentColor">@color/suunto_outdoor_adventures</item>
    </style>

    <style name="WhiteTheme.DiaryRecoveryGraph" parent="WhiteTheme">
        <item name="suuntoDiaryAccentColor">@color/suunto_unspecified</item>
    </style>

    <style name="WhiteTheme.DiarySleepNap" parent="WhiteTheme">
        <item name="suuntoDiaryAccentColor">@color/activity_data_nap</item>
    </style>

    <style name="WhiteTheme.DiaryOverview" parent="WhiteTheme">
        <item name="suuntoDiaryAccentColor">@color/functional_orange</item>
    </style>

    <style name="WhiteTheme.DiarySummary" parent="WhiteTheme" />

    <style name="WhiteTheme.DiaryScubaDiving" parent="WhiteTheme">
        <item name="suuntoDiaryAccentColor">@color/suunto_diving</item>
    </style>

    <style name="WhiteTheme.DiaryFreeDiving" parent="WhiteTheme">
        <item name="suuntoDiaryAccentColor">@color/suunto_diving</item>
    </style>

    <style name="WhiteTheme.DiaryCalendar" parent="WhiteTheme">
        <item name="suuntoDiaryAccentColor">@color/diary_calendar_color</item>
    </style>

    <style name="WhiteTheme.DiaryTSS" parent="WhiteTheme">
        <item name="suuntoDiaryAccentColor">@color/activity_progression</item>
    </style>

    <style name="WhiteTheme.Launcher">
        <item name="android:windowBackground">@drawable/launch_screen</item>
    </style>

    <!-- With dark parent theme -->
    <style name="DarkTheme.DiaryWorkouts" parent="DarkTheme">
        <item name="suuntoDiaryAccentColor">@color/functional_aqua</item>
    </style>

    <style name="DarkTheme.DiarySteps" parent="DarkTheme">
        <item name="suuntoDiaryAccentColor">@color/functional_cyan</item>
    </style>

    <style name="DarkTheme.DiaryCalories" parent="DarkTheme">
        <item name="suuntoDiaryAccentColor">@color/functional_yellow</item>
    </style>

    <style name="DarkTheme.DiarySleep" parent="DarkTheme">
        <item name="suuntoDiaryAccentColor">@color/functional_purple</item>
    </style>

    <style name="DarkTheme.DiaryScubaDiving" parent="DarkTheme">
        <item name="suuntoDiaryAccentColor">@color/functional_blue</item>
    </style>

    <style name="DarkTheme.DiaryFreeDiving" parent="DarkTheme">
        <item name="suuntoDiaryAccentColor">@color/functional_blue</item>
    </style>

    <style name="DarkTheme.DiaryCalendar" parent="DarkTheme">
        <!-- todo newDiary: change to proper color when we have one -->
        <item name="suuntoDiaryAccentColor">@color/bright_red</item>
    </style>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <!--<item name="colorPrimary">#2196F3</item>
        <item name="colorPrimaryDark">#1565C0</item>-->
        <item name="colorAccent">@color/accent</item>
    </style>

    <style name="AppTheme.WithoutActionBar" parent="AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Use default theme for API levels up to and including 26, but customize for 27 and above -->
    <style name="LightBottomSheetDialog" parent="Theme.MaterialComponents.Light.BottomSheetDialog"/>

    <!-- Theme to style plugin activity -->
    <style name="LicensesTheme" parent="WhiteTheme">
        <item name="android:textSize">@dimen/text_size_medium</item>s
    </style>


    <!-- New designed dialog -->
    <!-- 自定义弹出样式 -->
    <style name="MyDialogStyle" parent="Theme.AppCompat.Light.Dialog.Alert">
        <!--是否浮在窗口之上-->
        <!--<item name="android:windowIsFloating">true</item>-->
        <!--半透明-->
        <!--<item name="android:windowIsTranslucent">true</item>-->
        <!--是否显示title-->
        <item name="android:windowNoTitle">true</item>
        <!--dialog之外没有焦点的区域是否罩上黑色半透明-->
        <item name="android:background">@color/white</item>
        <item name="android:textColor">@color/near_black</item>
        <item name="android:textSize">14sp</item>
        <item name="android:layout_gravity">center</item>
        <item name="buttonBarButtonStyle">@color/white</item>
    </style>
    <!-- Dialog 样式 -->
    <style name="MyAlertDialogTheme" parent="ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <!-- 设置圆角背景 -->
        <item name="shapeAppearance">@style/MyDialogShape</item>

        <!-- 背景颜色 -->
        <item name="backgroundColor">@color/white</item>

        <!-- 设置标题、消息样式 -->
        <item name="textAppearanceHeadline6">@style/MyDialogTitle</item>
        <item name="textAppearanceBody2">@style/MyDialogMessage</item>

        <!-- 设置按钮样式 -->
        <item name="buttonBarPositiveButtonStyle">@style/MyPositiveButton</item>
        <item name="buttonBarNegativeButtonStyle">@style/MyNegativeButton</item>
    </style>

    <style name="MyDialogShape" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerSize">16dp</item>
    </style>

    <style name="MyDialogTitle" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">@color/near_black</item>
        <item name="android:gravity">center</item> <!-- 可选 -->
    </style>

    <style name="MyDialogMessage" parent="TextAppearance.MaterialComponents.Body2">
        <item name="android:textColor">@color/middle_gray</item>
        <item name="android:gravity">center</item> <!-- 可选 -->
    </style>

    <!-- OK按钮：实心蓝底白字 -->
    <style name="MyPositiveButton" parent="Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:background">@drawable/bg_dialog_button_positive</item>
        <item name="android:backgroundTint">@color/newAccent</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="cornerRadius">8dp</item>
        <item name="enforceMaterialTheme">false</item>
        <item name="enforceTextAppearance">false</item>
    </style>

    <!-- Cancel按钮：边框灰字白底 -->
    <style name="MyNegativeButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:background">@drawable/bg_dialog_button_negative</item>
        <item name="android:backgroundTint">@null</item>
        <item name="android:textColor">@color/middle_gray</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="enforceMaterialTheme">false</item>
        <item name="enforceTextAppearance">false</item>
    </style>

    <!-- New designed dialog -->

</resources>
