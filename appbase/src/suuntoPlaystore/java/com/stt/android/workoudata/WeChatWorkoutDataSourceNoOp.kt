package com.stt.android.workoudata

import com.stt.android.domain.workouts.WeChatWorkoutData
import com.stt.android.domain.workouts.WeChatWorkoutDataSource
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.SummaryExtension
import javax.inject.Inject

class WeChatWorkoutDataSourceNoOp @Inject constructor() : WeChatWorkoutDataSource {

    override fun store(workoutHeader: WorkoutHeader, extension: SummaryExtension) {}

    override suspend fun findUnSyncData(): List<WeChatWorkoutData> = emptyList()

    override suspend fun updateStatus(id: Int, sync: <PERSON><PERSON><PERSON>) {}

    override suspend fun findSyncedData(): List<WeChatWorkoutData> = emptyList()

    override suspend fun deleteSynced() {}

    override fun deleteAll() {}
}
