package com.stt.android

import android.content.Context
import com.stt.android.home.settings.connectedservices.WeChatConnectServiceHelper
import javax.inject.Inject

class DefaultHandleConnectServiceHelper @Inject constructor() : WeChatConnectServiceHelper {
    override fun showWeRunItem(): Boolean = false
    override fun toWeRunConnectGuidePage(context: Context) {}
    override fun toWeRunConnectedPage(context: Context) {}
}
