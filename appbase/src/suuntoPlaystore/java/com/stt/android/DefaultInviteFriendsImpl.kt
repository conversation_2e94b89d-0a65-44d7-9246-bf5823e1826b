package com.stt.android

import android.content.Context
import com.stt.android.home.people.InviteFriendsHelper
import javax.inject.Inject

class DefaultInviteFriendsImpl @Inject constructor() : InviteFriendsHelper {
    override fun showPhoneContact(): Boolean = false

    override fun toPhoneContactActivity(context: Context) {
        // do nothing
    }

    override fun showUserName(): Boolean = true
}
