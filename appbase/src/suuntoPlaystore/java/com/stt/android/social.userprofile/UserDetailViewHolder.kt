package com.stt.android.social.userprofile

import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.databinding.ItemUserProfileDetailBinding
import com.stt.android.follow.UserFollowStatus

internal class UserDetailViewHolder(
    activity: BaseUserProfileActivity,
    private val userDetailPresenter: UserDetailPresenter,
    currentUserController: CurrentUserController,
    binding: ItemUserProfileDetailBinding,
    userSettingsController: UserSettingsController,
    loadUser: () -> Unit,
    onDescriptionClicked: () -> Unit,
    onRealNameClicked: () -> Unit
) : BaseUserDetailViewHolder(
    activity,
    userDetailPresenter,
    currentUserController,
    binding,
    userSettingsController,
    loadUser,
    onDescriptionClicked,
    onRealNameClicked
) {
    override fun showFollowActionSpinner(userFollowStatus: UserFollowStatus?) {
        // do nothing
    }

    override fun onChangeProfileImage(needRequestPermission: Boolean) {
        userDetailPresenter.informProfileImageClick()
    }
}
