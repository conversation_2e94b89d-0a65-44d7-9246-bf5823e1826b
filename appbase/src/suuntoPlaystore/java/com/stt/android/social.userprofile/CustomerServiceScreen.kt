package com.stt.android.social.userprofile

import android.annotation.SuppressLint
import android.net.Uri
import android.view.ViewGroup.LayoutParams
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebView
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.viewinterop.AndroidView
import com.github.lzyzsd.jsbridge.BridgeWebView
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.SuuntoIcons

@OptIn(ExperimentalMaterial3Api::class)
@SuppressLint("SetJavaScriptEnabled")
@Composable
fun CustomerServiceScreen(
    url: String,
    params: String,
    onFileChoose: (ValueCallback<Array<Uri>>?) -> Unit,
    onBackPressed: () -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            TopAppBar(title = {
                Text(text = stringResource(R.string.user_profile_support_chat_title))
            }, navigationIcon = {
                SuuntoIconButton(
                    icon = SuuntoIcons.ActionBack,
                    onClick = onBackPressed,
                )
            })
        }) { paddingValue ->
        AndroidView(
            modifier = Modifier
                .padding(paddingValue)
                .fillMaxSize()
                .narrowContent(),
            factory = { context ->
                BridgeWebView(context).apply {
                    layoutParams =
                        LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
                    settings.domStorageEnabled = true
                    settings.setSupportZoom(false)
                    settings.displayZoomControls = false
                    registerHandler("getLoginInfo") { _, function ->
                        function.onCallBack(params)
                    }
                    webChromeClient = object : WebChromeClient() {
                        override fun onShowFileChooser(
                            webView: WebView?,
                            filePathCallback: ValueCallback<Array<Uri>>?,
                            fileChooserParams: FileChooserParams?
                        ): Boolean {
                            onFileChoose.invoke(filePathCallback)
                            return true
                        }
                    }
                }
            }
        ) { webView ->
            webView.loadUrl(url)
        }
    }
}
