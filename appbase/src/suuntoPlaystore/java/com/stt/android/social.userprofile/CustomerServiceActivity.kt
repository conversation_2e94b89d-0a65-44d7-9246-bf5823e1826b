package com.stt.android.social.userprofile

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.webkit.ValueCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.net.toUri
import com.google.gson.JsonObject
import com.stt.android.R
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.usecases.GetHumanReadablePairedWatchModelNameUseCase
import com.stt.android.usecases.NoPairedWatchModelFoundException
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class CustomerServiceActivity : AppCompatActivity() {
    @Inject
    lateinit var currentUserController: CurrentUserController

    @Inject
    lateinit var userSettingsController: UserSettingsController

    @Inject
    lateinit var getHumanReadablePairedWatchModelNameUseCase: GetHumanReadablePairedWatchModelNameUseCase
    private var pathCallback: ValueCallback<Array<Uri>>? = null

    private val fileChooserLauncher: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            val resultData = if (result.resultCode == Activity.RESULT_OK) {
                result.data?.data?.let { arrayOf(it) }
            } else {
                null
            }
            pathCallback?.onReceiveValue(resultData)
            pathCallback = null
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithM3Theme {
            CustomerServiceScreen(url = getUrl(), params = getLoginInfo(), onFileChoose = {
                pathCallback?.onReceiveValue(null)
                pathCallback = it
                openFileChooser()
            }, onBackPressed = {
                onBackPressedDispatcher.onBackPressed()
            })
        }
    }

    private fun getUrl(): String {
        return getString(R.string.support_chat_bot_url_quick_cep).toUri()
            .buildUpon()
            .appendQueryParameter("platform", "others")
            .appendQueryParameter(
                "accessId",
                getString(R.string.support_chat_bot_quick_cep_id)
            ).appendQueryParameterIfNotNull("lang", getQuickCEPLanguageCode())
            .build()
            .toString()
    }

    private fun getQuickCEPLanguageCode(): String? {
        val localLanguageCode = Locale.getDefault().language
        return when (localLanguageCode) {
            "zh" -> if (traditionalChinese()) "cht" else "zh"
            "cs" -> "cs"
            "vi" -> "vie"
            "tr" -> "tr"
            "th" -> "t"
            "sv" -> "swe"
            "es" -> "spa"
            "ru" -> "ru"
            "pt" -> "pt"
            "pl" -> "pl"
            "ko" -> "kor"
            "ja" -> "jp"
            "it" -> "it"
            "de" -> "de"
            "fr" -> "fra"
            "fi" -> "fin"
            "nl" -> "nl"
            "da" -> "dan"
            else -> null
        }
    }

    private fun traditionalChinese(): Boolean {
        val country = Locale.getDefault().country
        return country == "TW" || country == "HK"
    }

    /**
     * get user info for quickCEP
     */
    private fun getLoginInfo(): String {
        val user = currentUserController.currentUser
        val params = JsonObject()
        params.addProperty("code", 1)
        val data = JsonObject()
        data.addProperty("ID", user.key)
        data.addProperty("email", userSettingsController.settings.email)
        data.addProperty("phone", userSettingsController.settings.phoneNumber)
        data.addProperty("firstName", user.realNameOrUsername)
        params.add("data", data)
        val customValue = JsonObject()
        val product: String? = try {
            getHumanReadablePairedWatchModelNameUseCase()
        } catch (e: NoPairedWatchModelFoundException) {
            null
        }
        customValue.addProperty("product", product)
        params.add("custom", customValue)
        return params.toString()
    }

    private fun openFileChooser() {
        val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            putExtra(Intent.EXTRA_MIME_TYPES, MediaStoreUtils.SUPPORTED_IMAGE_MIME_TYPES)
            type = "*/*"
        }
        fileChooserLauncher.launch(
            Intent.createChooser(
                intent,
                getString(R.string.gallery_menu_launch_file_picker)
            )
        )
    }

    private fun Uri.Builder.appendQueryParameterIfNotNull(
        key: String,
        value: String?
    ): Uri.Builder {
        return if (value != null) {
            appendQueryParameter(key, value)
        } else {
            this
        }
    }

    companion object {
        fun newIntent(context: Context): Intent =
            Intent(context, CustomerServiceActivity::class.java)
    }
}
