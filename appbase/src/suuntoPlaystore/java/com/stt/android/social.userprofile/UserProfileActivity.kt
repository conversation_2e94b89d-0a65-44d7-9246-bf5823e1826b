package com.stt.android.social.userprofile

import android.content.Context
import android.content.Intent
import com.stt.android.domain.user.User
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class UserProfileActivity : SuuntoUserProfileActivity() {

    override fun onContactCustomerServiceClicked() {
        // Do nothing
    }

    override fun onAfterSalesServiceClicked() {
        // Do nothing
    }

    override fun onChatBotClicked() {
        startActivity(CustomerServiceActivity.newIntent(this))
    }

    companion object {
        @JvmStatic
        fun newStartIntent(
            context: Context,
            userName: String,
            fromNotification: Boolean
        ): Intent {
            return BaseUserProfileActivity.newStartIntent(context, userName, fromNotification)
        }

        @JvmStatic
        fun newStartIntent(context: Context, user: User): Intent {
            return BaseUserProfileActivity.newStartIntent(context, user)
        }
    }
}
