package com.stt.android.launcher

import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import javax.inject.Inject

@HiltViewModel
class ProxyViewModel @Inject constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    workoutHeaderController: WorkoutHeaderController,
    currentUserController: CurrentUserController,
) : BaseProxyViewModel(ioThread, mainThread, workoutHeaderController, currentUserController)
