package com.stt.android.home.dashboardv2

import android.content.SharedPreferences
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.di.ExploreMapPreferences
import com.stt.android.domain.firstpairing.FirstPairingInfoUseCase
import com.stt.android.domain.terms.NeedAcceptTermsUseCase
import com.stt.android.home.dashboardv2.repository.DashboardConfigRepository
import com.stt.android.models.MapSelectionModel
import com.stt.android.refreshable.Refreshables
import com.stt.android.watch.forcedupdate.ShouldShowOnboardingUseCase
import com.stt.android.workouts.RecordWorkoutModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
internal class DashboardViewModel @Inject constructor(
    refreshables: Refreshables,
    dashboardConfigRepository: DashboardConfigRepository,
    currentUserController: CurrentUserController,
    emarsysAnalytics: EmarsysAnalytics,
    firstPairingInfoUseCase: FirstPairingInfoUseCase,
    shouldShowOnboardingUseCase: ShouldShowOnboardingUseCase,
    coroutinesDispatchers: CoroutinesDispatchers,
    recordWorkoutModel: RecordWorkoutModel,
    mapSelectionModel: MapSelectionModel,
    @ExploreMapPreferences exploreMapPreferences: SharedPreferences,
    needAcceptTermsUseCase: NeedAcceptTermsUseCase,
) : BaseSuuntoDashboardViewModel(
    refreshables = refreshables,
    dashboardConfigRepository = dashboardConfigRepository,
    currentUserController = currentUserController,
    emarsysAnalytics = emarsysAnalytics,
    emarsysAppName = "app_suunto",
    firstPairingInfoUseCase = firstPairingInfoUseCase,
    shouldShowOnboardingUseCase = shouldShowOnboardingUseCase,
    coroutinesDispatchers = coroutinesDispatchers,
    recordWorkoutModel = recordWorkoutModel,
    mapSelectionModel = mapSelectionModel,
    exploreMapPreferences = exploreMapPreferences,
    needAcceptTermsUseCase = needAcceptTermsUseCase,
)
