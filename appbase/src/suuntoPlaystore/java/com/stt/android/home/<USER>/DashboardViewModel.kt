package com.stt.android.home.dashboard

import android.app.Application
import android.content.SharedPreferences
import androidx.work.WorkManager
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.ReactionModel
import com.stt.android.controllers.SummaryExtensionDataModel
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.firstpairing.FirstPairingInfoUseCase
import com.stt.android.domain.marketing.MarketingBannerUseCase
import com.stt.android.domain.terms.NeedAcceptTermsUseCase
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.eventtracking.EventTracker
import com.stt.android.home.dashboard.card.ExploreCardLoader
import com.stt.android.home.dashboard.card.SportieCardLoader
import com.stt.android.home.dashboard.card.WelcomeCardLoader
import com.stt.android.home.dashboard.card.WorkoutCardLoader
import com.stt.android.refreshable.Refreshables
import com.stt.android.watch.forcedupdate.ShouldShowOnboardingUseCase
import com.stt.android.workouts.SyncSingleWorkoutUseCase
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import javax.inject.Inject

@HiltViewModel
class DashboardViewModel @Inject constructor(
    dashboardAnalytics: DashboardAnalytics,
    workoutHeaderController: WorkoutHeaderController,
    currentUserController: CurrentUserController,
    sharedPreferences: SharedPreferences,
    reactionModel: ReactionModel,
    needAcceptTermsUseCase: NeedAcceptTermsUseCase,
    appContext: Application,
    workManager: WorkManager,
    syncSingleWorkoutUseCase: SyncSingleWorkoutUseCase,
    refreshables: Refreshables,
    emarsysAnalytics: EmarsysAnalytics,
    isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    workoutCardLoader: WorkoutCardLoader,
    exploreCardLoader: ExploreCardLoader,
    sportieCardLoader: SportieCardLoader,
    welcomeCardLoader: WelcomeCardLoader,
    summaryExtensionDataModel: SummaryExtensionDataModel,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    firstPairingInfoUseCase: FirstPairingInfoUseCase,
    shouldShowOnboardingUseCase: ShouldShowOnboardingUseCase,
    workoutShareHelper: WorkoutShareHelper,
    marketingBannerUseCase: MarketingBannerUseCase,
    eventTracker: EventTracker,
    coroutinesDispatchers: CoroutinesDispatchers,
) : BaseSuuntoDashboardViewModel(
    dashboardAnalytics,
    workoutHeaderController,
    currentUserController,
    sharedPreferences,
    reactionModel,
    needAcceptTermsUseCase,
    appContext,
    workManager,
    syncSingleWorkoutUseCase,
    refreshables,
    emarsysAnalytics,
    isSubscribedToPremiumUseCase,
    workoutCardLoader,
    exploreCardLoader,
    sportieCardLoader,
    welcomeCardLoader,
    summaryExtensionDataModel,
    ioThread,
    mainThread,
    firstPairingInfoUseCase,
    shouldShowOnboardingUseCase,
    workoutShareHelper,
    marketingBannerUseCase,
    eventTracker,
    coroutinesDispatchers,
) {
    override val emrasysAppName: String get() = "app_suunto"
}
