package com.stt.android.inappreview

import android.content.SharedPreferences
import com.google.android.play.core.review.ReviewManager
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.controllers.UserSettingsController
import com.stt.android.help.LoadSupportMetadataUseCase
import com.stt.android.utils.STTConstants.InAppReviewPreferences.SUUNTO_DISABLED_APP_RATING_SUGGESTIONS_VALUE
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class InAppRatingFragmentViewModel @Inject constructor(
    sharedPreferences: SharedPreferences,
    reviewManager: ReviewManager,
    emarsysAnalytics: EmarsysAnalytics,
    amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    loadSupportMetadataUseCase: LoadSupportMetadataUseCase,
    userSettingsController: UserSettingsController
) : BaseInAppRatingFragmentViewModel(
    sharedPreferences,
    reviewManager,
    emarsysAnalytics,
    amplitudeAnalyticsTracker,
    loadSupportMetadataUseCase,
    userSettingsController
) {

    override fun disabledInAppRatingUserSettingsKey(): String =
        SUUNTO_DISABLED_APP_RATING_SUGGESTIONS_VALUE
}
