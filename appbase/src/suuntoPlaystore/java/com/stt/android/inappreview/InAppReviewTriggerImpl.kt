package com.stt.android.inappreview

import android.content.SharedPreferences
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.gear.GearRepository
import com.stt.android.utils.STTConstants
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import kotlinx.coroutines.withContext
import javax.inject.Inject

class InAppReviewTriggerImpl @Inject constructor(
    sharedPreferences: SharedPreferences,
    currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val gearRepository: GearRepository,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : BaseInAppReviewTrigger(
    sharedPreferences = sharedPreferences,
    currentUserController = currentUserController,
    coroutinesDispatchers = coroutinesDispatchers,
) {
    override suspend fun isEnabledForBrand(): Boolean {
        val excludedWatches = setOf(
            SuuntoDeviceType.Unrecognized,
            SuuntoDeviceType.SpartanUltra,
            SuuntoDeviceType.SpartanSport,
            SuuntoDeviceType.SpartanSportWristHR,
            SuuntoDeviceType.SpartanTrainer,
            SuuntoDeviceType.SpartanSportWristHRBaro,
            SuuntoDeviceType.Suunto7,
            SuuntoDeviceType.Ambit3Peak,
            SuuntoDeviceType.Ambit3Sport,
            SuuntoDeviceType.Ambit3Run,
            SuuntoDeviceType.Ambit3Vertical,
            SuuntoDeviceType.Traverse,
            SuuntoDeviceType.TraverseAlpha,
            SuuntoDeviceType.EonSteel,
            SuuntoDeviceType.EonSteelBlack,
            SuuntoDeviceType.EonCore,
            SuuntoDeviceType.SuuntoD5
        )
        return withContext(coroutinesDispatchers.io) {
            gearRepository.fetchAll().any {
                !excludedWatches.contains(SuuntoDeviceType.fromVariantName(it.name))
            }
        }
    }

    override fun isAppRatingSuggestionsDisabled(): Boolean =
        userSettingsController.settings.disabledAppRatingSuggestions.contains(
            STTConstants.InAppReviewPreferences.SUUNTO_DISABLED_APP_RATING_SUGGESTIONS_VALUE
        )
}
