package com.stt.android.di.findfriends

import com.stt.android.DefaultInviteFriendsImpl
import com.stt.android.home.people.InviteFriendsHelper
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class FindFriendsModule {
    @Binds
    abstract fun bindFindFriendsToggle(findFriendsToggleImpl: DefaultInviteFriendsImpl): InviteFriendsHelper
}
