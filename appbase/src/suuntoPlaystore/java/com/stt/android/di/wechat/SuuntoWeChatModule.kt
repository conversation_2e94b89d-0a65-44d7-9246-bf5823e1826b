package com.stt.android.di.wechat

import com.stt.android.DefaultHandleConnectServiceHelper
import com.stt.android.domain.workouts.WeChatWorkoutDataSource
import com.stt.android.home.settings.connectedservices.WeChatConnectServiceHelper
import com.stt.android.watch.WeChatConnectionStateHelper
import com.stt.android.watch.background.WeChatDataSyncScheduler
import com.stt.android.wechat.WeChatConnectionStateHelperNoOp
import com.stt.android.wechat.WeChatDataSyncSchedulerNoOp
import com.stt.android.workoudata.WeChatWorkoutDataSourceNoOp
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class SuuntoWeChatModule {

    @Binds
    abstract fun bindDefaultHandConnectService(handleConnectServiceHelper: DefaultHandleConnectServiceHelper): WeChatConnectServiceHelper

    @Binds
    abstract fun bindWechatWorkoutDataSource(weChatWorkoutDataSourceNoOp: WeChatWorkoutDataSourceNoOp): WeChatWorkoutDataSource

    @Binds
    abstract fun bindWeChatDataSyncScheduler(weChatDataSyncSchedulerNoOp: WeChatDataSyncSchedulerNoOp): WeChatDataSyncScheduler

    @Binds
    abstract fun bindWeChatConnectionStateHelper(weChatConnectionStateHelperNoOp: WeChatConnectionStateHelperNoOp): WeChatConnectionStateHelper
}
