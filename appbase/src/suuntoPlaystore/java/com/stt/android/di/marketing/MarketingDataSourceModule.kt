package com.stt.android.di.marketing

import com.squareup.moshi.Moshi
import com.stt.android.data.Local
import com.stt.android.data.Remote
import com.stt.android.data.marketing.MarketingBannerLocalDataSource
import com.stt.android.data.marketing.MarketingBannerRemoteDataSource
import com.stt.android.domain.marketing.MarketingBannerDataSource
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrlWithoutPath
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import com.stt.android.remote.marketing.MarketingBannerRestApi
import dagger.Binds
import dagger.Module
import dagger.Provides
import okhttp3.OkHttpClient

@Module
abstract class MarketingDataSourceModule {
    @Binds
    @Remote
    abstract fun bindMarketingBannerRemoteDataSource(
        marketingBannerRemoteDataSource: MarketingBannerRemoteDataSource
    ): MarketingBannerDataSource

    @Binds
    @Local
    abstract fun bindMarketingBannerLocalDataSource(
        marketingBannerLocalDataSource: MarketingBannerLocalDataSource
    ): MarketingBannerDataSource

    companion object {
        @Provides
        fun provideMarketingBannerRestApi(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            authProvider: AuthProvider,
            @BaseUrlWithoutPath baseUrl: String,
            @UserAgent userAgent: String,
            moshi: Moshi,
        ): MarketingBannerRestApi {
            return RestApiFactory.buildRestApi(
                sharedClient,
                baseUrl,
                MarketingBannerRestApi::class.java,
                BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
                moshi,
            )
        }
    }
}
