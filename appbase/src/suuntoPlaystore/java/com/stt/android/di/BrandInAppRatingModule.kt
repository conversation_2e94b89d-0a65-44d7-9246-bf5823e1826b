package com.stt.android.di

import com.stt.android.inappreview.InAppReviewTrigger
import com.stt.android.inappreview.InAppReviewTriggerImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class BrandInAppRatingModule {
    @Binds
    abstract fun bindInAppReviewTrigger(inAppReviewTrigger: InAppReviewTriggerImpl): InAppReviewTrigger
}
