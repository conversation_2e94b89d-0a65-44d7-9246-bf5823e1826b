package com.stt.android.di.maps

import com.stt.android.maps.MapsProvider
import com.stt.android.maps.SuuntoMaps
import com.stt.android.maps.mapbox.MapboxMapsProvider
import dagger.Module
import dagger.Provides
import javax.inject.Singleton

@Module(includes = [MapboxMapsAbstractionModule::class])
object MapsAbstractionModule {
    @Provides
    @Singleton
    fun provideSuuntoMaps(mapsProviders: Set<@JvmSuppressWildcards MapsProvider>): SuuntoMaps {
        for (mapsProvider in mapsProviders) {
            SuuntoMaps.addProvider(mapsProvider)
        }
        SuuntoMaps.setDefaultProvider(MapboxMapsProvider.NAME)

        return SuuntoMaps
    }
}
