package com.stt.android.domain.user

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.google.android.gms.maps.GoogleMap
import com.stt.android.R
import com.stt.android.maps.MAP_TYPE_DARK
import com.stt.android.maps.MAP_TYPE_LIGHT
import com.stt.android.maps.MAP_TYPE_SKI

object ProductMapTypes {

    @JvmField
    val SATELLITE = createMapType(
        "SATELLITE",
        GoogleMap.MAP_TYPE_SATELLITE,
        R.string.map_type_satellite,
        R.drawable.map_type_mapbox_satellite
    )

    private val BUILT_IN_MAPS = listOf(
        createMapType(
            "TERRAIN",
            GoogleMap.MAP_TYPE_TERRAIN,
            R.string.map_type_terrain,
            R.drawable.map_type_mapbox_terrain
        ),

        // Only visible when viewing Finland on map or when the user country setting is Finland
        MAANMITTAUSLAITOS_FINLAND_TERRAIN_MAP,
        SATELLITE,
        AVALANCHE,

        createMapType(
            "SKI",
            MAP_TYPE_SKI,
            R.string.map_type_ski,
            R.drawable.map_type_mapbox_ski
        ),

        createMapType(
            "DARK",
            MAP_TYPE_DARK,
            R.string.map_type_dark,
            R.drawable.map_type_mapbox_dark
        ),

        createMapType(
            "LIGHT",
            MAP_TYPE_LIGHT,
            R.string.map_type_light,
            R.drawable.map_type_mapbox_light
        )
    )

    fun getBuiltInMaps() = BUILT_IN_MAPS

    fun getMapTypeByName(mapTypeName: String): MapType? {
        return getBuiltInMaps().find { it.name == mapTypeName }
    }

    private fun createMapType(
        name: String,
        type: Int,
        @StringRes titleResource: Int,
        @DrawableRes iconResource: Int,
        @ColorRes scaleBarTextColorResource: Int = R.color.map_scale_bar_text_color
    ): MapType {
        return MapType(
            name = name,
            googleMapType = type,
            titleResource = titleResource,
            providerText = "Mapbox",
            providerResource = R.string.map_provider_mapbox,
            iconResource = iconResource,
            scaleBarTextColorResource = scaleBarTextColorResource
        )
    }
}
