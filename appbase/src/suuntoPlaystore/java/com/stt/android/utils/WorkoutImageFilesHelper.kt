package com.stt.android.utils

import android.content.Context
import android.graphics.Bitmap
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.ui.tasks.BaseWorkoutImageFilesHelper
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException
import javax.inject.Inject

class WorkoutImageFilesHelper @Inject constructor(
    context: Context,
    dispatchers: CoroutinesDispatchers
) : BaseWorkoutImageFilesHelper(context, dispatchers) {
    override suspend fun saveBitmapToJpegFile(bitmap: Bitmap, dest: File): String =
        withContext(dispatchers.io) {
            dest.outputStream().buffered().use { os ->
                if (bitmap.compress(Bitmap.CompressFormat.JPEG, 90, os)) {
                    FileUtils.calculateMd5(dest.absolutePath)
                } else {
                    throw IOException("Unable to compress bitmap")
                }
            }
        }
}
