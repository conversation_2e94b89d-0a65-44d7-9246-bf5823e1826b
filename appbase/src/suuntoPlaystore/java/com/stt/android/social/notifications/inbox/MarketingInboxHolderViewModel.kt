package com.stt.android.social.notifications.inbox

import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalyticsImpl
import com.stt.android.controllers.FeedController
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.eventtracking.EventTracker
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import javax.inject.Inject

@HiltViewModel
class MarketingInboxHolderViewModel
@Inject constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    feedController: FeedController,
    amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    emarsysAnalyticsImpl: EmarsysAnalyticsImpl,
    eventTracker: EventTracker,
) : BaseMarketingInboxHolderViewModel(
    ioThread,
    mainThread,
    feedController,
    amplitudeAnalyticsTracker,
    emarsysAnalyticsImpl,
    eventTracker,
)
