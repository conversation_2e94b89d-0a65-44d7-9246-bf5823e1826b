package com.stt.android.social.userprofileV2

import android.content.Context
import android.content.Intent
import com.stt.android.domain.user.User
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class UserProfileActivity : SuuntoUserProfileActivity() {


    override fun onContactCustomerServiceClicked() {
        // Do nothing
    }

    override fun onAfterSalesServiceClicked() {
        // Do nothing
    }

    companion object {
        @JvmStatic
        fun newStartIntent(
            context: Context,
            userName: String,
            fromNotification: Boolean
        ): Intent {
            return BaseUserProfileActivity.Companion.newStartIntent(context, userName, fromNotification)
        }

        @JvmStatic
        fun newStartIntent(context: Context, user: User): Intent {
            return BaseUserProfileActivity.Companion.newStartIntent(context, user)
        }
    }
}
