package com.stt.android.ui.rating

import android.content.SharedPreferences
import com.google.common.truth.Truth.assertThat
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.gear.Gear
import com.stt.android.domain.gear.GearRepository
import com.stt.android.domain.user.User
import com.stt.android.domain.user.UserSettings
import com.stt.android.inappreview.InAppReviewSource
import com.stt.android.inappreview.InAppReviewTriggerImpl
import com.stt.android.testutils.NewCoroutinesTestRule
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_ASK_ME_LATER_CLICKED
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_NEVER_SHOW_AGAIN_CLICKED
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_POSITIVE_FEELING_CLICKED
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_TIMESTAMP_WHEN_ASK_ME_LATER_CLICKED
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_TIMESTAMP_WHEN_POSITIVE_FEELING_CLICKED
import com.stt.android.utils.STTConstants.InAppReviewPreferences.KEY_TRIGGER_IN_APP_REVIEW
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.SPARTAN_SPORT_VARIANT_NAME_GLOBAL
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.SUUNTO_9_GEN2_VARIANT_NAME_GLOBAL
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.never
import org.mockito.kotlin.stub
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.util.concurrent.TimeUnit

@RunWith(MockitoJUnitRunner::class)
class InAppReviewTriggerTest {
    @Rule
    @JvmField
    val coroutinesTestRule = NewCoroutinesTestRule()

    private val coroutinesDispatchers = object : CoroutinesDispatchers {
        override val main = coroutinesTestRule.testDispatcher
        override val computation = coroutinesTestRule.testDispatcher
        override val io = coroutinesTestRule.testDispatcher
    }

    @Mock
    private lateinit var sharedPreferences: SharedPreferences

    @Mock
    private lateinit var editor: SharedPreferences.Editor

    @Mock
    private lateinit var currentUserController: CurrentUserController

    @Mock
    private lateinit var userSettingsController: UserSettingsController

    @Mock
    private lateinit var gearRepository: GearRepository

    @Mock
    private lateinit var userSettings: UserSettings

    private val currentTimeMillis = System.currentTimeMillis()

    private lateinit var inAppReviewTrigger: InAppReviewTriggerImpl

    @Before
    fun setup() {
        whenever(sharedPreferences.edit()).thenReturn(editor)

        whenever(
            sharedPreferences.getInt(
                eq(InAppReviewSource.HOME.storageKey),
                any()
            )
        ).thenReturn(20) // 20 sessions
        whenever(
            sharedPreferences.getInt(
                eq(InAppReviewSource.WORKOUT_DETAILS.storageKey),
                any()
            )
        ).thenReturn(3) // Path repeated 3 times
        whenever(
            sharedPreferences.getInt(
                eq(InAppReviewSource.DIARY.storageKey),
                any()
            )
        ).thenReturn(3) // Path repeated 3 times
        whenever(
            sharedPreferences.getInt(
                eq(InAppReviewSource.MAP.storageKey),
                any()
            )
        ).thenReturn(3) // Path repeated 3 times
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_NEVER_SHOW_AGAIN_CLICKED),
                any()
            )
        ).thenReturn(false)
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_ASK_ME_LATER_CLICKED),
                any()
            )
        ).thenReturn(false)
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_POSITIVE_FEELING_CLICKED),
                any()
            )
        ).thenReturn(false)

        whenever(userSettings.disabledAppRatingSuggestions).thenReturn(emptyArray())

        whenever(userSettingsController.settings).thenReturn(userSettings)
        whenever(currentUserController.isLoggedIn).thenReturn(true)
        whenever(currentUserController.currentUser).thenReturn(
            User.loggedIn(
                key = null,
                username = "",
                session = null,
                website = null,
                city = null,
                country = null,
                profileImageUrl = null,
                profileImageKey = null,
                realName = null,
                followModel = null,
                description = null,
                createdDate = currentTimeMillis - TimeUnit.DAYS.toMillis(7 * 7), // 7 weeks on millis
                lastLogin = currentTimeMillis - TimeUnit.DAYS.toMillis(7), // 1 week on millis
                roles = null
            )
        )

        gearRepository.stub {
            onBlocking { fetchAll() }.doReturn(
                listOf(
                    Gear(
                        serialNumber = "",
                        manufacturer = "",
                        name = SUUNTO_9_GEN2_VARIANT_NAME_GLOBAL,
                        softwareVersion = null,
                        hardwareVersion = null,
                        lastSyncDate = null,
                        firstSyncDate = null
                    )
                )
            )
        }

        inAppReviewTrigger = InAppReviewTriggerImpl(
            sharedPreferences = sharedPreferences,
            currentUserController = currentUserController,
            userSettingsController = userSettingsController,
            gearRepository = gearRepository,
            coroutinesDispatchers = coroutinesDispatchers,
        )
    }

    @Test
    fun givenNonLoggedUser_ReviewShouldNotBeScheduled() {
        whenever(currentUserController.isLoggedIn).thenReturn(false)

        inAppReviewTrigger.scheduleInAppReviewIfPossible()

        assertInAppReviewNotScheduled()
    }

    @Test
    fun givenUserWithOldWatch_reviewShouldNotBeScheduled() = runTest {
        whenever(gearRepository.fetchAll()).thenReturn(
            listOf(
                Gear(
                    serialNumber = "",
                    manufacturer = "",
                    name = SPARTAN_SPORT_VARIANT_NAME_GLOBAL,
                    softwareVersion = null,
                    hardwareVersion = null,
                    lastSyncDate = null,
                    firstSyncDate = null
                )
            )
        )

        inAppReviewTrigger.scheduleInAppReviewIfPossible()
        assertInAppReviewNotScheduled()
    }

    @Test
    fun givenNewUser_reviewShouldNotBeScheduled() = runTest {
        whenever(currentUserController.currentUser).thenReturn(
            User.loggedIn(
                key = null,
                username = "",
                session = null,
                website = null,
                city = null,
                country = null,
                profileImageUrl = null,
                profileImageKey = null,
                realName = null,
                followModel = null,
                description = null,
                createdDate = currentTimeMillis - TimeUnit.DAYS.toMillis(7 * 2), // 2 weeks on millis,
                lastLogin = null,
                roles = null
            )
        )

        inAppReviewTrigger.scheduleInAppReviewIfPossible()
        assertInAppReviewNotScheduled()
    }

    @Test
    fun givenNonActiveUser_reviewShouldNotBeScheduled() = runTest {
        whenever(currentUserController.currentUser).thenReturn(
            User.loggedIn(
                key = null,
                username = "",
                session = null,
                website = null,
                city = null,
                country = null,
                profileImageUrl = null,
                profileImageKey = null,
                realName = null,
                followModel = null,
                description = null,
                createdDate = null,
                lastLogin = currentTimeMillis - TimeUnit.DAYS.toMillis(7 * 5), // 5 weeks on millis,
                roles = null
            )
        )

        inAppReviewTrigger.scheduleInAppReviewIfPossible()
        assertInAppReviewNotScheduled()
    }

    @Test
    fun givenUserWithDisabledAppRatingSuggestions_reviewShouldNotBeScheduled() {
        whenever(userSettings.disabledAppRatingSuggestions).thenReturn(listOf("SUUNTO_APP_ANDROID").toTypedArray())

        inAppReviewTrigger.scheduleInAppReviewIfPossible()
        assertInAppReviewNotScheduled()
    }

    @Test
    fun givenUserWithFewSessions_reviewShouldNotBeScheduled() {
        whenever(
            sharedPreferences.getInt(
                eq(InAppReviewSource.HOME.storageKey),
                any()
            )
        ).thenReturn(1)

        inAppReviewTrigger.scheduleInAppReviewIfPossible()
        assertInAppReviewNotScheduled()
    }

    @Test
    fun givenUserThatVisitedWorkoutDetailsOrDiaryOrMapAndDoneSomethingOnlyOnce_reviewShouldNotBeScheduled() {
        whenever(
            sharedPreferences.getInt(
                eq(InAppReviewSource.WORKOUT_DETAILS.storageKey),
                any()
            )
        ).thenReturn(1)
        whenever(
            sharedPreferences.getInt(
                eq(InAppReviewSource.DIARY.storageKey),
                any()
            )
        ).thenReturn(1)
        whenever(
            sharedPreferences.getInt(
                eq(InAppReviewSource.MAP.storageKey),
                any()
            )
        ).thenReturn(1)

        inAppReviewTrigger.scheduleInAppReviewIfPossible()
        assertInAppReviewNotScheduled()
    }

    @Test
    fun givenTheInitialDataInSetupForTheHappyPath_reviewShouldBeScheduled() = runTest {
        inAppReviewTrigger.scheduleInAppReviewIfPossible()
        assertInAppReviewScheduled()
    }

    @Test
    fun givenUserWithNeverShowAgainAlreadyClicked_canShowInAppReviewDialogShouldReturnsFalse() {
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_NEVER_SHOW_AGAIN_CLICKED),
                any()
            )
        ).thenReturn(true)

        assertThat(inAppReviewTrigger.canShowInAppReviewDialog()).isFalse()
    }

    @Test
    fun givenUserWhoClickedAskMeLaterInLessThanWeek_canShowInAppReviewDialogShouldReturnsFalse() {
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_ASK_ME_LATER_CLICKED),
                any()
            )
        ).thenReturn(true)
        whenever(
            sharedPreferences.getLong(
                eq(KEY_TIMESTAMP_WHEN_ASK_ME_LATER_CLICKED),
                any()
            )
        ).thenReturn(currentTimeMillis)

        assertThat(inAppReviewTrigger.canShowInAppReviewDialog()).isFalse()
    }

    @Test
    fun givenUserWhoClickedAskMeLaterInMoreThanWeek_canShowInAppReviewDialogShouldReturnsTrue() {
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_ASK_ME_LATER_CLICKED),
                any()
            )
        ).thenReturn(true)
        whenever(
            sharedPreferences.getLong(
                eq(KEY_TIMESTAMP_WHEN_ASK_ME_LATER_CLICKED),
                any()
            )
        ).thenReturn(currentTimeMillis - TimeUnit.DAYS.toMillis(7 * 10)) // 10 weeks
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_TRIGGER_IN_APP_REVIEW),
                any()
            )
        ).thenReturn(true)

        assertThat(inAppReviewTrigger.canShowInAppReviewDialog()).isTrue()
    }

    @Test
    fun givenUserWhoClickedHappyFeelingLessThanHour_canShowInAppReviewDialogShouldReturnsFalse() {
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_POSITIVE_FEELING_CLICKED),
                any()
            )
        ).thenReturn(true)
        whenever(
            sharedPreferences.getLong(
                eq(KEY_TIMESTAMP_WHEN_POSITIVE_FEELING_CLICKED),
                any()
            )
        ).thenReturn(currentTimeMillis)

        assertThat(inAppReviewTrigger.canShowInAppReviewDialog()).isFalse()
    }

    @Test
    fun givenUserWhoClickedHappyFeelingMoreThanHour_canShowInAppReviewDialogShouldReturnsTrue() {
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_POSITIVE_FEELING_CLICKED),
                any()
            )
        ).thenReturn(true)
        whenever(
            sharedPreferences.getLong(
                eq(KEY_TIMESTAMP_WHEN_POSITIVE_FEELING_CLICKED),
                any()
            )
        ).thenReturn(currentTimeMillis - TimeUnit.HOURS.toMillis(10))
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_TRIGGER_IN_APP_REVIEW),
                any()
            )
        ).thenReturn(true)

        assertThat(inAppReviewTrigger.canShowInAppReviewDialog()).isTrue()
    }

    @Test
    fun canShowInAppReviewDialogIfNotRejectedOrDelayedBefore() {
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_NEVER_SHOW_AGAIN_CLICKED),
                any()
            )
        ).thenReturn(false)
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_ASK_ME_LATER_CLICKED),
                any()
            )
        ).thenReturn(false)
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_POSITIVE_FEELING_CLICKED),
                any()
            )
        ).thenReturn(false)
        whenever(
            sharedPreferences.getBoolean(
                eq(KEY_TRIGGER_IN_APP_REVIEW),
                any()
            )
        ).thenReturn(true)

        assertThat(inAppReviewTrigger.canShowInAppReviewDialog()).isTrue()
    }

    @Test
    fun givenUserWithDisabledAppRatingSuggestions_canShowInAppReviewDialogShouldReturnsFalse() {
        whenever(userSettings.disabledAppRatingSuggestions).thenReturn(listOf("SUUNTO_APP_ANDROID").toTypedArray())

        assertThat(inAppReviewTrigger.canShowInAppReviewDialog()).isFalse()
    }

    private fun assertInAppReviewNotScheduled() =
        verify(sharedPreferences.edit(), never()).putBoolean(
            KEY_TRIGGER_IN_APP_REVIEW,
            true
        )

    private fun assertInAppReviewScheduled() =
        verify(sharedPreferences.edit()).putBoolean(
            KEY_TRIGGER_IN_APP_REVIEW,
            true
        )
}
