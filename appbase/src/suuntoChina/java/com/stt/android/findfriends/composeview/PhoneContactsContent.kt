package com.stt.android.findfriends.composeview

import android.text.TextUtils
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.boundsInParent
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.bodySmallBold
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.core.R
import com.stt.android.findfriends.FollowingState
import com.stt.android.findfriends.PhoneContactsData
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableMap
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.coroutines.launch

@Composable
fun PhoneContactsContent(
    phoneContacts: ImmutableMap<String, ImmutableList<PhoneContactsData>>,
    onFollowStateClick: (PhoneContactsData) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        val lazyListState = rememberLazyListState()
        val coroutineScope = rememberCoroutineScope()
        ListPhoneContact(
            phoneContacts = phoneContacts,
            lazyListState = lazyListState,
            modifier = Modifier
                .fillMaxWidth(),
            onFollowStateClick = onFollowStateClick
        )
        PhoneContactsNameInitial(
            phoneContacts = phoneContacts,
            onInitialChanged = { initial ->
                coroutineScope.launch {
                    var index = 0
                    run {
                        phoneContacts.forEach { (t, u) ->
                            if (initial == t) {
                                return@run
                            }
                            index += 1 + u.size
                        }
                    }
                    lazyListState.scrollToItem(index)
                }
            },
            modifier = Modifier.align(Alignment.CenterEnd)
        )
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun ListPhoneContact(
    phoneContacts: ImmutableMap<String, ImmutableList<PhoneContactsData>>,
    lazyListState: LazyListState,
    onFollowStateClick: (item: PhoneContactsData) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        state = lazyListState
    ) {
        phoneContacts.forEach { (key, value) ->
            stickyHeader {
                Text(
                    text = key,
                    modifier = Modifier
                        .background(MaterialTheme.colors.nearWhite)
                        .padding(MaterialTheme.spacing.smaller)
                        .fillMaxWidth(),
                    style = MaterialTheme.typography.bodyLargeBold.copy(color = MaterialTheme.colors.nearBlack)
                )
            }
            items(value) {
                ItemPhoneContact(item = it, onFollowStateClick)
                Spacer(
                    modifier = Modifier
                        .background(MaterialTheme.colors.dividerColor)
                        .fillMaxWidth()
                        .height(1.dp)
                )
            }
        }
    }
}

@Composable
private fun ItemPhoneContact(
    item: PhoneContactsData,
    onFollowStateClick: (item: PhoneContactsData) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .padding(
                start = MaterialTheme.spacing.smaller,
                top = MaterialTheme.spacing.smaller,
                bottom = MaterialTheme.spacing.smaller,
                end = MaterialTheme.spacing.xlarge
            )
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current).data(item.headUrl)
                .placeholderWithFallback(LocalContext.current, R.drawable.ic_default_profile_image_light)
                .transformations(CircleCropTransformation())
                .build(),
            contentDescription = "",
            modifier = Modifier.size(MaterialTheme.iconSizes.large),
        )
        Column(
            modifier = Modifier
                .weight(1f, true)
                .padding(horizontal = MaterialTheme.spacing.smaller)
        ) {
            Text(
                text = item.name,
                style = MaterialTheme.typography.bodyLarge.copy(color = MaterialTheme.colors.nearBlack)
            )
            Text(
                text = if (TextUtils.isEmpty(item.suuntoName)) {
                    ""
                } else {
                    stringResource(id = com.stt.android.R.string.suunto_username, item.suuntoName)
                },
                style = MaterialTheme.typography.body.copy(color = MaterialTheme.colors.darkGrey)
            )
        }
        Row(
            modifier = Modifier
                .width(100.dp)
                .clip(RoundedCornerShape(MaterialTheme.spacing.medium))
                .border(
                    MaterialTheme.spacing.xxxsmall,
                    MaterialTheme.colors.primary,
                    RoundedCornerShape(MaterialTheme.spacing.medium)
                )
                .clickable {
                    onFollowStateClick.invoke(item)
                }
                .background(
                    if (item.followed()) MaterialTheme.colors.primary else Color.White,
                )
                .padding(MaterialTheme.spacing.small),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            AnimatedVisibility(visible = item.followingState == FollowingState.Followed) {
                Icon(
                    modifier = Modifier.padding(0.dp, 0.dp, MaterialTheme.spacing.xsmall, 0.dp),
                    painter = painterResource(id = com.stt.android.R.drawable.ic_check_white),
                    contentDescription = "",
                    tint = MaterialTheme.colors.nearWhite
                )
            }
            val stateTextResId = when (item.followingState) {
                FollowingState.Followed -> com.stt.android.R.string.following
                FollowingState.Unfollowed -> com.stt.android.R.string.follow
                FollowingState.Requested -> com.stt.android.R.string.requested
                FollowingState.Unregistered -> com.stt.android.R.string.invite_friends_btn_title
                FollowingState.Friends -> com.stt.android.R.string.friends
            }
            val stateTextColor = if (item.followed()) {
                Color.White
            } else {
                MaterialTheme.colors.primary
            }
            Text(
                text = stringResource(id = stateTextResId),
                style = MaterialTheme.typography.bodySmallBold.copy(color = stateTextColor)
            )
        }
    }
}

@Composable
private fun PhoneContactsNameInitial(
    phoneContacts: ImmutableMap<String, ImmutableList<PhoneContactsData>>,
    onInitialChanged: (initial: String) -> Unit,
    modifier: Modifier = Modifier
) {
    val itemBounds = remember { mutableMapOf<String, Rect>() }
    Column(
        modifier
            .pointerInput(phoneContacts) {
                detectDragGestures { change, _ ->
                    itemBounds.forEach { (key, value) ->
                        if (value.contains(change.position)) {
                            onInitialChanged.invoke(key)
                        }
                    }
                }
            }
            .pointerInput(phoneContacts) {
                detectTapGestures(onTap = {
                    itemBounds.forEach { (key, value) ->
                        if (value.contains(it)) {
                            onInitialChanged.invoke(key)
                        }
                    }
                })
            }
    ) {
        phoneContacts.keys.forEach {
            Box(
                modifier = Modifier
                    .size(30.dp, 20.dp)
                    .onGloballyPositioned { layoutCoordinates ->
                        itemBounds[it] = layoutCoordinates.boundsInParent()
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    it,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colors.nearBlack
                )
            }
        }
    }
}

@Preview
@Composable
private fun PhoneContactsContentPreview() {
    val data = persistentMapOf(
        "A" to persistentListOf(PhoneContactsData("Asia", persistentListOf("1369622589"))),
        "B" to persistentListOf(PhoneContactsData("Ben", persistentListOf("1369622584")))
    )
    PhoneContactsContent(data, {} , modifier = Modifier.background(Color.White))
}
