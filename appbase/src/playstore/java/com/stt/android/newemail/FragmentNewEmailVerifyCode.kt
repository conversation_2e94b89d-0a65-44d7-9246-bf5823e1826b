package com.stt.android.newemail

import android.app.Activity
import android.content.Intent
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.base.BaseContentBody
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.COUNT_DOWN_SECONDS
import com.stt.android.compose.widgets.ComposeCountDownTimer
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.compose.widgets.TextFieldInputWithError
import com.stt.android.extensions.hideKeyboard
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class FragmentNewEmailVerifyCode : BaseFragment() {
    private val viewModel: NewEmailCheckViewModel by activityViewModels()
    private val newEmailVerifyCodeArgs: FragmentNewEmailVerifyCodeArgs by navArgs()
    @Composable
    override fun SetContentView() {
        NewEmailVerifyCodeScreen()
    }

    @Composable
    private fun NewEmailVerifyCodeScreen() {
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        LaunchedEffect(uiState.changeEmailSuccess) {
            uiState.changeEmailSuccess?.let {
                Timber.d("token: $it")
                Snackbar.make(
                    requireView(),
                    R.string.change_successful,
                    Snackbar.LENGTH_LONG
                ).apply {
                    addCallback(object : Snackbar.Callback() {
                        override fun onDismissed(transientBottomBar: Snackbar?, event: Int) {
                            activity?.setResult(
                                Activity.RESULT_OK,
                                Intent().putExtra(
                                    NewEmailActivity.EXTRA_EMAIL_KEY,
                                    newEmailVerifyCodeArgs.email
                                )
                            )
                            activity?.finish()
                        }
                    })
                    show()
                }
            }
        }

        BaseContentBody(
            currentFragment = this@FragmentNewEmailVerifyCode,
            viewModel = viewModel,
            onBackClicked = {
                findNavController().popBackStack()
            }
        ) {
            ContentBody(
                isLoading = commonUIState.isLoading,
                verifyCode = uiState.verifyCode,
                verifyCodeIsExpired = uiState.verifyCodeIsExpired,
                email = newEmailVerifyCodeArgs.email,
                onInputVerifyCode = {
                    viewModel.inputVerifyCode(it)
                },
                onVerificationCodeClicked = {
                    viewModel.checkVerificationCode(
                        newEmailVerifyCodeArgs.email,
                        uiState.verifyCode,
                        isModify = true
                    )
                    activity?.hideKeyboard()
                },
                onResendClicked = {
                    viewModel.sendEmailVerificationCode(
                        newEmailVerifyCodeArgs.email,
                    )
                }
            )
        }
    }
}

@Preview
@Composable
private fun BindEmailVerifyCodeScreenPreview() {
    AppTheme {
        Surface {
            ContentBody()
        }
    }
}

@Composable
private fun ContentBody(
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    verifyCode: String = "",
    verifyCodeIsExpired: Boolean = false,
    email: String = "",
    onInputVerifyCode: (String) -> Unit = {},
    onVerificationCodeClicked: () -> Unit = {},
    onResendClicked: () -> Unit = {}
) {
    var timeLeft by rememberSaveable { mutableIntStateOf(COUNT_DOWN_SECONDS) }
    var resendEnable by rememberSaveable { mutableStateOf(false) }
    if (!resendEnable) {
        ComposeCountDownTimer(countDownSeconds = timeLeft, onTick = {
            timeLeft = it
        }, onDone = {
            timeLeft = 0
            resendEnable = true
        })
    }

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top,
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        Spacer(modifier = Modifier.height(height = MaterialTheme.spacing.xlarge))

        Text(
            text = stringResource(id = R.string.enter_the_code_sent_by_email),
            style = MaterialTheme.typography.bodyXLargeBold,
            textAlign = TextAlign.Center,
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.medium
                ),
            horizontalArrangement = Arrangement.Center,
        ) {

            val checkVerificationCodeSummary =
                stringResource(id = com.stt.android.R.string.check_verification_code_summary_from_email, email)
            val startIndex = checkVerificationCodeSummary.indexOf(email)
            val text = buildAnnotatedString {
                append(checkVerificationCodeSummary)
                addStyle(
                    style = SpanStyle(fontWeight = FontWeight.Bold),
                    startIndex,
                    startIndex + email.length
                )
            }
            Text(
                text = text,
                style = MaterialTheme.typography.body,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colors.nearBlack,
            )
        }

        val errorMessage = when {
            verifyCodeIsExpired -> stringResource(id = R.string.verification_code_has_expired)
            else -> ""
        }
        TextFieldInputWithError(
            currentText = verifyCode,
            placeholderText = stringResource(id = R.string.verification_code),
            onActionDone = {
                if (verifyCode.length == VERIFICATION_CODE_LENGTH) {
                    onVerificationCodeClicked.invoke()
                }
            },
            onChanged = { onInputVerifyCode.invoke(it) },
            keyboardType = KeyboardType.Number,
            errorMessage = errorMessage,
            modifier = Modifier.padding(start = MaterialTheme.spacing.medium, end = MaterialTheme.spacing.medium),
            enablePlaceholderTextFloating = false
        )

        PrimaryButton(
            enabled = verifyCode.isNotEmpty() && verifyCode.length == VERIFICATION_CODE_LENGTH,
            onClick = {
                onVerificationCodeClicked.invoke()
            },
            backgroundColor = MaterialTheme.colors.primary,
            text = stringResource(id = R.string.verify_str).uppercase(),
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    top = MaterialTheme.spacing.xlarge,
                    end = MaterialTheme.spacing.medium,
                )
        )

        Text(
            text = "${stringResource(
                id = R.string.did_not_receive_code
            )}\n${stringResource(id = R.string.checking_spam_folder)}",
            style = MaterialTheme.typography.body,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colors.nearBlack,
            modifier = Modifier.padding(top = MaterialTheme.spacing.large)
        )

        TextButton(
            enabled = resendEnable,
            onClick = {
                resendEnable = false
                timeLeft = COUNT_DOWN_SECONDS
                onResendClicked.invoke()
            },
            modifier = Modifier
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.large
                ),
        ) {
            Text(
                text = stringResource(
                    id = R.string.resend_verification_code,
                    if (timeLeft <= 0) "" else "($timeLeft)"
                ),
                style = MaterialTheme.typography.bodyBold,
                textAlign = TextAlign.Center,
                color = if (resendEnable) MaterialTheme.colors.primary else MaterialTheme.colors.cloudyGrey,
            )
        }
    }
    LoadingContent(isLoading)
}
