package com.stt.android.newemail

import com.squareup.moshi.Moshi
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import dagger.Binds
import dagger.Module
import dagger.Provides
import okhttp3.OkHttpClient

@Module
abstract class EmailCheckModule {

    @Binds
    abstract fun bindEmailCheckRemoteDataSource(
        emailCheckRemoteDataSource: EmailCheckRemoteDataSource
    ): EmailCheckDataSource

    companion object {

        @Provides
        fun provideEmailCheck(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            moshi: Moshi
        ): EmailCheckApi {
            return RestApiFactory.buildRestApi(
                sharedClient,
                baseUrl,
                EmailCheckApi::class.java,
                BrandOkHttpConfigFactory.getNoAuthOkHttpConfig(userAgent),
                moshi
            )
        }

        @Provides
        fun provideChangeEmail(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: Moshi
        ): ChangeEmailApi {
            return RestApiFactory.buildRestApi(
                sharedClient,
                baseUrl,
                ChangeEmailApi::class.java,
                BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }
    }
}
