package com.stt.android.newemail

import com.stt.android.exceptions.remote.ClientError
import com.stt.android.exceptions.remote.STTError
import com.stt.android.remote.otp.GenerateOTPUseCase
import javax.inject.Inject

class EmailCheckRemote<PERSON>pi @Inject constructor(
    private val emailCheckApi: <PERSON>ailCheckA<PERSON>,
    private val changeEmailApi: ChangeEmail<PERSON><PERSON>,
    private val generateOTPUseCase: GenerateOTPUseCase
) {
    suspend fun sendEmailVerificationCode(email: String): Any =
        emailCheckApi.sendEmailVerificationCode(generateOTPUseCase.generateTOTP(email), email).payloadOrThrow()

    suspend fun checkEmailVerificationCode(email: String, code: String): String = try {
        emailCheckApi.checkEmailVerificationCode(
            generateOTPUseCase.generateTOTP(email),
            email,
            code
        ).payloadOrThrow()
    } catch (e: ClientError.Forbidden) {
        // We need to do the exception mapping here because BE returns HTTP 403 (Forbidden) if pin code is invalid
        throw STTError.InvalidPinCode()
    }

    suspend fun changeEmail(email: String, token: String): Boolean =
        changeEmailApi.changeEmail(generateOTPUseCase.generateTOTP(email), email, token).payloadOrThrow()

    suspend fun checkEmailExists(email: String): Boolean =
        changeEmailApi.checkEmailExists(generateOTPUseCase.generateTOTP(email), email).payloadOrThrow()

    suspend fun sendLoginEmail(email: String): Boolean =
        changeEmailApi.sendLoginEmail(generateOTPUseCase.generateTOTP(email), email).payloadOrThrow()
}
