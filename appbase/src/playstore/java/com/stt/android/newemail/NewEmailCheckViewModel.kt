package com.stt.android.newemail

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.stt.android.base.BaseHandlerViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.exceptions.remote.ClientError
import com.stt.android.exceptions.remote.STTError
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
open class NewEmailCheckViewModel @Inject constructor(
    private val emailCheckRepository: EmailCheckRepository,
    private val dispatchers: CoroutinesDispatchers,
    private val userSettingsController: UserSettingsController
) : BaseHandlerViewModel() {

    private var email: String? = null

    /**
     * Initializes the ViewModel with the provided email.
     * Ensures that sendEmailVerificationCode is called only once per email.
     */
    fun initialize(email: String, changeEmail: Boolean = false) {
        if (email != this.email) {
            sendEmailVerificationCode(email, changeEmail)
        } else {
            uiState = uiState.copy(sendEmailVerificationCodeSuccess = true)
        }
    }

    var uiState by mutableStateOf(EmailCheckUIState())
        private set

    private val updateEmailHandler = CoroutineExceptionHandler { _, throwable ->
        when (throwable) {
            is ClientError.Conflict -> {
                uiState = uiState.copy(emailError = EmailError.EmailHasBeenRegistered)
                loadingEnd()
            }

            is ClientError.BadRequest -> {  
                uiState = uiState.copy(emailError = EmailError.EmailHasNotChanged)
                loadingEnd()
            }

            else -> {
                loadingEndWithFail(throwable)
            }
        }
        Timber.w(throwable, "message: ${throwable.message}")
    }

    fun clearSendEmailVerificationCodeSuccess() {
        uiState = uiState.copy(sendEmailVerificationCodeSuccess = null)
    }

    fun inputEmail(email: String) {
        uiState = uiState.copy(
            email = email,
            emailError = null,
        )
    }

    fun sendEmailVerificationCode(email: String, changeEmail: Boolean = false) {
        when {
            !isEmailValid(email) -> {
                uiState = uiState.copy(emailError = EmailError.EmailIsError)
            }
            changeEmail && userSettingsController.settings.email == email -> {
                uiState = uiState.copy(emailError = EmailError.EmailHasNotChanged)
            }
            else -> {
                loadingStart()
                viewModelScope.launch(dispatchers.io + updateEmailHandler) {
                    if (changeEmail) {
                        val exists = emailCheckRepository.checkEmailExists(email)
                        if (exists) {
                            uiState = uiState.copy(emailError = EmailError.EmailHasBeenRegistered)
                            loadingEnd()
                            return@launch
                        }
                    }
                    Timber.d("Sending email verification token")
                    emailCheckRepository.sendEmailVerificationCode(email)
                    <EMAIL> = email
                    uiState = uiState.copy(sendEmailVerificationCodeSuccess = true)
                    loadingEnd()
                }
            }
        }
    }

    fun inputVerifyCode(verifyCode: String) {
        uiState = uiState.copy(
            verifyCode = verifyCode,
            verifyCodeIsExpired = false,
        )
    }

    fun checkVerificationCode(email: String, verifyCode: String, isModify: Boolean = false, sendLoginEmail: Boolean = false) {
        loadingStart()
        viewModelScope.launch(
            dispatchers.io + getCheckVerificationCodeFailedHandler {
                uiState = uiState.copy(verifyCodeIsExpired = true)
            }
        ) {
            val emailVerificationToken = emailCheckRepository.checkEmailVerificationCode(email, verifyCode)
            uiState = if (isModify) {
                val result = emailCheckRepository.changeEmail(email, emailVerificationToken)
                if (result) {
                    userSettingsController.storeSettings(
                        userSettingsController.settings.copyWithEmail(
                            email,
                            false
                        )
                    )
                    uiState.copy(changeEmailSuccess = true)
                } else {
                    throw STTError.ModifyEmailFailed()
                }
            } else {
                if (sendLoginEmail) {
                    val result = emailCheckRepository.sendLoginEmail(email)
                    Timber.d("NewEmailCheckViewModel send login email result: $result ")
                }
                uiState.copy(
                    emailVerifyToken = emailVerificationToken,
                )
            }
            loadingEnd()
        }
    }
}

data class EmailCheckUIState(
    val email: String = "",
    val emailError: EmailError? = null,
    val verifyCode: String = "",
    val verifyCodeIsExpired: Boolean = false,
    val emailVerifyToken: String? = null,
    val changeEmailSuccess: Boolean? = null,
    val sendEmailVerificationCodeSuccess: Boolean? = null,
)
