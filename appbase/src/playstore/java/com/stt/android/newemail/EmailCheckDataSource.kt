package com.stt.android.newemail

interface EmailCheckDataSource {

    suspend fun sendEmailVerificationCode(email: String): Any

    suspend fun checkEmailVerificationCode(email: String, pin: String): String

    suspend fun changeEmail(email: String, token: String): Boolean

    suspend fun checkEmailExists(email: String): <PERSON>olean

    suspend fun sendLoginEmail(email: String): <PERSON><PERSON><PERSON>
}
