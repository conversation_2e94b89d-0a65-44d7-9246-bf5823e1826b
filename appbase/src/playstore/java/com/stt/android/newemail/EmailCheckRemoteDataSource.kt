package com.stt.android.newemail

import javax.inject.Inject

class EmailCheckRemoteDataSource @Inject constructor(private val emailCheckRemoteApi: EmailCheckRemoteApi) :
    EmailCheckDataSource {

    override suspend fun sendEmailVerificationCode(email: String) =
        emailCheckRemoteApi.sendEmailVerificationCode(email)

    override suspend fun checkEmailVerificationCode(email: String, pin: String) =
        emailCheckRemoteApi.checkEmailVerificationCode(email, pin)

    override suspend fun changeEmail(email: String, token: String): Boolean =
        emailCheckRemoteApi.changeEmail(email, token)

    override suspend fun checkEmailExists(email: String): Boolean =
        emailCheckRemoteApi.checkEmailExists(email)

    override suspend fun sendLoginEmail(email: String): Bo<PERSON>an =
        emailCheckRemoteApi.sendLoginEmail(email)
}
