package com.stt.android.newemail

import com.stt.android.remote.BaseRemoteApi
import com.stt.android.remote.response.AskoResponse
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface ChangeEmailApi {
    @POST("change/email")
    suspend fun changeEmail(
        @Header(BaseRemoteApi.HEADER_TOTP_KEY) totp: String,
        @Query("email") email: String,
        @Query("token") token: String
    ): AskoResponse<Boolean>

    @GET("user/emailexists/{email}")
    suspend fun checkEmailExists(
        @Header(BaseRemoteApi.HEADER_TOTP_KEY) totp: String,
        @Path("email") email: String
    ): AskoResponse<Boolean>

    @GET("user/sendloginemail/{email}")
    suspend fun sendLoginEmail(
        @Header(BaseRemoteApi.HEADER_TOTP_KEY) totp: String,
        @Path("email") email: String
    ): AskoResponse<Boolean>
}
