package com.stt.android.newemail

import javax.inject.Inject

class EmailCheckRepository @Inject constructor(
    private val emailCheckDataSource: EmailCheckDataSource
) {

    suspend fun sendEmailVerificationCode(email: String) =
        emailCheckDataSource.sendEmailVerificationCode(email)

    suspend fun checkEmailVerificationCode(email: String, code: String) =
        emailCheckDataSource.checkEmailVerificationCode(email, code)

    suspend fun changeEmail(email: String, token: String) =
        emailCheckDataSource.changeEmail(email, token)

    suspend fun checkEmailExists(email: String): Boolean =
        emailCheckDataSource.checkEmailExists(email)

    suspend fun sendLoginEmail(email: String) =
        emailCheckDataSource.sendLoginEmail(email)
}
