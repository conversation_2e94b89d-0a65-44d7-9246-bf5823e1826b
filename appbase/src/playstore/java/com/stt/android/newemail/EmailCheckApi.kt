package com.stt.android.newemail

import com.stt.android.remote.BaseRemoteApi
import com.stt.android.remote.response.AskoResponse
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Query

interface EmailCheckApi {
    @POST("auth/verificationEmail/send")
    suspend fun sendEmailVerificationCode(
        @Header(BaseRemoteApi.HEADER_TOTP_KEY) totp: String,
        @Query("email") email: String
    ): AskoResponse<Any>

    @POST("auth/verificationEmail/verify")
    suspend fun checkEmailVerificationCode(
        @Header(BaseRemoteApi.HEADER_TOTP_KEY) totp: String,
        @Query("email") email: String,
        @Query("pin") pin: String
    ): AskoResponse<String>
}
