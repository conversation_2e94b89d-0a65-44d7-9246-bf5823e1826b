package com.stt.android.newemail

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.stt.android.R
import com.stt.android.base.BaseContentBody
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.compose.widgets.TextFieldInputWithError
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FragmentNewEmail : BaseFragment() {
    private val viewModel: NewEmailCheckViewModel by activityViewModels()

    @Composable
    override fun SetContentView() {
        NewEmailScreen()
    }

    @Composable
    private fun NewEmailScreen() {
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        LaunchedEffect(uiState.sendEmailVerificationCodeSuccess) {
            uiState.sendEmailVerificationCodeSuccess?.let {
                findNavController().navigate(
                    FragmentNewEmailDirections.actionNewEmailVerifyCode().setEmail(uiState.email)
                )
                viewModel.clearSendEmailVerificationCodeSuccess()
            }
        }

        BaseContentBody(
            currentFragment = this@FragmentNewEmail,
            viewModel = viewModel,
            onBackClicked = {
                activity?.finish()
            }
        ) {
            ContentBody(
                isLoading = commonUIState.isLoading,
                email = uiState.email,
                emailError = uiState.emailError,
                onInputEmail = {
                    viewModel.inputEmail(it)
                },
                onBtnClicked = { email ->
                    viewModel.initialize(email, true)
                }
            )
        }
    }
}

@Preview
@Composable
private fun NewEmailScreenPreview() {
    AppTheme {
        Surface {
            ContentBody()
        }
    }
}

@Composable
private fun ContentBody(
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    email: String = "",
    emailError: EmailError? = null,
    onInputEmail: (String) -> Unit = {},
    onBtnClicked: (String) -> Unit = {}
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top,
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xlarge))
        Text(
            text = stringResource(id = R.string.new_email_address),
            style = MaterialTheme.typography.bodyLargeBold,
            color = MaterialTheme.colors.nearBlack,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = MaterialTheme.spacing.xxxxlarge)
        )

        TextFieldInputWithError(
            currentText = email,
            placeholderText = stringResource(id = R.string.email),
            onChanged = { onInputEmail.invoke(it) },
            keyboardType = KeyboardType.Email,
            onActionDone = {
                if (email.isNotEmpty()) {
                    onBtnClicked.invoke(email)
                    keyboardController?.hide()
                }
            },
            errorMessage = emailError?.let { stringResource(id = it.resId) } ?: "",
            modifier = Modifier.padding(start = MaterialTheme.spacing.medium, end = MaterialTheme.spacing.medium),
            enablePlaceholderTextFloating = false
        )

        PrimaryButton(
            enabled = email.isNotEmpty(),
            onClick = {
                onBtnClicked.invoke(email)
            },
            text = stringResource(R.string.change_email),
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.medium
                )
        )
    }
    LoadingContent(isLoading)
}
