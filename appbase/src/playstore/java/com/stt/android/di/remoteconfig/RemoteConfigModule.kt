package com.stt.android.di.remoteconfig

import android.app.Application
import android.content.Context
import android.net.ConnectivityManager
import com.squareup.moshi.Moshi
import com.stt.android.remote.DefaultAskoRemoteConfig
import com.stt.android.remote.RemoteConfigBaseUrl
import com.stt.android.remote.RemoteConfigCacheDirectory
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory.getOfflineCachingOkHttpConfig
import com.stt.android.remote.di.BrandOkHttpConfigFactory.getResponseSourceLoggingOfflineCachingOkHttpConfig
import com.stt.android.remote.di.RestApiFactory.buildRestApi
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse
import com.stt.android.remote.remoteconfig.AskoRemoteConfigRestApi
import com.stt.android.remoteconfig.AskoRemoteConfigDefaults
import com.stt.android.remoteconfig.AskoRemoteConfigDefaults.Companion.singleCondition
import com.stt.android.utils.STTConstants
import dagger.Module
import dagger.Provides
import okhttp3.Cache
import okhttp3.OkHttpClient
import java.io.File

@Module
object RemoteConfigModule {
    private const val CACHE_SIZE = 1024 * 1024 * 2

    @Provides
    @DefaultAskoRemoteConfig
    fun provideAskoRemoteConfigResponse(): AskoRemoteConfigResponse {
        return AskoRemoteConfigResponse(
            // all partner connections enabled by default
            partnerConnectionsEnabled = singleCondition(value = AskoRemoteConfigDefaults.DEFAULT_PARTNER_CONNECTIONS_ENABLED),  // movescount partner connection disabled by default on playstore (this is used in
            // China)
            showMovescountConnection = singleCondition(false),
            // movescountImport partner connection disabled by default
            showMovescountImportConnection = singleCondition(false),
            // movescountImport partner disconnect enabled by default
            showMovescountImportDisconnect = singleCondition(true),
            // sml to backend enabled by default
            smlToBackend = singleCondition(AskoRemoteConfigDefaults.DEFAULT_SML_TO_BACKEND),
            // delete account disabled by default
            deleteAccount = singleCondition(false),
            // st fused location parameters
            stFusedLocationParameters = singleCondition(AskoRemoteConfigDefaults.ST_FUSED_LOCATION_DEFAULTS),
            // companion linking disabled by default
            companionLinking = singleCondition(false),
            // companion linking parameters
            companionLinkingParemeters = singleCondition(AskoRemoteConfigDefaults.COMPANION_LINKING_PARAMETERS_DEFAULTS),
            // Emarsys Enabled
            emarsysEnabled = singleCondition(true),
            // no amplitude event sampling by default
            amplitudeEventSamplingConfig = singleCondition(emptyList()),
            // Chat bot not shown
            androidCustomerSupportChatBotEnabled = singleCondition(false),
            // default Graphhopper base URL
            graphhopperBaseUrl = singleCondition(AskoRemoteConfigDefaults.DEFAULT_GRAPHHOPPER_BASE_URL),
            aiPlannerEnabled = singleCondition(AskoRemoteConfigDefaults.DEFAULT_AI_PLANNER_ENABLED)
        )
    }

    @Provides
    fun provideAskoRemoteConfigRestApi(
        @SharedOkHttpClient sharedClient: OkHttpClient,
        @RemoteConfigBaseUrl baseUrl: String,
        @UserAgent userAgent: String,
        moshi: Moshi,
        @RemoteConfigCacheDirectory cacheDirectoryName: String,
        application: Application
    ): AskoRemoteConfigRestApi {
        val cm = application.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val okHttpConfig =
            if (STTConstants.DEBUG) getResponseSourceLoggingOfflineCachingOkHttpConfig(
                userAgent,
                cm
            ) else getOfflineCachingOkHttpConfig(userAgent, cm)
        return buildRestApi(
            sharedClient,
            baseUrl,
            AskoRemoteConfigRestApi::class.java,
            okHttpConfig,
            moshi,
            Cache(File(application.cacheDir, cacheDirectoryName), CACHE_SIZE.toLong())
        )
    }
}
