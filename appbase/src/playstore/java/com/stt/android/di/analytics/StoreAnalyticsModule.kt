package com.stt.android.di.analytics

// comment-out to enable Tencent SDK in debug and china

import com.stt.android.analytics.tencent.TencentAnalytics
import com.stt.android.analytics.tencent.TencentAnalyticsNoOp
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class StoreAnalyticsModule {
    @Binds
    abstract fun bindTencentAnalytics(tencentAnalyticsNoOp: TencentAnalyticsNoOp): TencentAnalytics
}
