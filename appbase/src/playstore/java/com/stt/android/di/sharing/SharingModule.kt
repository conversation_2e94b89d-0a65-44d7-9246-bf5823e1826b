package com.stt.android.di.sharing

import com.stt.android.sharing.SharingHelper
import com.stt.android.sharing.SharingHelperImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class SharingModule {
    @Binds
    abstract fun bindSharingHelper(sharingHelperImpl: SharingHelperImpl): SharingHelper
}
