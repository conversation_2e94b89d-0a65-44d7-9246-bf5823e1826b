package com.stt.android.ui.fragments.login.terms

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.addCallback
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.stt.android.R
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.databinding.ActivityUpdatedTermsBinding
import com.stt.android.remote.BaseRemoteApi
import com.stt.android.remote.BaseUrlV2
import com.stt.android.remote.otp.GenerateOTPUseCase
import com.stt.android.ui.tasks.LogoutTask
import com.stt.android.utils.FlavorUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class TermsActivity : AppCompatActivity() {
    @Inject
    lateinit var currentUserController: CurrentUserController

    @Inject
    lateinit var generateOTPUseCase: GenerateOTPUseCase

    @Inject
    lateinit var logoutTask: dagger.Lazy<LogoutTask>

    @Inject
    @BaseUrlV2
    lateinit var baseUrlV2: String

    private val viewBinding: ActivityUpdatedTermsBinding by lazy {
        ActivityUpdatedTermsBinding.inflate(layoutInflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(viewBinding.root)

        setSupportActionBar(viewBinding.toolbar)
        supportActionBar?.let {
            it.setDisplayShowHomeEnabled(false)
            it.setDisplayHomeAsUpEnabled(false)
        }

        viewBinding.retry.setOnClickListener { load() }
        initializeWebView()

        onBackPressedDispatcher.addCallback {
            if (viewBinding.webView.canGoBack()) {
                viewBinding.webView.goBack()
            }
        }

        if (savedInstanceState == null) {
            load()
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initializeWebView() = with(viewBinding.webView) {
        settings.javaScriptEnabled = true
        settings.cacheMode = WebSettings.LOAD_NO_CACHE

        webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                if (request?.url?.toString()?.startsWith(CALLBACK_URL) == true) {
                    finish()
                    return true
                }

                return super.shouldOverrideUrlLoading(view, request)
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                onLoadingFinished()
            }

            override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
                super.onReceivedError(view, request, error)
                if (request?.isForMainFrame == true) {
                    onLoadingFailed()
                }
            }
        }

        webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                viewBinding.loadingSpinner.isVisible = newProgress < 100
            }
        }
    }

    private fun load() = with(viewBinding) {
        retry.isVisible = false
        webView.isInvisible = true
        webView.loadUrl(createUpdatedTermsUrl(), createUpdatedTermsHeader())
    }

    private fun onLoadingFailed() = with(viewBinding) {
        webView.isInvisible = true
        retry.isVisible = true
    }

    private fun onLoadingFinished() = with(viewBinding) {
        webView.isVisible = !retry.isVisible
    }

    override fun onDestroy() {
        viewBinding.webView.apply {
            webViewClient = WebViewClient()
            webChromeClient = WebChromeClient()
        }
        super.onDestroy()
    }

    private fun createUpdatedTermsUrl(): String =
        "${baseUrlV2}policyandterms/ask?brand=${brand()}&callbackUrl=$CALLBACK_URL"

    private fun brand(): String = if (FlavorUtils.isSuuntoApp) {
        "SUUNTOAPP"
    } else {
        "SPORTSTRACKER"
    }

    private fun createUpdatedTermsHeader(): Map<String, String> =
        currentUserController.session
            ?.getAuthorizationHeaders()
            ?.apply {
                put(BaseRemoteApi.HEADER_TOTP_KEY, generateOTPUseCase.generateTOTP())
            }
            ?: emptyMap()

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.activity_terms, menu)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean = when (item.itemId) {
        R.id.logout -> {
            AlertDialog.Builder(this)
                .setCancelable(true)
                .setTitle(
                    getString(R.string.dialog_title_settings_service_sign_out, currentUserController.username)
                )
                .setMessage(R.string.dialog_message_settings_service_sign_out)
                .setPositiveButton(R.string.positive_button_settings_service_sign_out) { _, _ ->
                    lifecycleScope.launch {
                        runSuspendCatching {
                            logoutTask.get()
                                .logoutWithProgressDialog(this@TermsActivity, supportFragmentManager)
                                .await()
                        }.onFailure { e ->
                            Timber.w(e, "Error while logging out")
                        }
                    }
                }
                .setNegativeButton(R.string.negative_button_settings_service_sign_out, null)
                .show()
            true
        }
        else -> super.onOptionsItemSelected(item)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        viewBinding.webView.saveState(outState)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        viewBinding.webView.restoreState(savedInstanceState)
    }

    private companion object {
        const val CALLBACK_URL = "suunto://action/agree-tos"
    }
}
