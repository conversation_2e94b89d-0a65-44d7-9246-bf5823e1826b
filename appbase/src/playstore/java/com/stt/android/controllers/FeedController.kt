package com.stt.android.controllers

import android.content.Context
import com.stt.android.domain.notifications.GetNotificationsCountUseCase
import com.stt.android.domain.notifications.GetUnreadNotificationsCountUseCase
import com.stt.android.utils.HuaweiUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FeedController @Inject constructor(
    @ApplicationContext context: Context,
    getNotificationsCountUseCase: GetNotificationsCountUseCase,
    getUnreadNotificationsCountUseCase: GetUnreadNotificationsCountUseCase,
    huaweiUtils: HuaweiUtils,
) : BaseFeedController(
    context,
    getNotificationsCountUseCase,
    getUnreadNotificationsCountUseCase,
    huaweiUtils,
)
