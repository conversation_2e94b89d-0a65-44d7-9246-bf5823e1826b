package com.stt.android.workoutsettings

import android.Manifest
import android.os.Build
import android.os.Bundle
import android.view.View
import com.stt.android.R
import com.stt.android.utils.PermissionUtils
import pub.devrel.easypermissions.EasyPermissions

abstract class ApplyPermissionWorkoutSettingsFragment : BaseWorkoutSettingsFragment() {
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        checkStepsPermission()
    }

    private fun checkStepsPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            PermissionUtils.requestPermissionsIfNeeded(
                this, arrayOf(Manifest.permission.ACTIVITY_RECOGNITION),
                getString(R.string.steps_permission_rationale)
            )
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        // Forward results to EasyPermissions
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this)
    }
}
