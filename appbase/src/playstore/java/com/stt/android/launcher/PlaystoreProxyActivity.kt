package com.stt.android.launcher

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import com.facebook.AccessToken
import com.facebook.CallbackManager
import com.facebook.FacebookAuthorizationException
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.FacebookSdk
import com.facebook.login.LoginBehavior
import com.facebook.login.LoginManager
import com.facebook.login.LoginResult
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks
import com.stt.android.domain.session.EmailVerificationState
import com.stt.android.utils.STTConstants
import rx.Completable
import rx.Subscription
import rx.android.schedulers.AndroidSchedulers
import rx.schedulers.Schedulers
import timber.log.Timber

abstract class PlaystoreProxyActivity : BaseProxyActivity() {

    private var linkWithFbSubscription: Subscription? = null
    private var callbackManager: CallbackManager? = null
    val handler = Handler(Looper.getMainLooper())

    private val facebookLoginCallback: FacebookCallback<LoginResult> =
        object : FacebookCallback<LoginResult> {
            override fun onSuccess(result: LoginResult) {
                val token = result.accessToken.token
                linkWithFbSubscription?.unsubscribe()
                linkWithFbSubscription = Completable.fromCallable {
                    currentUserController.linkWithFacebook(token)
                }
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(
                        {
                            Timber.d("Facebook access token refreshed")
                            showProxyActivity()
                        },
                        {
                            Timber.i(it, "Error on linking with facebook with token $token")
                            handleFacebookLoginError()
                        }
                    )
            }

            override fun onCancel() {
                handleFacebookLoginError()
            }

            override fun onError(error: FacebookException) {
                if (error is FacebookAuthorizationException) {
                    LoginManager.getInstance().logInWithReadPermissions(
                        this@PlaystoreProxyActivity,
                        STTConstants.FB_READ_PERMISSION_LIST
                    )
                } else {
                    Timber.w(error, "Error on facebook login for expired access token")
                    handleFacebookLoginError()
                }
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (isLoginToFacebookNeeded()) {
            return
        }
        handleDynamicLinks()
    }

    @Deprecated("Deprecated in Java")
    public override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        callbackManager?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onStop() {
        super.onStop()
        handler.removeCallbacksAndMessages(null)
        linkWithFbSubscription?.unsubscribe()
        if (callbackManager != null) {
            LoginManager.getInstance().unregisterCallback(callbackManager)
        }
        callbackManager = null
    }

    override fun isLoginToFacebookNeeded(): Boolean {
        if (!FacebookSdk.isInitialized()) {
            return false
        }

        val accessToken = AccessToken.getCurrentAccessToken()
        return (accessToken != null && accessToken.isExpired)
    }

    override fun loginToFacebook(): Boolean {
        try {
            if (callbackManager == null) {
                callbackManager = CallbackManager.Factory.create()
                LoginManager.getInstance().setLoginBehavior(LoginBehavior.NATIVE_WITH_FALLBACK)
                LoginManager.getInstance().registerCallback(callbackManager, facebookLoginCallback)
            }
            handler.postDelayed({ handleFacebookLoginError() }, FACEBOOK_TOKEN_REFRESH_TIMEOUT)

            Timber.d("Facebook access token expired, start login")
            LoginManager.getInstance()
                .logInWithReadPermissions(this, STTConstants.FB_READ_PERMISSION_LIST)
            return true
        } catch (e: Exception) {
            Timber.w(e, "Error launching facebook login for expired access token")
        }
        return false
    }

    private fun handleDynamicLinks() {
        FirebaseDynamicLinks.getInstance()
            .getDynamicLink(intent)
            .addOnSuccessListener(this) { pendingDynamicLinkData ->
                // pendingDynamicLinkData can effectively be null
                if (pendingDynamicLinkData != null) {
                    val deepLink = pendingDynamicLinkData.link
                    if (deepLink != null && !handleCustomInboxUri(deepLink)) {
                        handleInstallReferrerUri(deepLink)
                    }
                }
            }
            .addOnFailureListener(this) { exception ->
                Timber.d(
                    exception,
                    "getDynamicLink:onFailure"
                )
            }
    }

    private fun handleFacebookLoginError() {
        LoginManager.getInstance().logOut()
        showProxyActivity()
    }

    private fun showProxyActivity() {
        val newIntent = newStartIntentClearStack(applicationContext)
        newIntent.data = intent.data
        startActivity(newIntent)
        finish()
        /* don't show any animation for the activity being started, otherwise the user will see
        two activity started animations (one for the current ProxyActivity, and other one for the
         activity being started)
         */
        overridePendingTransition(0, 0)
    }

    override fun userIsNotLoggedIn(): Boolean = super.userIsNotLoggedIn() ||
        currentUserController.currentUser.session?.emailVerificationState == EmailVerificationState.NOT_VERIFIED

    companion object {
        private const val FACEBOOK_TOKEN_REFRESH_TIMEOUT = 30000L // 30 seconds
    }
}
