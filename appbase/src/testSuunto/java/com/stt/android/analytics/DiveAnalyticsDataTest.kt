package com.stt.android.analytics

import com.stt.android.analytics.DiveAnalyticsData.Companion.batteryValueFromJsonEntry
import com.stt.android.analytics.DiveAnalyticsData.Companion.voltageValueFromJsonEntry
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test

class DiveAnalyticsDataTest {

    private val batteryJsonEntry = "Charge: 87%, Voltage: 4.054V"

    @Test
    fun `correct extraction of battery value`() {
        assertThat(batteryValueFromJsonEntry(null)).isNull()
        assertThat(batteryValueFromJsonEntry(batteryJsonEntry)).isEqualTo(87)
    }

    @Test
    fun `correct extraction of voltage value`() {
        assertThat(voltageValueFromJsonEntry(null)).isNull()
        assertThat(voltageValueFromJsonEntry(batteryJsonEntry)).isEqualTo(4054)
    }
}
