package com.stt.android.home.diary;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;
import com.stt.android.R;
import com.stt.android.common.ui.ViewPagerFragmentCreator;
import java.lang.ref.WeakReference;

class DiaryPagerAdapter extends FragmentStatePagerAdapter {
    static final int PAGE_LIST = 0;
    static final int PAGE_TSS_ANALYSIS = 1;
    static final int PAGE_SUMMARY = 2;
    private static final int PAGE_COUNT = 3;

    private final WeakReference[] fragments = new WeakReference[PAGE_COUNT];
    private final Resources resources;
    private final ViewPagerFragmentCreator tssFragmentCreator;
    private final ViewPagerFragmentCreator trainingZoneSummaryFragmentCreator;

    DiaryPagerAdapter(
        FragmentManager fm,
        Resources resources,
        @Nullable ViewPagerFragmentCreator tssFragmentCreator,
        @Nullable ViewPagerFragmentCreator trainingZoneSummaryFragmentCreator
    ) {
        super(fm, FragmentStatePagerAdapter.BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        this.resources = resources;
        this.tssFragmentCreator = tssFragmentCreator;
        this.trainingZoneSummaryFragmentCreator = trainingZoneSummaryFragmentCreator;
    }

    @Override
    public int getCount() {
        if (tssFragmentCreator != null) {
            return PAGE_COUNT;
        } else {
            return PAGE_COUNT - 1; // No Progress tab if feature toggle disabled
        }
    }

    @NonNull
    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        Fragment fragment = (Fragment) super.instantiateItem(container, position);
        fragments[position] = new WeakReference<>(fragment);
        return fragment;
    }

    @NonNull
    @Override
    public Fragment getItem(int position) {
        switch (position) {
            case PAGE_LIST:
                return DiaryWorkoutListFragment.newInstance();
            case PAGE_TSS_ANALYSIS:
                assert tssFragmentCreator != null; // Should never be called if null
                return tssFragmentCreator.create(new Bundle());

            case PAGE_SUMMARY:
                assert trainingZoneSummaryFragmentCreator != null; // Should never be called if null
                return trainingZoneSummaryFragmentCreator.create(new Bundle());
            default:
                throw new IllegalStateException("Unknown position - " + position);
        }
    }

    @Override
    public CharSequence getPageTitle(int position) {
        switch (position) {
            case PAGE_LIST:
                return resources.getString(R.string.list);
            case PAGE_TSS_ANALYSIS:
                return resources.getString(R.string.progress_title);
            case PAGE_SUMMARY:
                return resources.getString(R.string.summary_tab_title);
        }
        throw new IllegalStateException("Unknown position - " + position);
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        fragments[position] = null;
        super.destroyItem(container, position, object);
    }

    @SuppressWarnings("unchecked")
    @Nullable
    Fragment getFragment(int position) {
        WeakReference<Fragment> reference = fragments[position];
        return reference == null ? null : reference.get();
    }
}
