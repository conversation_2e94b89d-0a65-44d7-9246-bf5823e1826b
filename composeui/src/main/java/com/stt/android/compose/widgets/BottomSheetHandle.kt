package com.stt.android.compose.widgets

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bottomSheetShape
import com.stt.android.compose.theme.spacing
import com.stt.android.core.R

@Composable
fun DraggableBottomSheetHandle(
    modifier: Modifier = Modifier,
    topPadding: Dp = MaterialTheme.spacing.smaller,
    bottomPadding: Dp = MaterialTheme.spacing.smaller,
) {
    // This is never really scrollable, but use verticalScroll modifier
    // so that ViewInteropNestedScrollConnection forwards scroll events
    // to the bottom sheet dialog fragment allowing it to be closed by dragging.
    BottomSheetHandle(
        modifier = modifier.verticalScroll(rememberScrollState()),
        topPadding = topPadding,
        bottomPadding = bottomPadding
    )
}

@Composable
fun BottomSheetHandle(
    modifier: Modifier = Modifier,
    topPadding: Dp = MaterialTheme.spacing.smaller,
    bottomPadding: Dp = MaterialTheme.spacing.smaller,
) {
    Image(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                top = topPadding,
                bottom = bottomPadding
            ),
        painter = painterResource(id = R.drawable.ic_handle),
        contentDescription = null
    )
}

@Preview(widthDp = 320, heightDp = 480)
@Composable
private fun BottomSheetHandlePreview() {
    AppTheme {
        Surface(shape = MaterialTheme.shapes.bottomSheetShape) {
            Column {
                BottomSheetHandle()
            }
        }
    }
}
