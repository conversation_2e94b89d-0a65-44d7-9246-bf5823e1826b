package com.stt.android.sportmode.composables

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.snapping.rememberSnapFlingBehavior
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.coerceAtLeast
import androidx.compose.ui.unit.dp
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.sportmode.R
import com.stt.android.sportmode.datascreen.DataScreenList
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.SCREEN_SIZE
import com.stt.android.sportmode.datascreen.content.DataScreenScreen
import com.stt.android.sportmode.datascreen.testDataScreens
import com.stt.android.sportmode.modesetting.baseDrawable
import kotlin.math.roundToInt

internal val WATCH_DISPLAY_SIZE = SCREEN_SIZE.dp - 4.dp
internal val WATCH_SHELL_HEIGHT = 309.dp
internal val WATCH_SHELL_WIDTH = 230.dp
private val ITEM_HEIGHT = 210.dp

@Composable
fun TrainingModeDataScreenContent(
    dataScreenList: DataScreenList,
    displayListState: LazyListState,
    onEditDisplayClick: () -> Unit,
    onIndexSelect: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    val density = LocalDensity.current
    val itemHeightDp = ITEM_HEIGHT
    val layoutInfo by remember { derivedStateOf { displayListState.layoutInfo } }
    val paddingTop = itemHeightDp / 3
    val paddingBottom =
        (with(density) { layoutInfo.viewportSize.height.toDp() } - itemHeightDp - paddingTop)
            .coerceAtLeast(0.dp)
    val showEditButton = dataScreenList.notFixedDataScreens.any() && dataScreenList.selectedDataScreen.items.any()
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = MaterialTheme.spacing.medium)
        ) {
            DisplayDataScreens(
                displayListState = displayListState,
                dataScreenList = dataScreenList,
                onIndexSelect = onIndexSelect,
                onAddClick = onEditDisplayClick,
                canClickAdd = !showEditButton,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                paddingTop = paddingTop,
                paddingBottom = paddingBottom,
                itemHeightDp = itemHeightDp,
            )
        }

        if (showEditButton) {
            PrimaryButton(
                text = stringResource(id = R.string.sport_mode_display_edit),
                onClick = onEditDisplayClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .padding(
                        horizontal = MaterialTheme.spacing.large,
                        vertical = MaterialTheme.spacing.medium,
                    )
            )
        }
    }
}

@Composable
fun DisplayDataScreens(
    displayListState: LazyListState,
    dataScreenList: DataScreenList,
    onIndexSelect: (Int) -> Unit,
    onAddClick: () -> Unit,
    canClickAdd: Boolean,
    modifier: Modifier = Modifier,
    paddingTop: Dp = 0.dp,
    paddingBottom: Dp = 0.dp,
    itemHeightDp: Dp = WATCH_SHELL_HEIGHT,
) {
    val density = LocalDensity.current
    val layoutInfo by remember { derivedStateOf { displayListState.layoutInfo } }
    val itemCount = dataScreenList.notFixedDataScreens.size
    fun relatePaddingTop(height: Dp, paddingTop: Dp, itemHeightDp: Dp): Dp {
        return (paddingTop - (height - itemHeightDp) / 2).coerceAtLeast(0.dp)
    }
    val watchPaddingTop = remember(paddingTop, itemHeightDp) {
        relatePaddingTop(WATCH_SHELL_HEIGHT, paddingTop, itemHeightDp)
    }
    val watchBackgroundPaddingTop = remember(paddingTop, itemHeightDp) {
        relatePaddingTop(WATCH_DISPLAY_SIZE, paddingTop, itemHeightDp)
    }
    val editing = remember { dataScreenList.notFixedDataScreens.any { it.editing } }

    val userScrolling = remember { mutableStateOf(false) }
    LaunchedEffect(displayListState.isScrollInProgress) {
        userScrolling.value = displayListState.isScrollInProgress
    }
    LaunchedEffect(dataScreenList, dataScreenList.selectedIndex) {
        if (!userScrolling.value) {
            displayListState.scrollToItem(dataScreenList.selectedIndex)
        }
    }

    Box(
        modifier = modifier.fillMaxWidth()
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    top = watchBackgroundPaddingTop,
                )
        ) {
            Box(
                modifier = Modifier
                    .size(WATCH_DISPLAY_SIZE)
                    .background(Color.Black, shape = CircleShape)
                    .align(Alignment.Center)
            )
        }

        if (editing) {
            WatchShell(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = watchPaddingTop)
            )
        }

        if (itemCount > 0) {
            LazyColumn(
                state = displayListState,
                flingBehavior = rememberSnapFlingBehavior(displayListState),
                contentPadding = PaddingValues(top = paddingTop, bottom = paddingBottom),
                modifier = Modifier
                    .align(Alignment.Center)
                    .fillMaxHeight()
            ) {
                items(itemCount) { index ->
                    val viewportStart = layoutInfo.viewportStartOffset
                    val adjustedViewportHeight =
                        layoutInfo.viewportSize.height - ((paddingBottom - paddingTop).value * density.density).roundToInt()
                    val centerPosition = viewportStart + adjustedViewportHeight / 2

                    val selectedIndex by remember {
                        derivedStateOf {
                            layoutInfo.visibleItemsInfo.find { item ->
                                val itemTop = item.offset
                                val itemBottom = item.offset + item.size
                                centerPosition in itemTop..itemBottom
                            }?.index ?: 0
                        }
                    }

                    LaunchedEffect(selectedIndex) {
                        onIndexSelect(selectedIndex)
                    }

                    val item = layoutInfo.visibleItemsInfo.find { it.index == index }
                    val alpha = if (item != null) {
                        if (item.index == selectedIndex) {
                            1f
                        } else {
                            0.3f
                        }
                    } else {
                        0.3f
                    }

                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(itemHeightDp)
                            .alpha(alpha)
                    ) {
                        if (dataScreenList.notFixedDataScreens[index].items.none()) {
                            var addDisplayModifier = Modifier
                                .size(WATCH_DISPLAY_SIZE)
                                .background(Color.Black, shape = CircleShape)
                                .align(Alignment.Center)
                            if (canClickAdd) {
                                addDisplayModifier = addDisplayModifier.clickableThrottleFirst {
                                    onAddClick()
                                }
                            }
                            Image(
                                painter = painterResource(id = baseDrawable.add_display),
                                contentDescription = null,
                                contentScale = ContentScale.Inside,
                                modifier = addDisplayModifier
                            )
                        } else {
                            Box(
                                modifier = Modifier
                                    .background(Color.Black, shape = CircleShape)
                                    .align(Alignment.Center)
                            ) {
                                DataScreenScreen(
                                    list = dataScreenList.notFixedDataScreens[index],
                                    modifier = Modifier.size(WATCH_DISPLAY_SIZE)
                                )
                            }
                        }
                    }
                }
            }
        }

        if (!editing) {
            WatchShell(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = watchPaddingTop)
            )
        }

        Box(
            modifier = Modifier
                .height(WATCH_SHELL_HEIGHT)
                .padding(top = watchPaddingTop)
        ) {
            DisplayListIndicator(
                listState = displayListState,
                pageCount = dataScreenList.notFixedDataScreens.size,
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = MaterialTheme.spacing.xlarge),
            )
        }
    }
}

@Composable
private fun WatchShell(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
    ) {
        Image(
            painter = painterResource(
                id = baseDrawable.watch_activity_suunto_run
            ),
            contentDescription = null,
            modifier = Modifier
                .width(WATCH_SHELL_WIDTH)
                .height(WATCH_SHELL_HEIGHT)
                .align(Alignment.Center)
        )
    }
}

@Composable
private fun DisplayListIndicator(
    listState: LazyListState,
    pageCount: Int,
    modifier: Modifier = Modifier,
    activeColor: Color = MaterialTheme.colors.primary,
    inactiveColor: Color = MaterialTheme.colors.mediumGrey,
    indicatorHeight: Dp = 8.dp,
    indicatorWidth: Dp = indicatorHeight,
    spacing: Dp = indicatorHeight,
    indicatorShape: Shape = CircleShape,
) {
    val density = LocalDensity.current
    val indicatorHeightPx = density.run { indicatorHeight.roundToPx() }
    val spacingPx = density.run { spacing.roundToPx() }
    val itemWithSpace = indicatorHeightPx + spacingPx

    val scrollProgress by remember {
        derivedStateOf {
            val layoutInfo = listState.layoutInfo
            val totalItemCount = layoutInfo.totalItemsCount.takeIf { it > 0 } ?: pageCount
            val itemSize =
                layoutInfo.visibleItemsInfo.firstOrNull()?.size?.takeIf { it > 0 } ?: itemWithSpace
            val scroll =
                listState.firstVisibleItemIndex + (listState.firstVisibleItemScrollOffset / itemSize.toFloat())
            (scroll / (totalItemCount - 1).coerceAtLeast(1)).coerceIn(0f, 1f)
        }
    }

    val offset by remember(scrollProgress) {
        mutableStateOf(
            { IntOffset(0, (scrollProgress * itemWithSpace * (pageCount - 1)).roundToInt()) }
        )
    }

    Box(
        modifier = modifier,
        contentAlignment = Alignment.TopCenter
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(spacing),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            val indicatorModifier = Modifier
                .size(width = indicatorWidth, height = indicatorHeight)
                .background(color = inactiveColor, shape = indicatorShape)

            repeat(pageCount) {
                Box(indicatorModifier)
            }
        }

        if (pageCount > 0) {
            Box(
                Modifier
                    .offset { offset() }
                    .size(width = indicatorWidth, height = indicatorHeight)
                    .background(color = activeColor, shape = indicatorShape)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun TrainingModeDataScreenContentPreview() {
    AppTheme {
        TrainingModeDataScreenContent(
            dataScreenList = DataScreenList(
                dataScreens = testDataScreens,
                selectedIndex = 2
            ),
            displayListState = rememberLazyListState(),
            onEditDisplayClick = {},
            onIndexSelect = {},
            modifier = Modifier.height(600.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun TrainingModeDataScreenContentPreview1() {
    AppTheme {
        TrainingModeDataScreenContent(
            dataScreenList = DataScreenList(
                dataScreens = testDataScreens.takeLast(1),
                selectedIndex = 0
            ),
            displayListState = rememberLazyListState(),
            onEditDisplayClick = {},
            onIndexSelect = {},
            modifier = Modifier.height(600.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun DisplayDataScreensPreview() {
    AppTheme {
        DisplayDataScreens(
            dataScreenList = DataScreenList(
                dataScreens = testDataScreens,
                selectedIndex = 1
            ),
            displayListState = rememberLazyListState(),
            onIndexSelect = {},
            onAddClick = {},
            canClickAdd = false,
            modifier = Modifier.height(WATCH_SHELL_HEIGHT),
        )
    }
}


