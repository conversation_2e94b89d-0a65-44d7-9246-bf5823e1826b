package com.stt.android.sportmode.modesetting.autolap

import android.content.Context
import android.os.Parcelable
import androidx.annotation.StringRes
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.sportmode.entity.EntityMapperConstants.STATUS_ON
import com.stt.android.sportmode.entity.EntityMapperConstants.AUTOLAP_TYPE_DISTANCE
import com.stt.android.sportmode.entity.EntityMapperConstants.AUTOLAP_TYPE_DURATION
import com.stt.android.sportmode.entity.EntityMapperConstants.AUTOLAP_TYPE_LOCATION
import com.stt.android.sportmode.modesetting.ModeSetting
import com.stt.android.sportmode.modesetting.ModeSettingReducer
import com.stt.android.sportmode.modesetting.autolap.reducer.AutolapDistanceWheelIndexReducer
import com.stt.android.sportmode.modesetting.autolap.reducer.AutolapDurationWheelIndexReducer
import com.stt.android.sportmode.modesetting.baseStr
import com.stt.android.sportmode.modesetting.coreStr
import com.stt.android.sportmode.modesetting.distanceWheelTextListLeft
import com.stt.android.sportmode.modesetting.distanceWheelTextListRight
import com.stt.android.sportmode.modesetting.exception.NotAvailableAutolapTypeException
import com.stt.android.sportmode.modesetting.hourWheelTextList
import com.stt.android.sportmode.modesetting.distanceToWheelIndices
import com.stt.android.sportmode.modesetting.minutesWheelTextList
import com.stt.android.sportmode.modesetting.resString
import com.stt.android.sportmode.modesetting.secondsPerHour
import com.stt.android.sportmode.modesetting.secondsPerMinute
import com.stt.android.sportmode.modesetting.secondsToWheelIndices
import com.stt.android.sportmode.modesetting.secondsWheelTextList
import com.stt.android.sportmode.modesetting.sportStr
import com.stt.android.sportmode.modesetting.supportsModeType
import com.stt.android.sportmode.trainingmode.formatInDistance
import com.stt.android.home.settings.wheel.DisableDoneStrategy
import com.stt.android.home.settings.wheel.WheelPickerColumn
import com.stt.android.home.settings.wheel.WheelPickerData
import com.suunto.connectivity.runsportmodes.entities.AutoLap
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.math.roundToInt

interface AutolapReducer : ModeSettingReducer<Autolap>

@Parcelize
data class Autolap(
    val enable: Boolean = false,
    val autolapTypes: List<AutolapType> = listOf(
        AutolapType.Duration(seconds = DEFAULT_MINUTES),
        AutolapType.Distance(meters = DEFAULT_METERS),
        AutolapType.Location(),
    )
) : ModeSetting {
    @IgnoredOnParcel
    override val nameRes: Int = sportStr.autolap

    @IgnoredOnParcel
    val autolapType: AutolapType =
        autolapTypes.find { it.checked } ?: throw NotAvailableAutolapTypeException()

    override fun summary(context: Context, infoModelFormatter: InfoModelFormatter): String =
        if (!enable) {
            baseStr.off.resString(context)
        } else {
            when (autolapType) {
                is AutolapType.Duration -> {
                    infoModelFormatter.formatDuration(autolapType.seconds.toDouble()).getOrNull()?.value ?: ""
                }
                is AutolapType.Distance -> run {
                    val meters = (autolapType as AutolapType.Distance).meters.toDouble()
                    val result = infoModelFormatter.formatDistanceFourdigits(meters).getOrNull() ?: return@run ""
                    val roundedValue = BigDecimal(result.value).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString()
                    "$roundedValue ${result.unitResId?.resString(context)}"
                }
                is AutolapType.Location -> autolapType.title.resString(context)
            }
        }

    companion object {
        const val DEFAULT_MINUTES = 30 * secondsPerMinute
        const val DEFAULT_METERS = 1000f

        fun fromEntity(autoLap: AutoLap): Autolap {
            val durationChecked = autoLap.autolapEnum == AUTOLAP_TYPE_DURATION || autoLap.autolapEnum == 0
            val distanceChecked = autoLap.autolapEnum == AUTOLAP_TYPE_DISTANCE
            val autolapTypes = buildList {
                if (autoLap.visibleSubItem.supportsModeType(AUTOLAP_TYPE_DURATION)) {
                    add(
                        AutolapType.Duration(
                            checked = durationChecked,
                            seconds = autoLap.data.roundToInt().takeIf { durationChecked && it > 0 }
                                ?: DEFAULT_MINUTES
                        )
                    )
                }
                if (autoLap.visibleSubItem.supportsModeType(AUTOLAP_TYPE_DISTANCE)) {
                    add(
                        AutolapType.Distance(
                            checked = distanceChecked,
                            meters = (autoLap.data * 100) // unit is 100m
                                .takeIf { distanceChecked && it > 0 } ?: DEFAULT_METERS
                        )
                    )
                }
                if (autoLap.visibleSubItem.supportsModeType(AUTOLAP_TYPE_LOCATION)) {
                    add(
                        AutolapType.Location(
                            checked = autoLap.autolapEnum == AUTOLAP_TYPE_LOCATION
                        )
                    )
                }
            }

            // Check if there is any `AutolapType` with `checked = true`
            // If none, set the first item to `checked = true`
            val checkFirstIfNeeded = if (autolapTypes.any() && autolapTypes.none { it.checked }) {
                autolapTypes.first().let {
                    when (it) {
                        is AutolapType.Duration -> it.copy(checked = true)
                        is AutolapType.Distance -> it.copy(checked = true)
                        is AutolapType.Location -> it.copy(checked = true)
                    }
                }
            } else null

            return Autolap(
                enable = autoLap.status == STATUS_ON,
                autolapTypes = autolapTypes.mapIndexed { index, autolapType ->
                    if (index == 0 && checkFirstIfNeeded != null) checkFirstIfNeeded else autolapType
                }
            )
        }
    }
}

sealed interface AutolapType : Parcelable {
    val autolapTypeId: Int
    @get:StringRes
    val title: Int
    val checked: Boolean

    @Parcelize
    data class Duration(
        override val checked: Boolean = true,
        val seconds: Int,
    ) : AutolapType {
        @IgnoredOnParcel
        override val autolapTypeId: Int = AUTOLAP_TYPE_DURATION

        @IgnoredOnParcel
        override val title: Int = sportStr.autolap_switch_duration

        @IgnoredOnParcel
        val range: Pair<Int, Int> = 1 to 100 * secondsPerHour - 1

        @IgnoredOnParcel
        val hourIndex = seconds.secondsToWheelIndices[0]

        @IgnoredOnParcel
        val minuteIndex = seconds.secondsToWheelIndices[1]

        @IgnoredOnParcel
        val secondIndex = seconds.secondsToWheelIndices[2]

        internal fun generateWheelData(
            context: Context,
            onReducer: (AutolapReducer) -> Unit
        ): WheelPickerData {
            return WheelPickerData(
                columns = listOf(
                    WheelPickerColumn(
                        textList = hourWheelTextList,
                        defaultIndex = hourIndex,
                        unit = coreStr.hour.resString(context),
                        onSelect = { i ->
                            onReducer(
                                AutolapDurationWheelIndexReducer(
                                    listOf(
                                        i,
                                        minuteIndex,
                                        secondIndex
                                    )
                                )
                            )
                        }
                    ),
                    WheelPickerColumn(
                        textList = minutesWheelTextList,
                        defaultIndex = minuteIndex,
                        unit = coreStr.minute.resString(context),
                        onSelect = { i ->
                            onReducer(
                                AutolapDurationWheelIndexReducer(
                                    listOf(
                                        hourIndex,
                                        i,
                                        secondIndex
                                    )
                                )
                            )
                        }
                    ),
                    WheelPickerColumn(
                        textList = secondsWheelTextList,
                        defaultIndex = secondIndex,
                        unit = coreStr.sec.resString(context),
                        onSelect = { i ->
                            onReducer(
                                AutolapDurationWheelIndexReducer(
                                    listOf(
                                        hourIndex,
                                        minuteIndex,
                                        i
                                    )
                                )
                            )
                        }
                    ),
                ),
                disableDoneStrategy = DisableDoneStrategy.AtLeast30Seconds,
            )
        }
    }

    @Parcelize
    data class Distance(
        override val checked: Boolean = false,
        val meters: Float
    ) : AutolapType {
        @IgnoredOnParcel
        override val autolapTypeId: Int = AUTOLAP_TYPE_DISTANCE

        @IgnoredOnParcel
        override val title: Int = sportStr.autolap_switch_distance

        @IgnoredOnParcel
        val range: Pair<Int, Int> = 0 to 990

        internal fun generateWheelData(
            context: Context,
            measurementUnit: MeasurementUnit,
            onReducer: (AutolapReducer) -> Unit
        ): WheelPickerData {
            val value = meters.formatInDistance(measurementUnit)
            val indices = value.roundToInt().distanceToWheelIndices
            val leftTextList = distanceWheelTextListLeft
            val rightTextList = distanceWheelTextListRight
            val firstIndex = indices[0].coerceIn(0, leftTextList.lastIndex)
            val secondIndex = indices[1].coerceIn(0, rightTextList.lastIndex)

            return WheelPickerData(
                columns = listOf(
                    WheelPickerColumn(
                        textList = leftTextList,
                        defaultIndex = firstIndex,
                        unit = ".",
                        onSelect = { i ->
                            onReducer(AutolapDistanceWheelIndexReducer(listOf(i, secondIndex), measurementUnit))
                        }
                    ),
                    WheelPickerColumn(
                        textList = rightTextList,
                        defaultIndex = secondIndex,
                        unit = measurementUnit.distanceUnit.resString(context),
                        onSelect = { i ->
                            onReducer(AutolapDistanceWheelIndexReducer(listOf(firstIndex, i), measurementUnit))
                        }
                    ),
                ),
                disableDoneStrategy = DisableDoneStrategy.Zero
            )
        }
    }

    @Parcelize
    data class Location(
        override val checked: Boolean = false,
    ) : AutolapType {
        @IgnoredOnParcel
        override val autolapTypeId: Int = AUTOLAP_TYPE_LOCATION

        @IgnoredOnParcel
        override val title: Int = sportStr.autolap_switch_location
    }
}
