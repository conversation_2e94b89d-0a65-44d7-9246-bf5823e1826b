package com.stt.android.sportmode.selectmode

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.progressSemantics
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.ListItem
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.SnackbarHost
import androidx.compose.material.SnackbarHostState
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.TopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.disabledColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingDialog
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.core.utils.EventThrottler
import com.stt.android.core.utils.onClick
import com.stt.android.sportmode.composables.WatchStateSnackbar
import com.stt.android.sportmode.entity.TrainingModeBase
import com.stt.android.sportmode.home.SportHeader
import com.stt.android.sportmode.modesetting.sportStr
import com.stt.android.sportmode.trainingmode.LoadingState
import java.util.Locale

@Composable
fun SelectModeScreen(
    sportHeader: SportHeader,
    onBackClick: (Int) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: SelectModeViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val modes by viewModel.stateFlow.collectAsState()

    val unableToEditReason by viewModel.sportModeConnectionWrapper.unableToEditReasonFlow.collectAsState(null)

    val loadingState by viewModel.loadingStateFlow.collectAsState()
    val loading = loadingState == LoadingState.LOADING
    val loadingMore = loadingState == LoadingState.LOADING_MORE

    LaunchedEffect(Unit) {
        viewModel.retrieve(sportHeader)
    }
    SelectModeContent(
        title = stringResource(sportHeader.titleId),
        modes = modes,
        onBackClick = {
            onBackClick(viewModel.updatedModeCount)
        },
        onSaveClick = {
            viewModel.save()
        },
        onEditingEnabled = {
            viewModel.onEnableEditing(it)
        },
        onItemClick = {
            viewModel.editTrainingMode(context, it)
        },
        onDeleteClick = {
            viewModel.deleteMode(it)
        },
        onCreateNewModeClick = {
            viewModel.createTrainingMode(context)
        },
        onLoadMore = {
            viewModel.loadMore()
        },
        modifier = modifier,
        loading = loading,
        loadingMore = loadingMore,
        unableToEditReason = unableToEditReason,
    )
}

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun SelectModeContent(
    title: String,
    modes: List<TrainingModeHeader>,
    onBackClick: () -> Unit,
    onSaveClick: () -> Unit,
    onEditingEnabled: (Boolean) -> Unit,
    onItemClick: (TrainingModeHeader) -> Unit,
    onDeleteClick: (Int) -> Unit,
    onCreateNewModeClick: () -> Unit,
    onLoadMore: () -> Unit,
    modifier: Modifier = Modifier,
    loading: Boolean = false,
    loadingMore: Boolean = false,
    unableToEditReason: Int? = null,
) {
    val eventThrottler = remember { EventThrottler() }
    val snackBarHostState = remember { SnackbarHostState() }
    val editing = modes.any { it.state == TrainingModeHeaderState.EDITING }
    fun back() = run {
        if (editing) {
            onEditingEnabled(false)
        } else {
            onBackClick()
        }
    }
    BackHandler { back() }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = title.uppercase(Locale.getDefault()),
                    )
                },
                navigationIcon = {
                    SuuntoIconButton(
                        icon = SuuntoIcons.ActionBack,
                        onClick = { back() },
                        contentDescription = stringResource(R.string.back),
                    )
                },
                actions = {
                    if (!editing) {
                        TopBarAction(
                            text = stringResource(id = R.string.edit).uppercase(Locale.getDefault()),
                            enabled = unableToEditReason == null,
                            onClick = {
                                onEditingEnabled(true)
                            }
                        )
                    } else {
                        TopBarAction(
                            text = stringResource(id = R.string.done).uppercase(Locale.getDefault()),
                            enabled = unableToEditReason == null,
                            onClick = {
                                onSaveClick()
                            }
                        )
                    }
                },
                backgroundColor = MaterialTheme.colors.surface,
            )
        },
        snackbarHost = {
            SnackbarHost(hostState = snackBarHostState)
        },
        modifier = modifier
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .fillMaxSize()
                .narrowContent()
                .background(MaterialTheme.colors.surface)
        ) {
            LazyColumn(modifier = Modifier.weight(1f)) {
                items(modes.size) { index ->
                    val trainingMode = modes[index]
                    key(trainingMode) {
                        TrainingItem(
                            trainingMode = trainingMode,
                            onItemClick = {
                                onItemClick(it)
                            },
                            onDeleteClick = {
                                onDeleteClick(it.id)
                            },
                            enabled = unableToEditReason == null,
                        )
                    }

                    if (index == modes.lastIndex && !loadingMore && !loading) {
                        onLoadMore()
                    }
                }

                if (loadingMore) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(MaterialTheme.spacing.medium)
                                .wrapContentWidth(Alignment.CenterHorizontally)
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier
                                    .progressSemantics()
                                    .size(MaterialTheme.iconSizes.small),
                                color = MaterialTheme.colors.primary,
                                strokeWidth = 2.dp,
                            )
                        }
                    }
                }
            }

            if (!editing) {
                PrimaryButton(
                    text = stringResource(id = sportStr.sport_mode_create_new_mode),
                    enabled = unableToEditReason == null,
                    onClick = eventThrottler.onClick(onCreateNewModeClick),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(MaterialTheme.spacing.large)
                )
            }
        }

        if (loading) {
            LoadingDialog(
                hintText = stringResource(id = R.string.please_wait),
                progressIndicatorColor = MaterialTheme.colors.primary,
            )
        }

        WatchStateSnackbar(unableToEditReason, snackBarHostState)
    }
}

@Composable
private fun TopBarAction(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
) {
    TextButton(
        onClick = onClick,
        enabled = enabled,
        modifier = modifier,
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodyBold,
            color = if (enabled) MaterialTheme.colors.primary else MaterialTheme.colors.disabledColor
        )
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun TrainingItem(
    trainingMode: TrainingModeHeader,
    onItemClick: ((TrainingModeHeader) -> Unit),
    onDeleteClick: ((TrainingModeHeader) -> Unit),
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
) {
    if (trainingMode.state == TrainingModeHeaderState.HIDDEN) return
    val enable = enabled && (trainingMode.state == TrainingModeHeaderState.IDLE
        || trainingMode.baseModeId != TrainingModeBase.FREE_TRAINING.id
        || !trainingMode.isDefault)
    val alpha = if (enable) 1f else 0.5f
    Column(
        modifier = modifier
            .clickableThrottleFirst(
                enabled = enable && trainingMode.state != TrainingModeHeaderState.EDITING,
                onClick = { onItemClick.invoke(trainingMode) }
            )
    ) {
        ListItem(
            icon = {
                Icon(
                    painter = painterResource(trainingMode.iconResId),
                    contentDescription = null,
                    modifier = Modifier
                        .size(MaterialTheme.iconSizes.large)
                        .padding(MaterialTheme.spacing.small),
                    tint = MaterialTheme.colors.nearBlack.copy(alpha = alpha)
                )
            },
            text = {
                Text(
                    text = trainingMode.title,
                    style = MaterialTheme.typography.bodyLarge,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    color = MaterialTheme.colors.nearBlack.copy(alpha = alpha)
                )
            },
            secondaryText = {
                Text(
                    text = trainingMode.subTitle,
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colors.darkGrey.copy(alpha = alpha)
                )
            },
            trailing = {
                when (trainingMode.state) {
                    TrainingModeHeaderState.EDITING -> {
                        if (enable) {
                            IconButton(onClick = {
                                onDeleteClick(trainingMode)
                            }) {

                                Icon(
                                    painter = painterResource(R.drawable.icon_delete),
                                    contentDescription = null,
                                    modifier = Modifier.size(MaterialTheme.iconSizes.medium),
                                    tint = Color.Unspecified
                                )
                            }
                        }
                    }
                    TrainingModeHeaderState.IDLE -> {
                        Icon(
                            painter = painterResource(R.drawable.chevron_right),
                            contentDescription = null,
                            modifier = Modifier.size(MaterialTheme.iconSizes.medium)
                        )
                    }
                    else -> {}
                }
            },
        )

        Divider()
    }
}

@Preview(showBackground = true)
@Composable
private fun SelectModeContentPreview(
    @PreviewParameter(UnableToEditReasonProvider::class) unableToEditReason: Int?
) {
    AppTheme {
        SelectModeContent(
            title = "Running",
            modes = TrainingModeBase.entries.mapIndexed { index, entry ->
                TrainingModeHeader(
                    id = entry.defaultModeId,
                    baseModeId = entry.id,
                    iconResId = entry.iconResId,
                    title = entry.name.repeat(3),
                    subTitle = "Default",
                    isDefault = entry.defaultModeId in (TrainingModeBase.entries.map { it.defaultModeId }),
                    state = if (index < 3) TrainingModeHeaderState.EDITING else TrainingModeHeaderState.IDLE
                )
            },
            onBackClick = {},
            onSaveClick = {},
            onEditingEnabled = {},
            onItemClick = {},
            onDeleteClick = {},
            onCreateNewModeClick = {},
            onLoadMore = {},
            loadingMore = true,
            unableToEditReason = unableToEditReason,
        )
    }
}

private class UnableToEditReasonProvider : PreviewParameterProvider<Int?> {
    override val values: Sequence<Int?>
        get() = sequenceOf(null, R.string.watch_is_syncing)
}
