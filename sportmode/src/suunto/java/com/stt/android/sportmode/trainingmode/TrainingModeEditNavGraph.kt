package com.stt.android.sportmode.trainingmode

import android.app.Activity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.sportmode.trainingmode.TrainingModeDestinations.TRAINING_COMPETITION_TARGET
import com.stt.android.sportmode.trainingmode.TrainingModeDestinations.TRAINING_MODE_EDIT
import com.stt.android.sportmode.trainingmode.competition.CompetitionTargetSelectScreen
import com.stt.android.ui.components.workout.WorkoutCardViewModel

@Suppress("ktlint:compose:vm-forwarding-check")
@Composable
fun TrainingModeEditNavGraph(
    rewriteNavigator: WorkoutDetailsRewriteNavigator,
    saveAndExit: (TrainingMode?) -> Unit,
    modifier: Modifier = Modifier,
    trainingModeEditViewModel: TrainingModeEditViewModel = hiltViewModel(),
    workoutCardViewModel: WorkoutCardViewModel = hiltViewModel(),
) {
    val navController = rememberNavController()
    val animateDurationMillis = 300
    val context = LocalContext.current
    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            navController.popBackStack()
        } else {
            trainingModeEditViewModel.targetWorkoutSelected(null)
        }
    }
    NavHost(
        navController = navController,
        startDestination = TRAINING_MODE_EDIT,
        modifier = modifier,
        enterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(animateDurationMillis)
            )
        },
        exitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(animateDurationMillis)
            )
        },
        popEnterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(animateDurationMillis)
            )
        },
        popExitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(animateDurationMillis)
            )
        },
    ) {
        composable(TRAINING_MODE_EDIT) {
            TrainingModeEditScreen(
                modifier = Modifier,
                onSaveClick = saveAndExit,
                onBackClick = {
                    saveAndExit.invoke(null)
                },
                navigateCompetitionTargetSelect = {
                    navController.navigate(
                        TRAINING_COMPETITION_TARGET
                    )
                },
                viewModel = trainingModeEditViewModel,
            )
        }
        composable(TRAINING_COMPETITION_TARGET) {
            CompetitionTargetSelectScreen(
                onCloseClicked = { navController.popBackStack() },
                viewModel = trainingModeEditViewModel,
                onItemClicked = { targetWorkout ->
                    launcher.launch(
                        rewriteNavigator.createIntent(
                            context = context,
                            username = targetWorkout.workoutHeader.username,
                            workoutId = targetWorkout.workoutHeader.id,
                            workoutKey = targetWorkout.workoutHeader.key,
                            analyticsSource = "",
                            isFromNotification = false,
                            showCommentsDialog = false,
                            hideBarInfo = true
                        )
                    )
                    trainingModeEditViewModel.targetWorkoutSelected(targetWorkout)
                },
                workoutCardViewModel = workoutCardViewModel,
            )
        }
    }
}
