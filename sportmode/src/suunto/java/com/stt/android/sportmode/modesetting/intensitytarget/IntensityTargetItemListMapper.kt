package com.stt.android.sportmode.modesetting.intensitytarget

import android.content.Context
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.sportmode.modesetting.ModeSettingItemList
import com.stt.android.sportmode.modesetting.ModeSettingItemListMapper
import com.stt.android.sportmode.modesetting.ModeSettingReducer
import com.stt.android.sportmode.modesetting.intensitytarget.reducer.IntensityTargetCheckZoneReducer
import com.stt.android.sportmode.modesetting.intensitytarget.reducer.IntensityTargetClickRangeReducer
import com.stt.android.sportmode.modesetting.intensitytarget.reducer.IntensityTargetSelectTypeReducer
import com.stt.android.sportmode.modesetting.intensitytarget.reducer.IntensityTargetSwitchReducer
import com.stt.android.sportmode.modesetting.list.KeyValueItem
import com.stt.android.sportmode.modesetting.list.RadioButtonItem
import com.stt.android.sportmode.modesetting.list.SwitchItem
import com.stt.android.sportmode.modesetting.resString
import com.stt.android.sportmode.modesetting.sportStr
import javax.inject.Inject

interface IntensityTargetItemListMapper : ModeSettingItemListMapper<IntensityTarget>

class IntensityTargetItemListMapperImpl @Inject constructor(
    private val context: Context,
    private val infoModelFormatter: InfoModelFormatter,
) : IntensityTargetItemListMapper {
    override var onReducer: (ModeSettingReducer<IntensityTarget>) -> Unit = {}
    override suspend fun invoke(mode: IntensityTarget): ModeSettingItemList {
        if (!mode.enable) {
            return ModeSettingItemList(
                title = sportStr.intensity_target.resString(context),
                itemList = listOf(
                    SwitchItem(
                        title = sportStr.intensity_target_switch.resString(context),
                        description = sportStr.intensity_target_switch_tips.resString(context),
                        checked = false,
                        onCheck = {
                            onReducer.invoke(IntensityTargetSwitchReducer())
                        },
                    )
                )
            )
        }
        return ModeSettingItemList(
            title = sportStr.intensity_target.resString(context),
            itemList = listOf(
                SwitchItem(
                    title = sportStr.intensity_target_switch.resString(context),
                    description = sportStr.intensity_target_switch_tips.resString(context),
                    checked = true,
                    onCheck = {
                        onReducer.invoke(IntensityTargetSwitchReducer())
                    },
                ),
                KeyValueItem(
                    key = sportStr.intensity_target_zone_type.resString(context),
                    value = mode.zoneType.name.resString(context),
                    onClick = { context ->
                        onReducer.invoke(IntensityTargetSelectTypeReducer(context))
                    }
                ),
                KeyValueItem(
                    key = sportStr.intensity_target_target_zone.resString(context),
                    value = mode.zoneType.toSummary(),
                    onClick = { context ->
                        onReducer.invoke(
                            IntensityTargetClickRangeReducer(
                                context,
                                infoModelFormatter
                            )
                        )
                    }
                ),
            ) + if (mode.zoneType.zones.size == 1) {
                emptyList()
            } else {
                mode.zoneType.zones.mapIndexed { index, intensityZone ->
                    RadioButtonItem(
                        index = index,
                        title = intensityZone.toSummary(mode.zoneType.unit, mode.zoneType),
                        checked = index == mode.zoneType.checkedZoneIndex,
                        onCheck = { cxt, i, duplicated ->
                            onReducer.invoke(
                                IntensityTargetCheckZoneReducer(
                                    context = cxt,
                                    infoModelFormatter = infoModelFormatter,
                                    index = i,
                                    duplicatedCheck = duplicated,
                                )
                            )
                        },
                    )
                }
            }
        )
    }

    private fun IntensityZone.toSummary(unit: Int, zoneType: ZoneType): String {
        if (zoneId == sportStr.intensity_target_zone_custom) {
            return zoneId.resString(context)
        }
        val formattedRange = rangeSummary(infoModelFormatter, zoneType.formatterZoneType)
        return "${zoneId.resString(context)}: $formattedRange ${unit.resString(context)}"
    }

    private fun ZoneType.toSummary(): String = run {
        val zone = zones[checkedZoneIndex]
        val rangeSummary = zone.rangeSummary(infoModelFormatter, formatterZoneType)
        val summary = "$rangeSummary ${unit.resString(context)}"

        if (hasSuggestZones) {
            "${zone.zoneId.resString(context)}: $summary"
        } else {
            summary
        }
    }
}
