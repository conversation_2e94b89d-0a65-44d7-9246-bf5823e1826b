package com.stt.android.sportmode.datascreen

import androidx.activity.result.ActivityResultLauncher
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.DataOptionMapping
import com.stt.android.sportmode.datascreen.DataScreenEditActivity.Companion.EXTRA_DATA_SCREENS
import com.stt.android.sportmode.datascreen.DataScreenEditActivity.Companion.EXTRA_SPORT_ID
import com.stt.android.sportmode.datascreen.fields.DataOptionsFieldsActivity
import com.stt.android.sportmode.datascreen.options.DataOptionsActivity
import com.stt.android.sportmode.datascreen.options.EditDataOptions
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.coroutines.Continuation
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

@HiltViewModel
class DataScreenEditViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val measurementUnit: MeasurementUnit,
) : ViewModel() {

    private var initialDataScreenList: DataScreenList = DataScreenList()

    private val _dataScreensFlow by lazy {
        MutableStateFlow(initialDataScreenList)
    }

    internal val dataScreensFlow by lazy {
        _dataScreensFlow.asStateFlow()
    }

    private lateinit var dataOptionsLauncher: ActivityResultLauncher<EditDataOptions>
    private lateinit var dataOptionsFieldsLauncher: ActivityResultLauncher<Int>
    private var dataOptionsContinuation: Continuation<EditDataOptions>? = null
    private var activityId = ActivityType.RUNNING.id

    fun onActivityCreate(activity: FragmentActivity) {
        val dataScreenList = savedStateHandle.get<DataScreenList>(EXTRA_DATA_SCREENS)
            ?: throw UnsupportedOperationException("no edit display in intent")
        activityId = savedStateHandle.get<Int>(EXTRA_SPORT_ID)
            ?: throw UnsupportedOperationException("no sport id in intent")
        initialDataScreenList = dataScreenList.copy(
            dataScreens = dataScreenList.dataScreens.map {
                it.copy(editing = true)
            }
        )
        initialDataOptionsFieldsLauncher(activity)
        initialDataOptionsLauncher(activity)
        if (initialDataScreenList.selectedDataScreen.items.none()) {
            changeFields(0)
        }
    }

    fun changeFields(fields: Int) {
        dataOptionsFieldsLauncher.launch(fields)
    }

    private fun initialDataOptionsFieldsLauncher(activity: FragmentActivity) {
        dataOptionsFieldsLauncher =
            activity.registerForActivityResult(DataOptionsFieldsActivity.ResultContract()) { fields ->
                reduce {
                    reduceByFields(fields)
                }
            }
    }

    private fun DataScreenList.reduceByFields(fields: Int): DataScreenList {
        if (selectedDataScreen.items.size == fields) return this
        val updated = copy(
            dataScreens = dataScreens.map { watchData ->
                if (watchData === selectedDataScreen) {
                    val currentSize = watchData.items.size
                    watchData.copy(
                        items = if (fields < currentSize) {
                            watchData.items.subList(0, fields)
                        } else if (fields > currentSize) {
                            watchData.items + (1..fields - currentSize).map {
                                watchData.items.lastOrNull()
                                    ?: DataScreenComponent.fromMapping(DataOptionMapping.BATTERY)
                            }
                        } else {
                            watchData.items
                        }
                    )
                } else {
                    watchData
                }
            }
        )
        return if (updated.dataScreens.none { it.items.none() } && updated.dataScreens.size < MAX_DATA_SCREEN_SIZE) {
            updated.copy(
                dataScreens = updated.dataScreens + DataScreen(editing = true)
            )
        } else {
            updated
        }
    }

    private fun initialDataOptionsLauncher(activity: FragmentActivity) {
        dataOptionsLauncher =
            activity.registerForActivityResult(DataOptionsActivity.ResultContract()) { dataOptions ->
                dataOptionsContinuation?.resume(dataOptions)
            }
    }

    fun updateSelectedIndex(index: Int) {
        viewModelScope.launch {
            reduce {
                copy(selectedIndex = index)
            }
        }
    }

    fun changeWatchDataItem(changeIndex: Int, data: DataScreenComponent) {
        viewModelScope.launch {
            val updatedDataOptions = suspendCoroutine {
                dataOptionsContinuation = it
                dataOptionsLauncher.launch(
                    EditDataOptions(
                        activityId = activityId,
                        dataOptionId = data.id,
                    )
                )
            }
            dataOptionsContinuation = null
            val updatedWatchDataItem =
                updatedDataOptions.mapComponent(isImperial = measurementUnit == MeasurementUnit.IMPERIAL)
            reduce {
                copy(
                    dataScreens = dataScreens.map { watchData ->
                        if (watchData === selectedDataScreen) {
                            watchData.copy(
                                items = watchData.items.mapIndexed { index, watchDataItem ->
                                    if (index == changeIndex) {
                                        updatedWatchDataItem
                                    } else {
                                        watchDataItem
                                    }
                                }
                            )
                        } else {
                            watchData
                        }
                    }
                )
            }
        }
    }

    fun delete() {
        val size = _dataScreensFlow.value.dataScreens.size
        reduce {
            val updatedDataScreens = (dataScreens - selectedDataScreen).toMutableList()
            if (updatedDataScreens.none { it.items.none() }) {
                updatedDataScreens += DataScreen(editing = true)
            }
            copy(
                dataScreens = updatedDataScreens.toList(),
                selectedIndex = selectedIndex.coerceAtMost(size - 2)
            )
        }
    }

    fun generateDataScreenListToSave(dataScreenList: DataScreenList): DataScreenList {
        // make editing false
        var toSave = dataScreenList.copy(
            dataScreens = dataScreenList.dataScreens.map {
                it.copy(
                    editing = false
                )
            }
        )
        // new data screen created
        if (toSave.dataScreens.find { it.items.none() } == null && toSave.dataScreens.size < MAX_DATA_SCREEN_SIZE) {
            toSave = toSave.copy(
                dataScreens = toSave.dataScreens + DataScreen(editing = false)
            )
        }
        return toSave
    }

    private fun reduce(reduce: DataScreenList.() -> DataScreenList) {
        _dataScreensFlow.value = _dataScreensFlow.value.reduce()
    }
}
