package com.stt.android.data.source.local.weatherextension

import androidx.room.Dao
import androidx.room.Query
import com.stt.android.data.source.local.TABLE_WEATHER_EXTENSION
import com.stt.android.data.source.local.workoutextension.WorkoutExtensionDao
import io.reactivex.Maybe

@Dao
interface WeatherExtensionDao : WorkoutExtensionDao<LocalWeatherExtension> {

    @Query(
        """
        SELECT *
        FROM $TABLE_WEATHER_EXTENSION
        """
    )
    fun fetchAll(): Maybe<List<LocalWeatherExtension>>

    @Query(
        """
        SELECT *
        FROM $TABLE_WEATHER_EXTENSION
        WHERE workoutId = :id
        """
    )
    fun findById(id: Int): Maybe<LocalWeatherExtension>

    @Query(
        """
        SELECT *
        FROM $TABLE_WEATHER_EXTENSION
        WHERE workoutId IN (:ids)
        """
    )
    fun findByIds(ids: Collection<Int>): List<LocalWeatherExtension>
}
