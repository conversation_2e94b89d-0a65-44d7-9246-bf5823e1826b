package com.stt.android.data.source.local.notifications

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.stt.android.data.source.local.TABLE_NOTIFICATION
import kotlinx.coroutines.flow.Flow

@Dao
interface NotificationDao {
    @Query("SELECT * FROM $TABLE_NOTIFICATION ORDER BY ${LocalNotification.TIMESTAMP} DESC")
    fun readAll(): Flow<List<LocalNotification>>

    @Query("SELECT COUNT(*) FROM $TABLE_NOTIFICATION")
    fun count(): Flow<Int>

    @Query("SELECT COUNT(*) FROM $TABLE_NOTIFICATION WHERE ${LocalNotification.READ} = 0")
    fun unreadCount(): Flow<Int>

    @Transaction
    suspend fun replace(notifications: List<LocalNotification>) {
        deleteAll()
        insertOrUpdate(notifications)
    }

    @Query("DELETE FROM $TABLE_NOTIFICATION")
    fun deleteAll()

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdate(notifications: List<LocalNotification>)
}
