package com.stt.android.watch.debug.di

import com.stt.android.common.ui.IntentResolverKey
import com.stt.android.intentresolver.IntentKey
import com.stt.android.intentresolver.IntentResolver
import com.stt.android.watch.debug.datasource.DebugDataSource
import com.stt.android.watch.debug.datasource.WatchDebugDataSource
import com.stt.android.watch.debug.ui.setlocation.SetDebugLocationActivity
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoMap
import kotlinx.coroutines.FlowPreview

@Module
@InstallIn(SingletonComponent::class)
abstract class WatchDebugModule {

    @Binds
    abstract fun bindWatchDebugDataSource(
        dataSource: WatchDebugDataSource
    ): DebugDataSource

    companion object {
        @FlowPreview
        @Provides
        @IntoMap
        @IntentResolverKey(IntentKey.DebugWatchLocation::class)
        fun provideDebugWatchLocationFromKeyIntentResolver(): IntentResolver<*> {
            return SetDebugLocationActivity.getDebugWatchLocationIntentResolver()
        }
    }
}
