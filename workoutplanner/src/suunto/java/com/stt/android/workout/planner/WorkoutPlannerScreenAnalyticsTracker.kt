package com.stt.android.workout.planner

import androidx.annotation.IdRes
import androidx.navigation.NavController
import com.stt.android.analytics.AnalyticsPropertyValue
import javax.inject.Inject

class WorkoutPlannerScreenAnalyticsTracker @Inject constructor() {
    private var source: String? = null
    private var viewModel: WorkoutPlannerViewModel? = null
    private var navController: NavController? = null
    private var restoringState = false

    @IdRes
    private var prevDestination: Int? = null

    private val navListener = NavController.OnDestinationChangedListener { _, destination, _ ->
        when {
            restoringState -> {
                // Don't send any analytics event when restoring the navigation graph from saved state
            }

            destination.id == R.id.plannedWorkoutsListFragment && source != null -> {
                // Send WorkoutPlannerScreen event
                viewModel?.sendPlannerScreenEvent(source!!)

                // Only send WorkoutPlannerScreen once unless user creates a new plan or exits
                // the planner
                source = null
            }

            destination.id == R.id.workoutPlanStepsListFragment &&
                prevDestination != R.id.summaryFragment -> {
                // Send WorkoutPlannerPlanStarted event
                viewModel?.sendWorkoutPlannerPlanStartedAnalyticsEvent()
            }

            destination.id == R.id.summaryFragment &&
                prevDestination == R.id.workoutPlanStepsListFragment -> {
                // Send WorkoutPlannerStepCompleted event for steps
                viewModel?.sendWorkoutPlannerStepCompletedAnalyticsEvent(
                    AnalyticsPropertyValue.WorkoutPlanner.PLANNER_STEP_STEPS
                )
            }
        }

        prevDestination = destination.id
    }

    fun setup(
        navController: NavController,
        viewModel: WorkoutPlannerViewModel,
        source: String?,
        restoringFromSavedState: Boolean
    ) {
        restoringState = restoringFromSavedState
        this.viewModel = viewModel
        this.navController = navController
        this.source = source.takeUnless { restoringFromSavedState }
        navController.addOnDestinationChangedListener(navListener)
        restoringState = false
    }

    fun destroy() {
        navController?.removeOnDestinationChangedListener(navListener)
        navController = null
        viewModel = null
    }

    fun setSource(source: String?) {
        this.source = source
    }
}
