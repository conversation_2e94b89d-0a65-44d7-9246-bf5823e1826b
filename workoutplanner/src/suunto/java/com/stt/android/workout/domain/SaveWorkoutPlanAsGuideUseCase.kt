package com.stt.android.workout.domain

import android.content.Context
import com.soy.algorithms.planner.WorkoutPlanner
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.squareup.moshi.Moshi
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuide
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusGuideRemoteDataSource
import com.stt.android.workout.planner.R
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import javax.inject.Inject

class SaveWorkoutPlanAsGuideUseCase
@Inject constructor(
    private val appContext: Context,
    private val remoteDataSource: SuuntoPlusGuideRemoteDataSource,
    moshi: <PERSON><PERSON>,
) {
    private val manifestAdapter = moshi.adapter(GuideManifest::class.java)

    suspend fun updateExistingPlan(
        guideId: SuuntoPlusGuideId,
        guideJson: String,
        name: String,
        description: String,
    ): SuuntoPlusGuide {
        val guideZip = buildGuideZipPackage(
            guideJson = guideJson,
            name = name,
            description = description
        )

        return remoteDataSource.updateGuide(guideId, guideZip)
    }

    suspend fun savePlanAsGuide(
        guideJson: String,
        name: String,
        description: String,
    ): SuuntoPlusGuide {
        val guideZip = buildGuideZipPackage(
            guideJson = guideJson,
            name = name,
            description = description
        )

        return remoteDataSource.uploadGuide(guideZip)
    }

    private suspend fun buildGuideZipPackage(
        guideJson: String,
        name: String,
        description: String,
    ): ByteArray = withContext(IO) {
        val manifest = manifestAdapter.toJson(
            GuideManifest(
                name = name,
                type = "sequence",
                owner = WorkoutPlanner.SUUNTO_PLANNER_OWNER,
                description = description
            )
        )

        // Guide ZIP files are small enough so we can easily handle them in RAM in a byte buffer
        val zipBuffer = ByteArrayOutputStream()
        ZipOutputStream(zipBuffer).use { zos ->
            zos.putNextEntry(ZipEntry("manifest.json"))
            zos.write(manifest.toByteArray())
            zos.closeEntry()

            zos.putNextEntry(ZipEntry("guide.json"))
            zos.write(guideJson.toByteArray())
            zos.closeEntry()

            zos.putNextEntry(ZipEntry("icon.png"))
            zos.write(
                appContext.resources.openRawResource(R.raw.workout_planner_guide_icon).readBytes()
            )
            zos.closeEntry()
        }

        zipBuffer.toByteArray()
    }
}

@JsonClass(generateAdapter = true)
data class GuideManifest(
    @Json(name = "name") val name: String,
    @Json(name = "type") val type: String,
    @Json(name = "owner") val owner: String,
    @Json(name = "description") val description: String
)
