package com.stt.android.domain.user

import android.os.Parcelable
import com.stt.android.domain.session.DomainUserSession
import kotlinx.parcelize.Parcelize

@Parcelize
data class User(
    val id: Int? = null,
    val key: String?,
    val username: String,
    val website: String?,
    val city: String?,
    val country: String?,
    val profileImageUrl: String?,
    val profileImageKey: String?,
    val realName: String?,
    val description: String? = null,
    val session: DomainUserSession? = null,
    val followModel: Boolean? = null,
    val createdDate: Long? = null,
    val lastLogin: Long? = null,
    val roles: List<String>? = null,
    val showLocale: Boolean? = null,
    val blocked: Boolean? = null,
    val coverImageUrl: String? = null,

) : Parcelable {
    val realNameOrUsername: String
        get() = realName?.takeUnless { it.isBlank() } ?: username

    // In case deserializing UserSession from a blob in OrmLite failed during migration
    val isSessionKeyMissing: Boolean
        get() = session != null && session.sessionKey == null

    val isLoggedIn: Boolean
        get() = session?.sessionKey != null

    val fullSizeProfileImageUrl: String?
        get() = profileImageUrl?.replace("_c.jpg", ".jpg")

    val isFieldTester: Boolean
        get() = roles?.contains(ROLE_FIELDTESTER) ?: false

    val isAnonymous: Boolean
        get() = username == ANONYMOUS_USERNAME

    fun makeFacebookReady() = copy(session = session?.makeFacebookReady())

    companion object {
        const val ANONYMOUS_USERNAME = "anonymous"
        private const val CURRENT_USER_DEFAULT_ID = 1
        private const val ROLE_FIELDTESTER = "FIELDTESTER"

        @JvmField
        val ANONYMOUS = User(
            id = CURRENT_USER_DEFAULT_ID,
            key = null,
            username = ANONYMOUS_USERNAME,
            website = null,
            city = null,
            country = null,
            profileImageUrl = null,
            profileImageKey = null,
            realName = null,
            description = null,
            session = null,
            followModel = null,
        )

        @JvmStatic
        val INVALID = User(
            id = null,
            key = null,
            username = "",
            website = null,
            city = null,
            country = null,
            profileImageUrl = null,
            profileImageKey = null,
            realName = null,
            description = null,
            session = null,
            followModel = null,
        )

        @JvmStatic
        fun loggedIn(
            key: String?,
            username: String,
            session: DomainUserSession?,
            website: String?,
            city: String?,
            country: String?,
            profileImageUrl: String?,
            profileImageKey: String?,
            realName: String?,
            followModel: Boolean?,
            description: String?,
            createdDate: Long?,
            lastLogin: Long?,
            roles: List<String>?,
            coverImageUrl: String? = null
        ) = User(
            id = CURRENT_USER_DEFAULT_ID,
            key = key,
            username = username,
            session = session,
            website = website,
            city = city,
            country = country,
            profileImageUrl = profileImageUrl,
            profileImageKey = profileImageKey,
            realName = realName,
            description = description,
            followModel = followModel,
            createdDate = createdDate,
            lastLogin = lastLogin,
            roles = roles,
            coverImageUrl = coverImageUrl,
        )

        @JvmStatic
        fun publicInfo(
            key: String?,
            username: String,
            realName: String?,
            profileImageUrl: String?,
            profileImageKey: String?,
            website: String?,
            city: String?,
            country: String?,
            description: String? = null
        ) = User(
            id = null,
            key = key,
            username = username,
            website = website,
            city = city,
            country = country,
            profileImageUrl = profileImageUrl,
            profileImageKey = profileImageKey,
            realName = realName,
            description = description,
        )

        @JvmStatic
        @JvmOverloads
        fun minimalInfo(
            key: String?,
            username: String,
            realName: String?,
            profileImageUrl: String?,
            description: String? = null
        ) = publicInfo(
            key = key,
            username = username,
            realName = realName,
            website = null,
            city = null,
            country = null,
            profileImageUrl = profileImageUrl,
            profileImageKey = null,
            description = description
        )
    }
}
