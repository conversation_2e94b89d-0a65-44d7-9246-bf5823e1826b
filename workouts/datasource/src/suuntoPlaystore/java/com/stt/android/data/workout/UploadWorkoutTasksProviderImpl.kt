package com.stt.android.data.workout

import com.stt.android.data.tags.SyncNewUserTags
import com.stt.android.data.workout.attributes.SyncWorkoutAttributes
import com.stt.android.data.workout.pictures.SyncNewPictures
import com.stt.android.data.workout.sync.SyncDeletedWorkouts
import com.stt.android.data.workout.sync.SyncManuallyCreatedWorkouts
import com.stt.android.data.workout.sync.SyncTrackedWorkouts
import com.stt.android.data.workout.sync.SyncUpdatedWorkouts
import com.stt.android.data.workout.videos.SyncNewVideos
import com.stt.android.utils.runSuspendCatchingEach
import javax.inject.Inject

class UploadWorkoutTasksProviderImpl @Inject constructor(
    private val syncDeletedWorkouts: SyncDeletedWorkouts,
    private val syncUpdatedWorkouts: SyncUpdatedWorkouts,
    private val syncManuallyCreatedWorkouts: SyncManuallyCreatedWorkouts,
    private val syncTrackedWorkouts: SyncTrackedWorkouts,
    private val syncNewPictures: SyncNewPictures,
    private val syncNewVideos: SyncNewVideos,
    private val syncWorkoutAttributes: SyncWorkoutAttributes,
    private val syncNewUserTags: SyncNewUserTags,
) : UploadWorkoutTasksProvider {
    override suspend fun provideTasks() =
        listOf(
            syncDeletedWorkouts::invoke to "Error during workout deletion sync",
            syncUpdatedWorkouts::invoke to "Error during workout update sync",
            syncManuallyCreatedWorkouts::invoke to "Error during new manual workout sync",
            syncTrackedWorkouts::invoke to "Error during new tracked workout sync",
            syncNewPictures::invoke to "Error during new pictures sync",
            syncNewVideos::invoke to "Error during new videos sync",
            syncNewUserTags::invoke to "Error during new user tags sync",
            syncWorkoutAttributes::invoke to "Error during workout attributes sync",
        ).runSuspendCatchingEach { (task, errorMessage) -> task() to errorMessage }
}
