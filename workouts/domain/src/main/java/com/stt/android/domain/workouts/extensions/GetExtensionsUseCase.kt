package com.stt.android.domain.workouts.extensions

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import timber.log.Timber
import javax.inject.Inject

/**
 * Gets workout extensions either from local or remote
 */
class GetExtensionsUseCase
@Inject constructor(
    private val extensionsDataSource: ExtensionsDataSource,
    private val workoutHeaderDataSource: WorkoutHeaderDataSource
) {
    suspend fun getExtensions(
        workoutId: Int,
        extensionsFetched: Boolean,
        workoutKey: String?,
    ): List<WorkoutExtension> {
        Timber.d("Getting extensions for workout id: $workoutId")
        if (!extensionsFetched && workoutKey != null) {
            val fetchedExtensions = runSuspendCatching {
                extensionsDataSource.fetchExtensions(
                    workoutId = workoutId,
                    workoutKey = workoutKey
                )
            }.getOrElse { e ->
                Timber.w(e, "Failed to fetch extensions from server for workout key = $workoutKey")
                return extensionsDataSource.loadExtensions(workoutId)
            }
            var extensionsPersistedSuccessfully = true
            fetchedExtensions.forEach { extension ->
                runSuspendCatching {
                    extensionsDataSource.upsertExtension(workoutId, extension)
                }.onFailure { e ->
                    Timber.w(e, "Failed to store extension to DB for workout ID $workoutId")
                    extensionsPersistedSuccessfully = false
                }
            }
            // We update extensionsFetched flag to true only if all extensions were stored
            // so that the process can be retried in case of a temporary failure
            if (extensionsPersistedSuccessfully) {
                Timber.d("Updating workout header 'extensionsFetched' flag to 'true'")
                workoutHeaderDataSource.markExtensionsFetched(workoutId)
            }
            Timber.d("Returning fetched workout extensions")
            return fetchedExtensions
        } else {
            Timber.d("Returning extensions loaded from local DB")
            return extensionsDataSource.loadExtensions(workoutId)
        }
    }

    suspend fun getExtensions(workoutHeader: WorkoutHeader): List<WorkoutExtension> = getExtensions(
        workoutId = workoutHeader.id,
        extensionsFetched = workoutHeader.extensionsFetched,
        workoutKey = workoutHeader.key,
    )
}
