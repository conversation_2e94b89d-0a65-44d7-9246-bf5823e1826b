package com.stt.android.data.activitydata.logout

import android.content.SharedPreferences
import androidx.work.WorkManager
import com.stt.android.data.recovery.RecoveryRemoteSyncJob
import com.stt.android.data.sleep.SleepRemoteSyncJob
import com.stt.android.data.source.local.recovery.RecoveryDataDao
import com.stt.android.data.source.local.sleep.SleepSegmentDao
import com.stt.android.data.source.local.sleep.SleepStagesDao
import com.stt.android.data.source.local.trenddata.TrendDataDao
import com.stt.android.data.source.local.trenddata.WeChatTrendDataDao
import com.stt.android.data.trenddata.TrendDataRemoteSyncJob
import com.stt.android.data.trenddata.WeChatTrendDataRemoteJob
import javax.inject.Inject

class ActivityDataHelperSyncAndDelete
@Inject constructor(
    private val trendDataDao: TrendDataDao,
    private val sleepSegmentDao: SleepSegmentDao,
    private val sleepStatesDao: SleepStagesDao,
    private val recoveryDataDao: RecoveryDataDao,
    private val weChatTrendDataDao: WeChatTrendDataDao,
    private val sharedPreferences: SharedPreferences,
    private val workManager: dagger.Lazy<WorkManager>
) : ActivityDataHelper {

    override fun delete247Data() {
        WeChatTrendDataRemoteJob.cancelAndClearFlags(workManager.get(), sharedPreferences)
        TrendDataRemoteSyncJob.cancelAndClearFlags(workManager.get(), sharedPreferences)
        SleepRemoteSyncJob.cancelAndClearFlags(workManager.get(), sharedPreferences)
        RecoveryRemoteSyncJob.cancelAndClearFlags(workManager.get(), sharedPreferences)
        trendDataDao.deleteAll()
        sleepSegmentDao.deleteAll()
        sleepStatesDao.deleteAll()
        recoveryDataDao.deleteAll()
        weChatTrendDataDao.deleteAll()
    }

    override fun start247DataSync() {
        TrendDataRemoteSyncJob.enqueue(workManager.get())
        SleepRemoteSyncJob.enqueue(workManager.get())
        RecoveryRemoteSyncJob.enqueue(workManager.get())
    }
}
