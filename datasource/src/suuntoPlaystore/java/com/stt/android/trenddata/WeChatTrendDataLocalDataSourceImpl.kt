package com.stt.android.trenddata

import com.stt.android.domain.trenddata.WeChatTrendData
import com.stt.android.domain.trenddata.WeChatTrendDataLocalDataSource
import javax.inject.Inject

class WeChatTrendDataLocalDataSourceImpl @Inject constructor() : WeChatTrendDataLocalDataSource {

    override suspend fun fetchUnSyncTrendData(): List<WeChatTrendData> = arrayListOf()

    override suspend fun fetchSyncedTrendData(): List<WeChatTrendData> = arrayListOf()

    override suspend fun saveTrendData(
        trendDataList: List<WeChatTrendData>,
        syncStatus: Boolean
    ) {}

    override suspend fun updateTrendDataSyncStatus(timestampSeconds: Long, sync: <PERSON>olean) {}

    override suspend fun deleteItem(items: List<WeChatTrendData>) {}
}
