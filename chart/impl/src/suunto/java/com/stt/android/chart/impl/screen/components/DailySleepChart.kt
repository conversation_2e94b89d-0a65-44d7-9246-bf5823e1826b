package com.stt.android.chart.impl.screen.components

import android.graphics.Color
import android.view.MotionEvent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.res.ResourcesCompat
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.CombinedData
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.listener.ChartTouchListener.ChartGesture
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import com.stt.android.FontRefs
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartData.SleepStageEntry
import com.stt.android.chart.impl.screen.components.SleepHRChartRenderer.Companion.MAX
import com.stt.android.chart.impl.screen.components.SleepHRChartRenderer.Companion.MIN
import com.stt.android.ui.utils.OnChartGestureListenerAdapter
import kotlin.math.roundToLong

@Stable
internal class DailySleepChartState {
    var chart by mutableStateOf<SleepStagesChart?>(null)
}

@Composable
internal fun rememberDailySleepChartState() = remember { DailySleepChartState() }

@Composable
internal fun DailySleepChart(
    chartData: ChartData,
    onEntrySelected: (Long) -> Unit,
    onNoEntrySelected: () -> Unit,
    modifier: Modifier = Modifier,
    state: DailySleepChartState = rememberDailySleepChartState(),
) {
    AndroidView(
        factory = { context ->
            state.chart ?: SleepStagesChart(context = context).apply {
                clipToOutline = true

                setScaleEnabled(false)
                isDoubleTapToZoomEnabled = false
                isHighlightPerTapEnabled = false

                setupHighlight(onEntrySelected, onNoEntrySelected)
            }.also { state.chart = it }
        },
        update = { chart ->
            val context = chart.context
            val sleepStagesSeries = chartData.series.first()
            sleepStagesSeries.axisRange.let {
                chart.setXRange(it.minX.roundToLong(), it.maxX.roundToLong())
                chart.setYRangeLeft(it.minY.toFloat(), it.maxY.toFloat())
            }
            val heartRateSeries = chartData.series.getOrNull(1)
            heartRateSeries?.axisRange?.let {
                chart.setYRangeRight(it.minY.toFloat(), it.maxY.toFloat())
            }
            chart.data = CombinedData().apply {
                setData(BarData(BarDataSet(sleepStagesSeries.sleepStageEntries, "sleep stages")))
                // in comparison
                if (heartRateSeries != null) {
                    val dataSets = buildList {
                        chartData.series.drop(1).dropLast(2).map { series ->
                            LineDataSet(
                                series.entries.map { Entry(it.x.toFloat(), it.y.toFloat()) },
                                "heart rate line",
                            ).apply {
                                color = series.color
                                lineWidth = 2f
                                isHighlightEnabled = false
                                mode = LineDataSet.Mode.HORIZONTAL_BEZIER
                                axisDependency = YAxis.AxisDependency.RIGHT
                                setDrawValues(false)
                                setDrawCircles(false)
                                setDrawFilled(false)
                            }
                        }.let { addAll(it) }
                        // has heart rate data
                        if (heartRateSeries.entries.isNotEmpty()) {
                            chartData.series.takeLast(2).mapIndexed { index, series ->
                                LineDataSet(
                                    series.entries.map { Entry(it.x.toFloat(), it.y.toFloat()) },
                                    "heart rate dot",
                                ).apply {
                                    isHighlightEnabled = false
                                    axisDependency = YAxis.AxisDependency.RIGHT
                                    circleRadius = 4f
                                    circleHoleRadius = 2f
                                    circleHoleColor = Color.WHITE
                                    valueTextColor = series.color
                                    valueTextSize = 14f
                                    valueTypeface = ResourcesCompat.getFont(
                                        context,
                                        FontRefs.DEFAULT_FONT_BOLD_REF,
                                    )
                                    valueFormatter = object : ValueFormatter() {
                                        override fun getFormattedValue(value: Float): String {
                                            return "${if (index == 0) MIN else MAX} ${value.toInt()}"
                                        }
                                    }
                                    setDrawValues(true)
                                    setDrawCircles(true)
                                    setDrawFilled(false)
                                    setCircleColor(series.color)
                                    setValueTextColor(series.color)
                                }
                            }.let { addAll(it) }
                        }
                    }
                    setData(LineData(dataSets))
                }
            }
        },
        onReset = { chart ->
            chart.data = CombinedData()
        },
        modifier = modifier.fillMaxSize(),
    )
}

private fun SleepStagesChart.setupHighlight(
    onEntrySelected: (Long) -> Unit,
    onNoEntrySelected: () -> Unit,
) {
    var longPressed = false
    fun notifyEntrySelected(
        entry: Entry?,
        h: Highlight?,
    ) = (entry as? SleepStageEntry)?.let {
        onEntrySelected(it.xStart)
        h?.let { highlightValue(it, false) }
    }
    setOnChartValueSelectedListener(object : OnChartValueSelectedListener {
        override fun onValueSelected(e: Entry?, h: Highlight?) {
            if (longPressed) {
                notifyEntrySelected(e, h)
            } else {
                highlightValue(null, false)
            }
        }

        override fun onNothingSelected() {
            highlightValue(null, false)
        }
    })
    onChartGestureListener = object : OnChartGestureListenerAdapter() {
        override fun onChartLongPressed(me: MotionEvent) {
            longPressed = true
            requestDisallowInterceptTouchEvent(true)
            notifyEntrySelected(
                getEntryByTouchPoint(me.x, me.y),
                getHighlightByTouchPoint(me.x, me.y),
            )
        }

        override fun onChartGestureEnd(me: MotionEvent, lastGesture: ChartGesture?) {
            if (longPressed) {
                longPressed = false
                requestDisallowInterceptTouchEvent(false)
                onNoEntrySelected()
                highlightValue(null, false)
            }
        }
    }
}
