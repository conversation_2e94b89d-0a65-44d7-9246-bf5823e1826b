package com.stt.android.chart.impl.usecases

import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.data.SleepDataLoader
import com.stt.android.chart.impl.screen.SleepStagesViewData
import com.stt.android.common.coroutines.CoroutinesDispatchers
import kotlinx.coroutines.flow.flowOn
import java.time.LocalDate
import javax.inject.Inject

internal class CreateSleepStagesDataUseCase @Inject constructor(
    private val dispatchers: CoroutinesDispatchers,
    private val sleepDataLoader: SleepDataLoader,
) {
    operator fun invoke(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
    ): SleepStagesViewData {
        return SleepStagesViewData.Loaded(
            sleepDataLoader.loadStagesData(
                chartGranularity = chartGranularity,
                from = from,
                to = to,
            ).flowOn(dispatchers.io),
        )
    }
}
