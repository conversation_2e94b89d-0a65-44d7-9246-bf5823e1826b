package com.stt.android.chart.impl.screen.components

import androidx.annotation.DrawableRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.unit.dp
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.model.abbreviatedNameRes
import com.stt.android.chart.impl.screen.ChartViewData
import com.stt.android.chart.impl.screen.ChartViewEvent
import com.stt.android.compose.component.SuuntoSegmentedButton
import com.stt.android.compose.component.SuuntoSingleChoiceSegmentedButtonRow
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing

@Composable
internal fun ChartSelection(
    viewData: ChartViewData.Loaded,
    onEvent: (ChartViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .horizontalScroll(rememberScrollState())
            .padding(horizontal = MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
    ) {
        ChartGranularitySelection(
            viewData = viewData,
            onEvent = onEvent,
        )

        ChartStyleSelection(
            viewData = viewData,
            onEvent = onEvent,
        )
    }
}

@Composable
private fun ChartGranularitySelection(
    viewData: ChartViewData.Loaded,
    onEvent: (ChartViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    SuuntoSingleChoiceSegmentedButtonRow(
        modifier = modifier,
    ) {
        val context = LocalContext.current
        val textMeasurer = rememberTextMeasurer()
        val textStyle = MaterialTheme.typography.bodyMedium
        val buttonWidth = remember(viewData.mainChartGranularities) {
            textMeasurer.measure(
                text = context.getString(R.string.chart_granularity_more),
                style = textStyle,
            )
                .size
                .width
                .dp
        }

        val hasExtraChartGranularity = viewData.extraChartGranularities.isNotEmpty()
        val segmentedButtonCount = if (hasExtraChartGranularity) {
            viewData.mainChartGranularities.size + 1
        } else {
            viewData.mainChartGranularities.size
        }

        viewData.mainChartGranularities
            .forEachIndexed { index, chartGranularity ->
                val selected = viewData.chartGranularity == chartGranularity
                SuuntoSegmentedButton(
                    selected = selected,
                    onClick = { onEvent(ChartViewEvent.UpdateChartGranularity(chartGranularity)) },
                    index = index,
                    count = segmentedButtonCount,
                    modifier = Modifier.width(buttonWidth),
                    label = {
                        Text(
                            text= stringResource(chartGranularity.abbreviatedNameRes),
                            style = MaterialTheme.typography.bodyMedium,
                        )
                    }
                )
            }

        if (hasExtraChartGranularity) {
            val moreSelected = viewData.extraChartGranularities
                .contains(viewData.chartGranularity)
            SuuntoSegmentedButton(
                selected = moreSelected,
                onClick = { onEvent(ChartViewEvent.ShowExtraChartGranularitySelection) },
                index = segmentedButtonCount - 1,
                count = segmentedButtonCount,
                modifier = Modifier.width(buttonWidth),
                icon = {
                    Icon(
                        painter = painterResource(R.drawable.ic_arrow_down),
                        contentDescription = null,
                        modifier = Modifier.size(MaterialTheme.iconSizes.tiny),
                    )
                },
                label = {
                    Text(
                        text = stringResource(
                            id = if (moreSelected) {
                                viewData.chartGranularity.abbreviatedNameRes
                            } else {
                                R.string.chart_granularity_more
                            },
                        ),
                        style = MaterialTheme.typography.bodyMedium,
                    )
                },
            )
        }
    }
}

@Composable
private fun ChartStyleSelection(
    viewData: ChartViewData.Loaded,
    onEvent: (ChartViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    if (viewData.chartStyle.availableChartStyles.size <= 1) {
        return
    }

    OutlinedButton(
        onClick = {
            val chartStyle = when (viewData.chartStyle.currentChartStyle) {
                ChartStyle.SINGLE -> ChartStyle.CUMULATIVE
                ChartStyle.CUMULATIVE -> ChartStyle.SINGLE
            }
            onEvent(ChartViewEvent.UpdateChartStyle(chartStyle))
        },
        modifier = modifier,
        border = BorderStroke(
            color = MaterialTheme.colorScheme.lightGrey,
            width = 0.5.dp,
        ),
    ) {
        Icon(
            painter = painterResource(viewData.chartStyle.currentChartStyle.iconRes()),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.small),
            tint = MaterialTheme.colorScheme.nearBlack,
        )
    }
}

@DrawableRes
private fun ChartStyle.iconRes(): Int = when (this) {
    ChartStyle.SINGLE -> R.drawable.ic_bar_graph
    ChartStyle.CUMULATIVE -> R.drawable.ic_line_graph
}
