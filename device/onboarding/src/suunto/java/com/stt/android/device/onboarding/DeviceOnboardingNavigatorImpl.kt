package com.stt.android.device.onboarding

import android.content.Context
import android.content.Intent
import com.stt.android.device.onboarding.dive.DiveOnboardingActivity
import com.stt.android.device.onboarding.intro.OnboardingIntroActivity
import com.stt.android.device.onboarding.onboardingnew.OnboardingActivity
import com.stt.android.device.onboarding.ocean.OceanOnboardingActivity
import com.stt.android.device.onboarding.spartan.SpartanOnboardingActivity
import com.stt.android.device.onboarding.suunto3.Suunto3OnboardingActivity
import com.stt.android.device.onboarding.suunto579.Suunto579OnboardingActivity
import com.stt.android.watch.DeviceOnboardingNavigator
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import javax.inject.Inject

class DeviceOnboardingNavigatorImpl @Inject constructor() : DeviceOnboardingNavigator {
    override fun newOnboardingActivityIntent(
        context: Context,
        deviceType: SuuntoDeviceType
    ): Intent? = when (deviceType) {
        SuuntoDeviceType.Suunto3,
        SuuntoDeviceType.Suunto3Fitness ->
            Suunto3OnboardingActivity.newStartIntent(
                context,
                deviceType
            )
        SuuntoDeviceType.Suunto5,
        SuuntoDeviceType.Suunto5Peak,
        SuuntoDeviceType.Suunto7,
        SuuntoDeviceType.Suunto9,
        SuuntoDeviceType.Suunto9Lima,
        SuuntoDeviceType.Suunto9Peak ->
            Suunto579OnboardingActivity.newStartIntent(
                context,
                deviceType
            )
        SuuntoDeviceType.SuuntoD5,
        SuuntoDeviceType.EonCore,
        SuuntoDeviceType.EonSteel,
        SuuntoDeviceType.EonSteelBlack ->
            DiveOnboardingActivity.newStartIntent(
                context,
                deviceType
            )
        SuuntoDeviceType.SpartanSport,
        SuuntoDeviceType.SpartanSportWristHR,
        SuuntoDeviceType.SpartanSportWristHRBaro,
        SuuntoDeviceType.SpartanTrainer,
        SuuntoDeviceType.SpartanUltra ->
            SpartanOnboardingActivity.newStartIntent(
                context,
                deviceType
            )
        SuuntoDeviceType.Unrecognized,
        SuuntoDeviceType.Ambit3Peak,
        SuuntoDeviceType.Ambit3Sport,
        SuuntoDeviceType.Ambit3Run,
        SuuntoDeviceType.Ambit3Vertical,
        SuuntoDeviceType.Traverse,
        SuuntoDeviceType.TraverseAlpha -> {
            // do nothing
            null
        }
        SuuntoDeviceType.SuuntoRaceS,
        SuuntoDeviceType.Suunto9PeakPro,
        SuuntoDeviceType.SuuntoVertical,
        SuuntoDeviceType.SuuntoRun,
        SuuntoDeviceType.SuuntoRace,
        SuuntoDeviceType.SuuntoRace2,
        SuuntoDeviceType.SuuntoVertical2 -> {
            OnboardingActivity.newStartIntent(
                context,
                deviceType
            )
        }
        SuuntoDeviceType.SuuntoOcean -> {
            OceanOnboardingActivity.newStartIntent(context, deviceType)
        }
        SuuntoDeviceType.SuuntoGT -> {
            // todo: implement onboarding
            null
        }
    }

    override fun newOnboardingIntroIntent(context: Context, deviceType: SuuntoDeviceType): Intent = when (deviceType) {
        SuuntoDeviceType.Suunto9PeakPro -> {
            // Sparrow goes directly to regular onboarding without separate intro
            OnboardingActivity.newStartIntent(
                context,
                deviceType
            )
        }
        else -> OnboardingIntroActivity.newStartIntent(context, deviceType)
    }
}
