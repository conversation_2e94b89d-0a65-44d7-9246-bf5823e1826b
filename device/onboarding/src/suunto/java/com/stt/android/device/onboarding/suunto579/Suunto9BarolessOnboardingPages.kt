package com.stt.android.device.onboarding.suunto579

import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.device.onboarding.OnboardingPage
import com.stt.android.device.onboarding.R
import com.stt.android.R as BaseR

val Suunto9BarolessOnboardingBattery = OnboardingPage(
    AnalyticsPropertyValue.OnboardingSuunto9PageNameProperty.BATTERY,
    R.string.onboarding_smart_battery_title,
    R.string.onboarding_smart_battery_detail,
    R.drawable.onboarding_s9baroless_photo_battery,
    badgeImageResId = R.drawable.onboarding_s9_badge_battery
)

val Suunto9BarolessOnboardingTraining = OnboardingPage(
    AnalyticsPropertyValue.OnboardingSuunto9PageNameProperty.TRAINING,
    R.string.onboarding_more_than_training_title,
    R.string.onboarding_more_than_training_detail,
    R.drawable.onboarding_s9baroless_photo_training,
    badgeImageResId = R.drawable.onboarding_s9_badge_training
)

val Suunto9BarolessOnboardingExplore = OnboardingPage(
    AnalyticsPropertyValue.OnboardingSuunto9PageNameProperty.EXPLORE,
    R.string.onboarding_explore_title,
    R.string.onboarding_explore_detail,
    R.drawable.onboarding_s9baroless_photo_explore,
    badgeImageResId = R.drawable.onboarding_s9_badge_explore
)

val Suunto9BarolessOnboardingCustomize = OnboardingPage(
    AnalyticsPropertyValue.OnboardingSuunto9PageNameProperty.CUSTOMIZE,
    R.string.onboarding_customize_title,
    R.string.onboarding_customize_detail,
    R.drawable.onboarding_s9baroless_photo_customise,
    badgeImageResId = R.drawable.onboarding_s9_badge_customize
)

val Suunto9BarolessOnboardingPartner = OnboardingPage(
    AnalyticsPropertyValue.OnboardingSuunto9PageNameProperty.PARTNER,
    R.string.onboarding_partner_title,
    R.string.onboarding_partner_detail,
    R.drawable.onboarding_s9_photo_partner,
    badgeImageResId = R.drawable.onboarding_s9_badge_partner
)

val Suunto9BarolessOnboardingEnd = OnboardingPage(
    AnalyticsPropertyValue.OnboardingSuunto9PageNameProperty.END,
    R.string.onboarding_s9_final_title,
    R.string.onboarding_s9_final_detail,
    R.drawable.onboarding_s9_photo_final,
    primaryButtonResId = BaseR.string.ok,
    secondaryButtonResId = BaseR.string.onboarding_visit_help
)

val Suunto9BarolessOnboardingPages = listOf(
    Suunto9BarolessOnboardingCustomize,
    Suunto9BarolessOnboardingTraining,
    Suunto9BarolessOnboardingBattery,
    Suunto9BarolessOnboardingExplore,
    Suunto9BarolessOnboardingPartner,
    Suunto9BarolessOnboardingEnd
)
