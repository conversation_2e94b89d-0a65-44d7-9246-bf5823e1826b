package com.stt.android.device.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.BottomSheetHandle
import com.stt.android.device.R

@Composable
fun WatchWidgetHelpInfoSheet(
    modifier: Modifier = Modifier,
    viewModel: WatchWidgetCustomizationViewModel = viewModel()
) {
    val watchWidgetHelp by viewModel.watchWidgetHelpByDevice.observeAsState()

    BottomSheetHandle()
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState())
            .padding(
                start = MaterialTheme.spacing.medium,
                top = MaterialTheme.spacing.small,
                end = MaterialTheme.spacing.medium,
                bottom = MaterialTheme.spacing.medium
            )
    ) {
        Text(
            modifier = Modifier.padding(MaterialTheme.spacing.zero, MaterialTheme.spacing.medium),
            text = stringResource(id = R.string.watch_widget_help_title),
            style = MaterialTheme.typography.bodyXLargeBold,
        )

        watchWidgetHelp?.let {
            Text(
                text = stringResource(id = it.descriptionRes),
                style = MaterialTheme.typography.body,
            )
            Image(
                modifier = Modifier.fillMaxWidth(),
                painter = painterResource(id = it.imageRes),
                contentDescription = null,
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun WatchWidgetHelpInfoSheetPreview() {
    AppTheme {
        WatchWidgetHelpInfoSheet()
    }
}
