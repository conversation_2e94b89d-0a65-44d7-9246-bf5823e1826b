package com.stt.android.device.datasource.suuntoplusguide

import com.stt.android.domain.connectedservices.ConnectedServicesRepository
import javax.inject.Inject

class IsSuuntoPlusGuidePartnerListChangedUseCase
@Inject constructor(
    private val connectedServicesRepository: ConnectedServicesRepository
) {
    fun isPartnerListChanged() = connectedServicesRepository.isGuidePartnerListChanged()

    fun clearPartnerListChanged() = connectedServicesRepository.clearGuidePartnerListChanged()
}
