package com.stt.android.device.suuntoplusguide.listitems

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.LocalContentColor
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.compose.widgets.TextDecoratorIcon
import com.stt.android.device.R
import java.util.Locale
import com.stt.android.R as BaseR

/**
 * A banner card with an image background, title and description text and a large CTA button
 *
 * @param title Title text
 * @param description Description text
 * @param buttonText Text for the primary button (max 1 line)
 * @param bannerImageRes Drawable resource for the background (will be cropped to fill size)
 * @param bannerImageDimmingStrength Dimming factor for background (0f = original colors, 1f = fully opaque black)
 * @param modifier Modifier
 */
@Composable
fun BannerCard(
    title: String,
    description: String,
    buttonText: String,
    showLearnMoreLink: Boolean,
    onButtonClick: () -> Unit,
    onLearnMoreClick: () -> Unit,
    @DrawableRes bannerImageRes: Int,
    bannerImageDimmingStrength: Float,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier.fillMaxWidth()) {
        Image(
            painter = painterResource(id = bannerImageRes),
            contentDescription = null,
            modifier = Modifier
                .matchParentSize()
                .background(color = MaterialTheme.colors.surface)
                .drawWithCache {
                    onDrawWithContent {
                        drawContent()
                        drawRect(
                            Color.Black.copy(alpha = bannerImageDimmingStrength)
                        )
                    }
                },
            contentScale = ContentScale.Crop,
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 40.dp, horizontal = MaterialTheme.spacing.medium),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CompositionLocalProvider(LocalContentColor provides MaterialTheme.colors.onPrimary) {
                Text(
                    text = title.uppercase(Locale.getDefault()),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyBold
                )

                Text(
                    modifier = Modifier.padding(top = MaterialTheme.spacing.small),
                    text = description,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyLarge
                )

                if (showLearnMoreLink) {
                    TextButton(
                        onClick = onLearnMoreClick,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = LocalContentColor.current
                        ),
                        contentPadding = PaddingValues(
                            horizontal = MaterialTheme.spacing.medium,
                            vertical = MaterialTheme.spacing.xsmall,
                        )
                    ) {
                        Text(
                            text = stringResource(id = BaseR.string.learn_more),
                            style = MaterialTheme.typography.bodyBold
                        )

                        TextDecoratorIcon(
                            modifier = Modifier.padding(start = MaterialTheme.spacing.xxsmall),
                            drawableResource = R.drawable.external_link_outline,
                            size = 24.sp,
                        )
                    }
                } else {
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
                }

                PrimaryButton(
                    modifier = Modifier
                        .fillMaxWidth(),
                    text = buttonText,
                    onClick = onButtonClick
                )
            }
        }
    }
}

@Composable
@Preview
private fun BannerCardPreview() {
    AppTheme {
        BannerCard(
            title = "Structured workouts",
            description = "Build workouts for your training goals. Get real-time guidance on your watch.",
            buttonText = "View workouts",
            showLearnMoreLink = false,
            onButtonClick = {},
            onLearnMoreClick = {},
            bannerImageRes = R.drawable.guides_planner_card_background,
            bannerImageDimmingStrength = 0.45f,
        )
    }
}

@Composable
@Preview
private fun BannerCardWithLearnMoreLinkPreview() {
    AppTheme {
        BannerCard(
            title = "Partner services",
            description = "The partners offer a range of features and functionalities to your watch.",
            buttonText = "Explore partners",
            showLearnMoreLink = true,
            onButtonClick = {},
            onLearnMoreClick = {},
            bannerImageRes = R.drawable.suunto_plus_store_partners_screen_banner,
            bannerImageDimmingStrength = 0.45f,
        )
    }
}
