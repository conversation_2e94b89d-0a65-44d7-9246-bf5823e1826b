package com.stt.android.device.suuntoplusdetails

import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.device.R

@Composable
fun SuuntoPlusItemDetailsModificationDate(
    formattedModificationTime: String?,
    modifier: Modifier = Modifier
) {
    formattedModificationTime?.let {
        Text(
            text = stringResource(R.string.suunto_plus_item_modification_date_and_time, it),
            modifier = modifier
        )
    }
}

@Preview
@Composable
private fun SuuntoPlusItemDetailsModificationDatePreview() {
    AppTheme {
        Surface {
            SuuntoPlusItemDetailsModificationDate("18.9.2022, 19.04")
        }
    }
}
