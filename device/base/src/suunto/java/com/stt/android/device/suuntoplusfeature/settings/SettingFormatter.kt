package com.stt.android.device.suuntoplusfeature.settings

import java.util.Locale
import kotlin.math.roundToLong

object SettingFormatter {
    fun format(value: Float?) = format(value?.toDouble())

    fun format(value: Double?): String {
        val string = value.toString()
        return when {
            // Show missing values as empty strings
            value == null -> ""

            // Infinite or NaN
            !value.isFinite() -> "?"

            // Scientific notation: show as is
            string.contains("E") || string.contains("e") -> string

            // Don't show any decimals for large values
            value > 1000.0 || value < -1000.0 -> value.roundToLong().toString()

            // Show as integer value
            string.endsWith(".0") -> string.dropLast(2)

            // Show as is if there are one or two decimals
            string.numberOfDecimals() <= 2 -> string

            // Format with two decimals with proper rounding
            else -> String.format(Locale.ROOT, "%.2f", value)
        }
    }

    private fun String.numberOfDecimals(): Int = if (contains(".")) {
        takeLastWhile { it != '.' }.length
    } else {
        0
    }
}
