package com.stt.android.device.domain

import com.stt.android.device.datasource.WatchModelDataSource
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class GetCurrentWatchModelUseCase
@Inject constructor(
    private val watchModelDataSource: WatchModelDataSource
) {
    fun getCurrentWatchVariantNameAsFlow(): Flow<String?> =
        watchModelDataSource.getCurrentWatchVariantNameAsFlow()

    fun getCurrentWatchFirmwareAsFlow(): Flow<String?> =
        watchModelDataSource.getCurrentWatchFirmwareAsFlow()

    fun getCurrentSuuntoDeviceTypeAsFlow(): Flow<SuuntoDeviceType> =
        getCurrentWatchVariantNameAsFlow().map { SuuntoDeviceType.fromVariantName(it) }
}
