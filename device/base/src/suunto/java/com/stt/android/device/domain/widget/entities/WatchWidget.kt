package com.stt.android.device.domain.widget.entities

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.suunto.connectivity.widget.WidgetConfig

data class WatchWidget(
    val type: WatchWidgetType,
    @StringRes val nameRes: Int,
    @StringRes val descriptionRes: Int,
    @DrawableRes val imageRes: Int,
    val enabled: <PERSON><PERSON><PERSON>,
    val isPinned: <PERSON>olean,
    val id: String
)

fun WatchWidget.toWidgetConfig() = WidgetConfig(id, enabled, isPinned)
