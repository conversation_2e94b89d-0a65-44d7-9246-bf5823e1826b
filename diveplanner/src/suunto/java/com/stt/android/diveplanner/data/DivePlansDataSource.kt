package com.stt.android.diveplanner.data

import com.stt.android.diveplanner.ui.common.DivePlanUiState
import com.stt.android.diveplanner.ui.common.DivePlansUiState
import kotlinx.coroutines.flow.StateFlow

interface DivePlansDataSource {

    val divePlans: StateFlow<DivePlansUiState>

    fun upsertDivePlan(newDivePlan: DivePlanUiState)

    fun deleteDivePlan(divePlanId: String)

    fun updateTitle(divePlan: DivePlanUiState, title: String)

    fun updateDescription(divePlan: DivePlanUiState, description: String?)

    fun findOrNull(divePlanId: String): DivePlanUiState?
}
