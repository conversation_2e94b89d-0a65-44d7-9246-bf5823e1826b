package com.stt.android.domain.goaldefinition

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class GoalDefinition(
    @<PERSON><PERSON>(name = "id") val id: Long = 0,
    @<PERSON><PERSON>(name = "userName") val userName: String,
    @<PERSON>son(name = "name") val name: String? = null,
    @<PERSON><PERSON>(name = "type") val type: Type = Type.DURATION,
    @Json(name = "period") val period: Period = Period.WEEKLY,
    @<PERSON><PERSON>(name = "startTime") val startTime: Long = 0L,
    @<PERSON><PERSON>(name = "endTime") val endTime: Long = 0L,
    @<PERSON><PERSON>(name = "target") val target: Int = 0,
    @Transient val created: Long = System.currentTimeMillis(),
    @Json(name = "activityIds") val activityIds: List<Int> = emptyList(),
    @Json(name = "default") val default: Boolean = true
) {
    enum class Type {
        DURATION,
        DISTANCE,
        WORKOUTS,
        ENERGY;

        companion object {
            private val map = values().associateBy(Type::ordinal)
            fun fromOrdinal(ordinal: Int) = map.getValue(ordinal)
        }
    }

    enum class Period {
        WEEKLY,
        MONTHLY,
        CUSTOM;

        companion object {
            private val map = values().associateBy(Period::ordinal)
            fun fromOrdinal(ordinal: Int) = map.getValue(ordinal)
        }
    }
}
