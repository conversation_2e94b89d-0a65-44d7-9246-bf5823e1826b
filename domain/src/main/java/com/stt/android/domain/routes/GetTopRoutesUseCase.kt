package com.stt.android.domain.routes

import com.stt.android.domain.BaseUseCase
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import io.reactivex.Flowable
import io.reactivex.Scheduler
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.rx2.asFlowable
import javax.inject.Inject

/**
 * Use case for loading list of [Route] instances that are not
 * marked as delete with an optional username filter
 */
class GetTopRoutesUseCase
@Inject constructor(
    private val topRouteRepository: TopRouteRepository,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : BaseUseCase(ioThread, mainThread) {

    /**
     * Fetch routes from local data source followed by a fetch from remote data source.
     * @param syncWithRemote true if fetching from remote should follow a fetch from local. Default is true.
     * @param includeSegments true to include route segments and points in the result
     */
    fun getRoutes(syncWithRemote: Boolean, includeSegments: Boolean): Flow<List<TopRoute>> =
        topRouteRepository.fetchTopRoutes(
            syncWithRemote = syncWithRemote,
            includeSegments = includeSegments,
        ).map { routes ->
            routes.filterNot(TopRoute::deleted)
        }

    fun getRoutesRx(syncWithRemote: Boolean, includeSegments: Boolean): Flowable<List<TopRoute>> =
        getRoutes(
            syncWithRemote = syncWithRemote,
            includeSegments = includeSegments,
        ).asFlowable()
            .subscribeOn(scheduler)
}
