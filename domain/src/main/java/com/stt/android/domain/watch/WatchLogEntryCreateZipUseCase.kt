package com.stt.android.domain.watch

import com.stt.android.TestOpen
import com.stt.android.domain.BaseUseCase
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import io.reactivex.Scheduler
import io.reactivex.Single
import timber.log.Timber
import java.io.File
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import javax.inject.Inject

@TestOpen
class WatchLogEntryCreateZipUseCase
@Inject constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : BaseUseCase(ioThread, mainThread) {

    /**
     * Creates zip from file list and returns it
     */
    fun createZipFromFiles(watchLogZipParams: WatchLogZipParams): Single<File> {
        val (files, destination, workoutId, logbookEntryId) = watchLogZipParams
        Timber.d("Zipping to destination $destination the following files: $files")
        when {
            destination.exists() && !destination.isDirectory -> "Destination is not a folder"
            files.isEmpty() -> "Nothing to zip"
            files.any { !it.exists() || it.isDirectory } -> "At least one of the files is invalid"
            !destination.exists() && !destination.mkdirs() -> "Cannot create destination folder"
            else -> null
        }?.let {
            return Single.error(IllegalArgumentException(it))
        }
        return Single.fromCallable {
            val zipEntries = files.map { ZipEntry(it.name) }
            val destZip = File(destination, "entry_${workoutId}_$logbookEntryId.zip")
            ZipOutputStream(destZip.outputStream().buffered()).use { zos ->
                zipEntries.forEachIndexed { i, zipEntry ->
                    zos.putNextEntry(zipEntry)
                    files[i].inputStream().buffered().use { iStream ->
                        iStream.copyTo(zos)
                    }
                }
            }
            destZip
        }.subscribeOn(scheduler)
    }

    data class WatchLogZipParams(
        val files: List<File>,
        val destination: File,
        val workoutId: Int,
        val logbookEntryId: Long
    )
}
