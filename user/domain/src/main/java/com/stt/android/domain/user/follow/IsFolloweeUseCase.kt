package com.stt.android.domain.user.follow

import com.stt.android.domain.user.followees.FolloweeDao
import kotlinx.coroutines.runBlocking
import javax.inject.Inject

class IsFolloweeUseCase
@Inject constructor(
    private val followeeDao: FolloweeDao
) {
    /**
     * Check if currently logged in user following the given username
     * @param username Username to check
     * @return True if current user is following given username
     */
    suspend fun isFollowee(username: String): Boolean =
        followeeDao.isFollowee(username)

    fun isFolloweeBlocking(username: String): Bo<PERSON><PERSON> = runBlocking {
        isFollowee(username)
    }
}
