package com.stt.android.suunto.china.debug.wxapi

import android.app.ComponentCaller
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.WeChatDeepLinkHelper
import com.stt.android.sharingplatform.SharingResultState
import com.stt.android.sharingplatform.WeChatAPI
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelbase.BaseResp.ErrCode.ERR_OK
import com.tencent.mm.opensdk.modelbase.BaseResp.ErrCode.ERR_USER_CANCEL
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram
import com.tencent.mm.opensdk.modelmsg.ShowMessageFromWX
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import dagger.hilt.android.AndroidEntryPoint
import org.json.JSONObject
import timber.log.Timber
import javax.inject.Inject

/**
 * DO NOT change the package name and/or name of this activity or it will break the wechat flow
 */
@AndroidEntryPoint
class WXEntryActivity : AppCompatActivity(), IWXAPIEventHandler {

    @Inject
    lateinit var weChatAPI: WeChatAPI

    @Inject
    lateinit var wechatDeepLinkHelper: WeChatDeepLinkHelper

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // forwards intent to WeChat API cause they are not able to do this themselves
        weChatAPI.api.handleIntent(intent, this)
    }

    override fun onNewIntent(intent: Intent, caller: ComponentCaller) {
        super.onNewIntent(intent, caller)
        weChatAPI.api.handleIntent(intent, this)
    }

    override fun onReq(req: BaseReq) {
        Timber.d("WXEntryActivity received request: $req")
        if (req.type == ConstantsAPI.COMMAND_SHOWMESSAGE_FROM_WX && req is ShowMessageFromWX.Req) {
            Timber.w(req.message.messageExt)
            val messageExt = req.message.messageExt ?: ""
            val deepLink = JSONObject(messageExt).optString("url")
            val intent = wechatDeepLinkHelper.getIntent(this, deepLink)
            startActivity(intent)
            finish()
        }
    }

    override fun onResp(resp: BaseResp) {
        if (resp.type == ConstantsAPI.COMMAND_LAUNCH_WX_MINIPROGRAM) {
            val miniProgramResp = resp as WXLaunchMiniProgram.Resp
            Timber.d(miniProgramResp.extMsg)
        } else if (resp.type == ConstantsAPI.COMMAND_SENDMESSAGE_TO_WX) {
            val shareResultState = when (resp.errCode) {
                ERR_USER_CANCEL -> SharingResultState.Cancel
                ERR_OK -> SharingResultState.Success
                else -> SharingResultState.Fail
            }
            weChatAPI.onShareResult(shareResultState)
        }
        finish()
    }
}
