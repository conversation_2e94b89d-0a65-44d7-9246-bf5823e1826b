package com.stt.android.di

import com.stt.android.analytics.SuuntoAnalyticsModule
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusGuideRemoteSyncJobModule
import com.stt.android.di.connectivity.DataLayerModule
import com.stt.android.newemail.EmailCheckModule
import com.stt.android.social.personalrecord.PersonalRecordModule
import com.stt.android.systemwidget.Suunto247SystemWidgetModule
import com.stt.android.watch.gearevent.GearEventModule
import com.stt.android.watch.sportmodes.SportModeActivityModule
import com.suunto.network.HeadsetModule
import dagger.Module

@Module(
    includes = [
        SportModeActivityModule::class,
        SuuntoAnalyticsModule::class,
        GearEventModule::class,
        DataLayerModule::class,
        Suunto247SystemWidgetModule::class,
        DeviceNavigatorModule::class,
        SuuntoPlusGuideRemoteSyncJobModule::class,
        HeadsetModule::class,
        EmailCheckModule::class,
        PersonalRecordModule::class
    ]
)
abstract class BrandContributeAndroidInjectorModule : ContributeAndroidInjectorModule()
