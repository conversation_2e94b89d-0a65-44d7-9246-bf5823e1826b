package com.stt.android.maps

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.controllers.UserSettingsController
import com.stt.android.ui.map.MapHelper
import com.stt.android.utils.FlavorUtils
import timber.log.Timber
import javax.inject.Inject

class SetDefaultMapProviderUseCase @Inject constructor(
    private val suuntoMaps: SuuntoMaps,
    private val sharedPreferences: SharedPreferences,
    private val userSettingsController: UserSettingsController,
) {
    /**
     * mapbox has not been filed for record in China，
     * In order to solve the problem that the suunto_zh cannot be successfully launched on the app market due to Mapbox,
     * the map source must be set to amap for reviewer.
     */
    fun setDefaultMapProviderForChineseAppstoreReviewer(): Boolean {
        if (!FlavorUtils.isSuuntoAppChina) return false
        val uuid = userSettingsController.settings.analyticsUUID
        Timber.d("currentUUID: %s", uuid)
        val isAppstoreReviewUser = uuid.equals(APPSTORE_REVIEWER_UUID, ignoreCase = true)
        if (isAppstoreReviewUser) {
            sharedPreferences.edit { putString("map_provider", "amap") }
            suuntoMaps.setDefaultProvider(MapHelper.PROVIDER_AMAP_NAME)
        }
        return isAppstoreReviewUser
    }

    companion object {
        private const val APPSTORE_REVIEWER_UUID = "c6c6c4ef-8271-43db-b955-4f2b3889abd9"
    }
}
