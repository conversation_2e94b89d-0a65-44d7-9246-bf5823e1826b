package com.stt.android.launcher

import android.content.Context
import android.content.Intent
import android.net.Uri
import com.stt.android.R
import com.stt.android.controllers.CurrentUserController
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.home.BaseHomeActivity
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer.Granularity.LAST_30_DAYS
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer.Granularity.MONTH
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer.Granularity.WEEK
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer.Granularity.YEAR
import com.stt.android.home.diary.diarycalendar.parseMonth
import com.stt.android.home.diary.diarycalendar.parseWeek
import com.stt.android.home.diary.diarycalendar.parseYear
import com.stt.android.home.diary.diarycalendar.sharesummary.DiaryCalendarShareSummaryActivity
import com.stt.android.home.settings.SettingsActivity
import com.stt.android.menstrualcycle.MenstrualCycleOnboardingNavigator
import com.stt.android.menstrualcycle.OnboardingDoneReason
import com.stt.android.session.SignInFlowHook
import com.stt.android.social.following.PeopleActivity
import com.stt.android.social.notifications.inbox.MarketingInboxActivity
import com.stt.android.social.userprofile.BaseUserProfileActivity
import com.stt.android.social.userprofile.UserProfileActivity
import com.stt.android.ui.activities.WorkoutEditDetailsActivity
import com.stt.android.ui.activities.settings.NotificationSettingsActivity
import com.stt.android.ui.activities.settings.PowerManagementSettingsActivity
import com.stt.android.ui.activities.settings.PrivacySettingsActivity
import com.stt.android.utils.BrandFlavourConstants
import com.stt.android.utils.CalendarProvider
import com.stt.android.utils.STTConstants
import timber.log.Timber
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters
import java.util.Locale
import javax.inject.Inject

/**
 * Default implementation of [DeepLinkIntentBuilder] that provides common intent builders
 * for deep links for all flavors
 */
internal class DefaultDeepLinkIntentBuilder
@Inject constructor(
    private val signInFlowHook: SignInFlowHook,
    private val homeActivityNavigator: HomeActivityNavigator,
    private val calendarProvider: CalendarProvider,
    private val menstrualCycleOnboardingNavigator: MenstrualCycleOnboardingNavigator,
    private val workoutDetailsRewriteNavigator: WorkoutDetailsRewriteNavigator,
) : DeepLinkIntentBuilder {
    override fun getDeepLinkIntent(
        context: Context,
        uri: Uri,
        currentUserController: CurrentUserController,
        fragmentOrPathParts: Array<String>,
        type: String
    ): Intent? {
        // Match against supported deeplinks
        return when (type.lowercase(Locale.ROOT)) {
            PHONE_SETTINGS -> parsePhoneSettingsDeeplinkIntent(context, fragmentOrPathParts.getOrNull(2) ?: "")
            SETTINGS -> parseSettingsDeeplinkIntent(context, currentUserController, fragmentOrPathParts.getOrNull(2) ?: "")
            EXPLORE -> parseExploreDeeplinkIntent(context, fragmentOrPathParts.getOrNull(2) ?: "")
            MAP -> parseMapStyleDeepLinkIntent(context, fragmentOrPathParts, uri)
            PEOPLE -> BaseHomeActivity.newStartIntentToPeopleTab(context, false, false, false)
            WORKOUT -> parseWorkoutDeeplinkIntent(fragmentOrPathParts[2])
            FEED -> parseFeedDeeplinkIntent(context, fragmentOrPathParts[2])
            MARKETINGINBOX -> MarketingInboxActivity.newIntent(context)
            WEEK_CALENDAR,
            MONTH_CALENDAR,
            YEAR_CALENDAR -> BaseHomeActivity.newStartIntentToDiaryCalendar(context, uri)
            AI_PROGRAMS -> BaseHomeActivity.newStartIntentToDiaryCalendar_AiProgramsTab(context, uri, fragmentOrPathParts.getOrNull(2) ?: "")
            SUBSCRIBE_MARKETING_CONSENT -> BaseHomeActivity.newStartIntentToSubscribeMarketingConsent(context)
            SHARE_YEAR -> parseShareSummaryDeeplinkIntent(context, fragmentOrPathParts, YEAR)
            SHARE_YEAR_DATE -> parseShareSummaryDeeplinkIntent(context, fragmentOrPathParts, YEAR)
            SHARE_MONTH -> parseShareSummaryDeeplinkIntent(context, fragmentOrPathParts, MONTH)
            SHARE_30_DAYS -> parseShareSummaryDeeplinkIntent(context, fragmentOrPathParts, LAST_30_DAYS)
            SHARE_WEEK -> parseShareSummaryDeeplinkIntent(context, fragmentOrPathParts, WEEK)
            DIARY -> parseDiaryDeepLinkIntent(context, fragmentOrPathParts)
            QUESTIONNAIRE -> parseQuestionnaireDeepLinkIntent(context, fragmentOrPathParts)
            MENSTRUAL_CYCLE -> parseMenstrualCycleDeeplinkIntent(context, fragmentOrPathParts.getOrNull(2))
            else -> throw IllegalArgumentException("Helpshift faq uri not supported")
        }
    }

    data class IntentInfo(val packageName: String, val className: String)

    private val batterySettingsIntents = listOf(
        IntentInfo(
            "com.huawei.systemmanager",
            "appcontrol.activity.StartupAppControlActivity"
        ),
        IntentInfo(
            "com.huawei.systemmanager",
            "power.ui.HwPowerManagerActivity"
        )
    )

    override fun getAuthIntent(context: Context, type: String, uri: Uri): Intent? =
        if (type == "apple") {
            // Forward Apple authentication result for SignInActivity as intent URI with
            // the result encoded as query parameters
            signInFlowHook.newStartIntent(context, homeActivityNavigator.newStartIntent(context))
                .apply {
                    data = uri
                }
        } else {
            null
        }

    override fun getTestIntent(context: Context, type: String): Intent? = null

    override fun getOtaIntent(context: Context, uri: Uri): Intent? = null

    override fun getUserIntent(context: Context, uri: Uri): Intent =
        BaseUserProfileActivity.newStartIntent(
            context = context,
            userName = getFragmentsOrPathParts(uri)[1].lowercase(),
            fromNotification = false,
        )

    override fun getWorkoutIntent(context: Context, uri: Uri): Intent =
        workoutDetailsRewriteNavigator.createIntent(
            context = context,
            username = "",
            workoutId = null,
            workoutKey = getFragmentsOrPathParts(uri)[1].lowercase(),
        )

    override fun getFollowersIntent(context: Context): Intent =
        PeopleActivity.newIntent(
            context = context,
            showPendingRequests = true,
        )

    private fun parseMenstrualCycleDeeplinkIntent(context: Context, path: String?): Intent? {
        if (!BrandFlavourConstants.PROVIDE_MC_FEATURE) return null
        // Supported settings uris:
        // com.sports-tracker[.suunto]://campaign/menstrualcycle/log
        // com.sports-tracker[.suunto]://campaign/menstrualcycle/onboarding
        return when (path) {
            LOG -> BaseHomeActivity.newStartIntentToLogMenstrualCycle(context)
            ONBOARDING -> menstrualCycleOnboardingNavigator.newOnboardingActivityIntent(context, OnboardingDoneReason.POP_UP)
            else -> null
        }
    }

    /**
     * Match the given path to supported settings uris: com.sports-tracker[.suunto]://campaign/settings/...
     * @return Intent instance or null
     */
    private fun parseSettingsDeeplinkIntent(context: Context, currentUserController: CurrentUserController, path: String): Intent? {
        // Supported settings uris:
        // com.sports-tracker[.suunto]://campaign/settings
        // com.sports-tracker[.suunto]://campaign/settings/account
        // com.sports-tracker[.suunto]://campaign/settings/country
        // com.sports-tracker[.suunto]://campaign/settings/usersettings
        // com.sports-tracker[.suunto]://campaign/settings/screenbacklight
        // com.sports-tracker[.suunto]://campaign/settings/altitudesource
        // com.sports-tracker[.suunto]://campaign/settings/altitudeoffset
        // com.sports-tracker[.suunto]://campaign/settings/notifications
        // com.sports-tracker[.suunto]://campaign/settings/followerapproval
        return when (path) {
            ACCOUNT -> UserProfileActivity.newStartIntent(context, currentUserController.currentUser)
            COUNTRY -> SettingsActivity.newPreferenceStartIntent(context, R.string.country_preference)
            USER_SETTINGS -> SettingsActivity.newScreenStartIntent(context, R.string.user_settings_category)
            SCREEN_BACKLIGHT -> SettingsActivity.newPreferenceStartIntent(context, R.string.screen_backlight_preference)
            ALTITUDE_SOURCE -> SettingsActivity.newPreferenceStartIntent(context, R.string.altitude_source_preference)
            ALTITUDE_OFFSET -> SettingsActivity.newPreferenceStartIntent(context, R.string.altitude_offset_preference)
            POWER_MANAGEMENT_SETTINGS -> PowerManagementSettingsActivity.newStartIntentForDeepLink(
                context
            )
            PRIVACY_SETTINGS -> PrivacySettingsActivity.newStartIntent(context)
            NOTIFICATIONS -> {
                if (currentUserController.isLoggedIn) {
                    NotificationSettingsActivity.newStartIntent(context)
                } else {
                    null
                }
            }
            FOLLOWER_APPROVAL -> PrivacySettingsActivity.newStartIntent(context)
            "" -> SettingsActivity.newStartIntent(context)
            else -> throw IllegalArgumentException("Settings deeplink uri not supported")
        }
    }

    private fun getBatterySettingsIntent(context: Context): Intent? {
        var intent = Intent()
        batterySettingsIntents.forEach {
            intent.setClassName(it.packageName, "${it.packageName}.${it.className}")
            val resolveInfo = context.packageManager.resolveActivity(intent, 0)
            if (resolveInfo != null) {
                return intent
            }
        }

        intent = Intent(Intent.ACTION_POWER_USAGE_SUMMARY)
        val resolveInfo = context.packageManager.resolveActivity(intent, 0)
        if (resolveInfo != null) {
            return intent
        }

        return null
    }

    private fun parsePhoneSettingsDeeplinkIntent(
        context: Context,
        path: String
    ): Intent? {
        return when (path) {
            BATTERY_SETTINGS -> getBatterySettingsIntent(context)
            else -> null
        }
    }

    /**
     * Match the given path to supported workout uris: com.sports-tracker[.suunto]://campaign/workout/...
     * @return Intent instance with extra specifying workout deep link type
     */
    private fun parseWorkoutDeeplinkIntent(path: String): Intent {
        // Supported workout uris:
        // com.sports-tracker[.suunto]://campaign/workout/latest
        // com.sports-tracker[.suunto]://campaign/workout/sharelatest
        // com.sports-tracker[.suunto]://campaign/workout/sharelatestcommute
        return when (path) {
            LATEST_WORKOUT -> Intent().putExtra(
                STTConstants.ExtraKeys.DEEPLINK,
                LATEST_WORKOUT
            )
            SHARE_LATEST_WORKOUT -> Intent().putExtra(
                STTConstants.ExtraKeys.DEEPLINK,
                SHARE_LATEST_WORKOUT
            )
            SHARE_LATEST_COMMUTE_WORKOUT -> Intent().putExtra(
                STTConstants.ExtraKeys.DEEPLINK,
                SHARE_LATEST_COMMUTE_WORKOUT
            )
            else -> throw IllegalArgumentException("Workout deeplink uri not supported")
        }
    }

    /**
     * Match the given path to supported feed uris: com.sports-tracker[.suunto]://campaign/feed/...
     * @return Intent instance or null
     */
    private fun parseFeedDeeplinkIntent(context: Context, path: String): Intent? {
        // Supported feed uris:
        // com.sports-tracker[.suunto]://campaign/feed/manualworkout
        // com.sports-tracker[.suunto]://campaign/feed/weeklygoal
        // com.sports-tracker[.suunto]://campaign/feed/co2reduced
        // com.sports-tracker[.suunto]://campaign/feed/dashboardcustomizationguidance
        return when (path) {
            MANUAL_WORKOUT -> WorkoutEditDetailsActivity.newStartIntentForNew(context)
            WEEKLY_GOAL -> BaseHomeActivity.newStartIntentToWeeklyGoal(context)
            CO2_REDUCED -> BaseHomeActivity.newStartIntentToCO2Widget(context)
            else -> throw IllegalArgumentException("Feed deeplink uri not supported")
        }
    }

    private fun parseExploreDeeplinkIntent(context: Context, path: String): Intent? {
        return when (path) {
            ROUTES -> BaseHomeActivity.newStartIntentToRoutes(context)
            MAPS -> BaseHomeActivity.newStartIntentToExploreTab(
                context,
                true,
                null,
                null,
                null,
                null,
                null
            )
            else -> BaseHomeActivity.newStartIntentToExploreTab(
                context,
                false,
                null,
                null,
                null,
                null,
                null
            )
        }
    }

    private fun parseMapStyleDeepLinkIntent(
        context: Context,
        fragmentOrPathParts: Array<String>,
        uri: Uri
    ): Intent? {
        // Samples:
        //
        // Open map using satellite map type:
        //   com.sports-tracker.suunto://campaign/map/style/satellite
        //
        // Open map in 2D using satellite map type:
        //   com.sports-tracker.suunto://campaign/map/style/satellite?3d=0
        //
        // Open map in 3D using satellite map type:
        //   com.sports-tracker.suunto://campaign/map/style/satellite?3d=1
        //
        // Open map in 3D using the map type set by the user:
        //   com.sports-tracker.suunto://campaign/map/style/?3d=true
        //
        // Opens map in 2D using the map type set by the user:
        //   com.sports-tracker.suunto://campaign/map/style?3d=false (last / omitted)
        //
        // Opens map with paved road surface layer, hiding cycling forbidden roads:
        //   com.sports-tracker.suunto://campaign/map/style/?road_surface=Paved&hide_cycling_forbidden=true

        val mapStyle = if (fragmentOrPathParts.getOrNull(2) == MAP_STYLE) {
            fragmentOrPathParts.getOrNull(3)?.lowercase(Locale.ROOT) ?: ""
        } else {
            null
        }

        val enable3D = uri.queryParameterNames.firstOrNull { it.equals("3d", true) }?.run {
            uri.getBooleanQueryParameter(this, false)
        }

        val roadSurfaces = uri.queryParameterNames.firstOrNull { it.equals("road_surface", true) }?.run {
            uri.getQueryParameters(this)
        }

        val hideCyclingForbidden = uri.queryParameterNames.firstOrNull { it.equals("hide_cycling_forbidden", true) }?.run {
            uri.getBooleanQueryParameter(this, false)
        }

        return BaseHomeActivity.newStartIntentToExploreTab(
            context,
            false,
            mapStyle,
            enable3D,
            roadSurfaces,
            hideCyclingForbidden,
            null
        )
    }

    private fun parseDiaryDeepLinkIntent(context: Context, fragmentOrPathParts: Array<String>): Intent? {
        // Supported diary uris:
        // com.sports-tracker.suunto://campaign/diary/progress
        return if (fragmentOrPathParts.size >= 3 && fragmentOrPathParts[2] == PROGRESS) {
            BaseHomeActivity.newStartIntentToDiaryProgressTab(context)
            // com.sports-tracker.suunto://campaign/diary/summary
        } else if (fragmentOrPathParts.size >= 3 && fragmentOrPathParts[2] == SUMMARY) {
            BaseHomeActivity.newStartIntentToDiarySummaryTab(context)
        }  else if (fragmentOrPathParts.size >= 3 && fragmentOrPathParts[2] == RECOVERY) {
            BaseHomeActivity.newStartIntentToDiaryRecoveryTab(context)
        } else {
            Timber.w("Unhandled diary URI, parts=${fragmentOrPathParts.joinToString()}")
            null
        }
    }

    private fun parseQuestionnaireDeepLinkIntent(context: Context, fragmentOrPathParts: Array<String>): Intent? {
        // Supported questionnaire uris:
        // com.sports-tracker.suunto://campaign/questionnaire/motivations
        return if (fragmentOrPathParts.size >= 3 &&
            fragmentOrPathParts[2] == QUESTIONNAIRE_MOTIVATIONS
        ) {
            // Only motivation query is supported for now.
            BaseHomeActivity.newStartIntentToMotivationsQuestionnaire(context)
        } else {
            Timber.w("Unhandled questionnaire URI, parts=${fragmentOrPathParts.joinToString()}")
            null
        }
    }

    private fun parseShareSummaryDeeplinkIntent(
        context: Context,
        pathParts: Array<String>,
        granularity: DiaryCalendarListContainer.Granularity
    ): Intent {
        // All formats of dates are in YYYY-MM-DD
        // Year
        // com.sports-tracker[.suunto]://campaign/shareyear
        // com.sports-tracker[.suunto]://campaign/shareyear/{year}
        // com.sports-tracker[.suunto]://campaign/sharesummaryyear
        // com.sports-tracker[.suunto]://campaign/sharesummaryyear/{year} or {date}
        // Month
        // com.sports-tracker[.suunto]://campaign/sharesummarymonth
        // com.sports-tracker[.suunto]://campaign/sharesummarymonth/{month} or {year-month} or {date}
        // Month
        // com.sports-tracker[.suunto]://campaign/sharesummary30d
        // Week
        // com.sports-tracker[.suunto]://campaign/sharesummaryweek
        // com.sports-tracker[.suunto]://campaign/sharesummaryweek/{date} or {weekoffset} (ie -2 weeks)
        val targetDate = if (pathParts.size < 3) LocalDate.now().toString() else pathParts[2]
        return when (granularity) {
            YEAR -> {
                val date = LocalDate.of(parseYear(targetDate).value, 1, 1)
                val startDate = date.with(TemporalAdjusters.firstDayOfYear())
                val endDate = date.with(TemporalAdjusters.lastDayOfYear())
                DiaryCalendarShareSummaryActivity.newIntent(
                    context,
                    startDate,
                    endDate,
                    granularity
                )
            }
            MONTH -> {
                val monthYear = parseMonth(targetDate)
                val date = LocalDate.of(monthYear.year, monthYear.month, 1)
                val startDate = date.with(TemporalAdjusters.firstDayOfMonth())
                val endDate = date.with(TemporalAdjusters.lastDayOfMonth())
                DiaryCalendarShareSummaryActivity.newIntent(
                    context,
                    startDate,
                    endDate,
                    granularity
                )
            }
            LAST_30_DAYS -> {
                val date = LocalDate.now()
                val startDate = date.minusDays(29)
                DiaryCalendarShareSummaryActivity.newIntent(
                    context = context,
                    startDate = startDate,
                    endDate = date,
                    granularity = granularity
                )
            }
            WEEK -> {
                // Set the date to the start of the current week
                val date = parseWeek(targetDate, calendarProvider.getDayOfWeekField())
                val endDate = date.plusDays(6)
                DiaryCalendarShareSummaryActivity.newIntent(
                    context = context,
                    startDate = date,
                    endDate = endDate,
                    granularity = granularity
                )
            }
        }
    }
}
