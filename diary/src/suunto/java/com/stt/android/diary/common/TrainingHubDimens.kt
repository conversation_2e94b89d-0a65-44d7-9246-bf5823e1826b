package com.stt.android.diary.common

import androidx.compose.material.MaterialTheme
import androidx.compose.material.ProgressIndicatorDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

internal data class TrainingHubDimens(
    val chipHeight: Dp = 32.dp,
    val chipIconSize: Dp = 24.dp,
    val comparisonChipHeight: Dp = 24.dp,
    val linearIndicatorHeight: Dp = ProgressIndicatorDefaults.StrokeWidth,
    val multiSeriesProgressBarHeight: Dp = 8.dp,
    val multiSeriesProgressMarkerHeight: Dp = 16.dp,
    val selectedMarkerHeight: Dp = 42.dp,
    val selectedMarkerWidth: Dp = 30.dp,
    val sleepCardHeight: Dp = 120.dp,
    val trainingLoadHeaderItemHeight: Dp = 110.dp,
    val tssLoadPerDayRowHeight: Dp = 20.dp,
    val feelingBarContainerHeight: Dp = 25.dp,
    val heartRateZoneRowHeight: Dp = 46.dp,
    val sectionHeaderIconSize: Dp = 36.dp, // Section like Suunto Coach, Training Load
    val weekChangerHeight: Dp = 48.dp,
    val viewMoreOrLessHeight: Dp = 48.dp,
    val impactItemHeight: Dp = 56.dp,
    val intensityCircledNumberSize: Dp = 24.dp,
    val progressIndicatorHeight: Dp = 8.dp
)

private val LocalTrainingHubDimens = staticCompositionLocalOf { TrainingHubDimens() }

internal val MaterialTheme.trainingHubDimens: TrainingHubDimens
    @Composable
    @ReadOnlyComposable
    get() = LocalTrainingHubDimens.current
