package com.stt.android.diary.trainingv2

import com.stt.android.common.ui.ViewPagerFragmentCreator
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named

@Module
@InstallIn(SingletonComponent::class)
abstract class TrainingV2Module {
    @Binds
    @Named("TRAINING_V2_FRAGMENT_CREATOR")
    abstract fun bindTrainingV2FragmentCreator(creator: TrainingV2FragmentCreator): ViewPagerFragmentCreator
}
