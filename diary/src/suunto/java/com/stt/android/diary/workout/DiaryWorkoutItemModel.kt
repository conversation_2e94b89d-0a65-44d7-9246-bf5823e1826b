package com.stt.android.diary.workout

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.diary.R
import com.stt.android.ui.components.workout.WorkoutCard
import com.stt.android.ui.components.workout.WorkoutCardViewModel

@EpoxyModelClass
abstract class DiaryWorkoutItemModel : EpoxyModelWithHolder<DiaryWorkoutItemHolder>() {
    @EpoxyAttribute
    lateinit var workoutHeader: WorkoutHeader

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var workoutCardViewModel: WorkoutCardViewModel

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var workoutDetailsNavigator: WorkoutDetailsRewriteNavigator

    override fun getDefaultLayout(): Int = R.layout.training_item

    override fun bind(holder: DiaryWorkoutItemHolder) {
        super.bind(holder)

        holder.root.setContentWithM3Theme {
            WorkoutCard(
                workoutHeader = workoutHeader,
                viewModel = workoutCardViewModel,
                onClick = {
                    workoutDetailsNavigator.navigate(
                        context = holder.root.context,
                        username = workoutHeader.username,
                        workoutId = workoutHeader.id,
                        workoutKey = workoutHeader.key
                    )
                },
                modifier = Modifier.padding(
                    vertical = MaterialTheme.spacing.small,
                    horizontal = MaterialTheme.spacing.medium,
                ),
            )
        }
    }
}

class DiaryWorkoutItemHolder : KotlinEpoxyHolder() {
    val root: ComposeView by bind(R.id.root)
}
