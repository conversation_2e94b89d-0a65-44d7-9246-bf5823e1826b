package com.stt.android.diary.graph.dataloaders

import com.stt.android.diary.graph.dataloaders.base.GraphDataLoader
import com.stt.android.diary.graph.dataloaders.common.CalorieGraphDataLoader
import com.stt.android.diary.graph.dataloaders.common.StepGraphDataLoader
import com.stt.android.diary.graph.dataloaders.dive.DiveCountGraphDataLoader
import com.stt.android.diary.graph.dataloaders.recovery.RecoveryStateGraphDataLoader
import com.stt.android.diary.graph.dataloaders.sleep.SleepGraphDataLoader
import com.stt.android.diary.graph.dataloaders.sleep.SleepHrvGraphDataLoader
import com.stt.android.diary.graph.dataloaders.training.AscentGraphDataLoader
import com.stt.android.diary.graph.dataloaders.training.AvgHrGraphDataLoader
import com.stt.android.diary.graph.dataloaders.training.DistanceGraphDataLoader
import com.stt.android.diary.graph.dataloaders.training.DurationGraphDataLoader
import com.stt.android.diary.graph.dataloaders.training.ExerciseFeelingGraphDataLoader
import com.stt.android.diary.graph.dataloaders.training.FitnessLevelGraphDataLoader
import com.stt.android.diary.graph.dataloaders.training.TssDataLoader
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.workout.ActivityType
import javax.inject.Inject

class GraphDataLoaders @Inject constructor(
    val durationDataLoader: DurationGraphDataLoader,
    val distanceDataLoader: DistanceGraphDataLoader,
    val trainingCountDataLoaderCreator: DiveCountGraphDataLoader.Creator,
    val trendDataLoader: CalorieGraphDataLoader,
    val avgHrDataLoader: AvgHrGraphDataLoader,
    val ascentDataLoader: AscentGraphDataLoader,
    val tssDataLoader: TssDataLoader,
    val fitnessLevelLoader: FitnessLevelGraphDataLoader,
    val exerciseFeelingGraphDataLoader: ExerciseFeelingGraphDataLoader,
    val sleepDataLoader: SleepGraphDataLoader,
    val sleepHrvDataLoader: SleepHrvGraphDataLoader,
    val stepDataLoader: StepGraphDataLoader,
    val recoveryStateGraphDataLoader: RecoveryStateGraphDataLoader,
)

fun GraphDataLoaders.getLoaders(types: List<GraphDataType?>): Set<GraphDataLoader> =
    types.filterNotNull().mapNotNull(::getLoader).toSet()

private fun GraphDataLoaders.getLoader(type: GraphDataType) =
    when (type) {
        GraphDataType.DURATION -> durationDataLoader
        GraphDataType.DISTANCE -> distanceDataLoader
        GraphDataType.TSS -> tssDataLoader
        GraphDataType.STEPS -> stepDataLoader
        GraphDataType.CALORIES -> trendDataLoader
        GraphDataType.SLEEP_QUALITY -> sleepDataLoader
        GraphDataType.EXERCISE_FEEL -> exerciseFeelingGraphDataLoader
        GraphDataType.AVERAGE_HEART_RATE -> avgHrDataLoader
        GraphDataType.ASCENT -> ascentDataLoader
        GraphDataType.FITNESS_LEVEL -> fitnessLevelLoader
        GraphDataType.SLEEP_DURATION -> sleepDataLoader
        GraphDataType.SLEEP_REGULARITY -> sleepDataLoader
        GraphDataType.BLOOD_OXYGEN -> sleepDataLoader
        GraphDataType.TRAINING -> durationDataLoader
        GraphDataType.AVG_HR_DURING_SLEEP -> sleepDataLoader
        GraphDataType.MORNING_RESOURCES -> sleepDataLoader
        GraphDataType.FREE_DIVE_COUNT -> trainingCountDataLoaderCreator.withActivityType(
            ActivityType.FREEDIVING
        )
        GraphDataType.SCUBA_DIVE_COUNT -> trainingCountDataLoaderCreator.withActivityType(
            ActivityType.SCUBADIVING
        )
        GraphDataType.HRV -> sleepHrvDataLoader
        GraphDataType.NONE -> null
        GraphDataType.SLEEP_TOTAL -> sleepDataLoader
        GraphDataType.SLEEP_NAP -> sleepDataLoader
        GraphDataType.AVG_SPEED -> null
        GraphDataType.AVG_PACE -> null
        GraphDataType.AVG_POWER -> null
        GraphDataType.NORMALIZED_POWER -> null
        GraphDataType.AVG_SWIM_PACE -> null
        GraphDataType.RECOVERY_STATE -> recoveryStateGraphDataLoader
    }
