package com.stt.android.divecustomization.customization.logic

import com.soy.algorithms.divemodecustomization.entities.WithAvailability
import com.soy.algorithms.divemodecustomization.entities.validationrules.domain.SmlEditor
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.flatMap
import com.stt.android.divecustomization.customization.entities.CustomizationModeWithAvailableOptions
import com.stt.android.divecustomization.customization.entities.DepthUnits
import com.stt.android.divecustomization.customization.entities.DiveAlgorithmType
import com.stt.android.divecustomization.customization.entities.DiveRangeSelectionOption
import com.stt.android.divecustomization.customization.entities.DiveSingleSelectionItem
import com.stt.android.divecustomization.customization.entities.DiveSingleSelectionOption
import com.stt.android.divecustomization.customization.entities.settings.AltitudeLevel
import com.stt.android.divecustomization.customization.entities.settings.DiveAltitude
import com.stt.android.divecustomization.customization.entities.settings.DiveAltitudeSelectable
import com.stt.android.divecustomization.customization.entities.settings.DiveCustomizationAlgorithmContent
import com.stt.android.divecustomization.customization.getAvailableValuesList
import com.stt.android.divecustomization.customization.getSettingOrNull
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlin.math.roundToInt
import com.stt.android.R as BaseR

interface DivingAlgorithm : CustomizationModeParent {

    fun getDiveSettingsAlgorithmFlow(): Flow<ViewState<DiveCustomizationAlgorithmContent>> {
        return currentModeFlow.map { value ->
            value.flatMap { modeWithAvailableOptions ->
                ViewState.Loaded(
                    getDiveSettingAlgorithmContent(modeWithAvailableOptions)
                )
            }
        }
    }

    fun setDiveAlgorithm(algorithm: DiveSingleSelectionItem<String>) {
        val algorithmName = algorithm.optionType.value
        getCurrentMode().let { currentMode ->
            val mode = currentMode.copy(
                diving = currentMode.diving.copy(
                    algorithm = WithAvailability(
                        algorithmName,
                        currentMode.diving.algorithm.available
                    )
                )
            )
            setCurrentMode(mode)
        }
    }

    fun setMinGF(percentage: Int) {
        getCurrentMode().let { currentMode ->
            val mode = currentMode.copy(
                diving = currentMode.diving.copy(
                    minGF = currentMode.diving.minGF.copy(
                        value = percentage.toDouble() / 100
                    )
                )
            )
            // If minGF is now above maxGF, change maxGF to be the same as minGF
            if (percentage > currentMode.diving.maxGF.value.times(100)) {
                setCurrentMode(
                    mode.copy(
                        diving = mode.diving.copy(
                            maxGF = mode.diving.maxGF.copy(
                                value = mode.diving.minGF.value
                            )
                        )
                    )
                )
            } else {
                setCurrentMode(mode)
            }
        }
    }

    fun setMaxGF(percentage: Int) {
        getCurrentMode().let { currentMode ->
            val mode = currentMode.copy(
                diving = currentMode.diving.copy(
                    maxGF = currentMode.diving.maxGF.copy(
                        value = percentage.toDouble() / 100
                    )
                )
            )
            // If maxGF is now below minGF, change minGF to be the same as maxGF
            if (percentage < currentMode.diving.minGF.value.times(100)) {
                setCurrentMode(
                    mode.copy(
                        diving = mode.diving.copy(
                            minGF = mode.diving.minGF.copy(
                                value = mode.diving.maxGF.value
                            )
                        )
                    )
                )
            } else {
                setCurrentMode(mode)
            }
        }
    }

    fun setDiveConservatism(conservatism: DiveSingleSelectionItem<Int>) {
        val conservatismLevel = conservatism.optionType.value
        getCurrentMode().let { currentMode ->
            val mode = currentMode.copy(
                diving = currentMode.diving.copy(
                    conservatism = WithAvailability(
                        conservatismLevel,
                        currentMode.diving.conservatism.available
                    )
                )
            )
            setCurrentMode(mode)
        }
    }

    fun setDiveAltitude(diveAltitude: DiveAltitudeSelectable) {
        val altitude = diveAltitude.altitudeLevel.value
        getCurrentMode().let { currentMode ->
            val mode = currentMode.copy(
                diving = currentMode.diving.copy(
                    altitude = WithAvailability(
                        altitude,
                        currentMode.diving.altitude.available
                    )
                )
            )
            setCurrentMode(mode)
        }
    }

    private fun getDiveSettingAlgorithmContent(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): DiveCustomizationAlgorithmContent {
        return DiveCustomizationAlgorithmContent(
            diveAlgorithm = getAlgorithm(modeWithAvailableOptions),
            diveConservatism = getConservatismLevel(modeWithAvailableOptions),
            diveAltitude = getAltitudeLevel(modeWithAvailableOptions),
            diveMinGF = getMinGF(modeWithAvailableOptions),
            diveMaxGF = getMaxGF(modeWithAvailableOptions),
            showInfoAction = showInfoAction(modeWithAvailableOptions)
        )
    }

    /**
     * Mapper function that maps the algorithm with their available options to the DiveModeGeneralSettings screen content.
     */
    private fun getAlgorithm(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): DiveSingleSelectionOption<String>? {
        val diveAlgorithmSetting =
            getSettingOrNull(modeWithAvailableOptions.mode.diving.algorithm)

        return diveAlgorithmSetting?.let { diveAlgorithm ->
            DiveSingleSelectionOption(
                optionsList = modeWithAvailableOptions.availableOptions.modeDivingAlgorithm.values.map {
                    DiveSingleSelectionItem(
                        selected = it.value == diveAlgorithm,
                        optionType = it
                    )
                }.toImmutableList()
            )
        }
    }

    private fun getConservatismLevel(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): DiveSingleSelectionOption<Int>? {
        val diveConservatismSetting =
            getSettingOrNull(modeWithAvailableOptions.mode.diving.conservatism)

        return diveConservatismSetting?.let { conservatism ->
            DiveSingleSelectionOption(
                optionsList = modeWithAvailableOptions.availableOptions.modeDivingConservatism.values.map {
                    DiveSingleSelectionItem(
                        selected = it.value == conservatism,
                        optionType = it
                    )
                }.toImmutableList()
            )
        }
    }

    private fun getAltitudeLevel(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): DiveAltitude? {
        val diveAltitudeSetting =
            getSettingOrNull(modeWithAvailableOptions.mode.diving.altitude)

        return diveAltitudeSetting?.let { altitude ->
            DiveAltitude(
                _depthUnits = DepthUnits.from(getDeviceConfig().units.depth.value),
                _selectedAltitude = AltitudeLevel.from(altitude),
                diveAltitudeList = modeWithAvailableOptions.availableOptions.modeDivingAltitude.values.map {
                    DiveAltitudeSelectable(
                        _depthUnits = DepthUnits.from(getDeviceConfig().units.depth.value),
                        selected = it.value == altitude,
                        altitudeLevel = AltitudeLevel.from(it.value)
                    )
                }.toImmutableList()
            )
        }
    }

    private fun getMinGF(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): DiveRangeSelectionOption<Int>? {
        return getGF(
            modeWithAvailableOptions.mode.diving.minGF,
            modeWithAvailableOptions.availableOptions.modeDivingMinGF.editor
        )
    }

    private fun getMaxGF(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): DiveRangeSelectionOption<Int>? {
        return getGF(
            modeWithAvailableOptions.mode.diving.maxGF,
            modeWithAvailableOptions.availableOptions.modeDivingMaxGF.editor
        )
    }

    private fun getGF(
        gradientFactor: WithAvailability<Double>,
        conditions: SmlEditor?
    ): DiveRangeSelectionOption<Int>? {
        val gradientFactorSetting =
            getSettingOrNull(gradientFactor)

        return gradientFactorSetting?.let {
            val possibleValues =
                getAvailableValuesList(conditions)

            val percentages = possibleValues.map { it.roundToInt() }.toImmutableList()

            DiveRangeSelectionOption(
                values = percentages,
                selectedValue = (gradientFactorSetting * 100).roundToInt(),
                units = BaseR.string.percentage_sign
            )
        }
    }

    /**
     * We don't want to show the info action if algorithm is none as there's nothing to show.
     */
    private fun showInfoAction(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): Boolean {
        val algorithmType =
            DiveAlgorithmType.from(modeWithAvailableOptions.mode.diving.algorithm.value)
        return algorithmType != DiveAlgorithmType.NONE
    }
}
