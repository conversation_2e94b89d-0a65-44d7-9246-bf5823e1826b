package com.stt.android.divecustomization.customization.ui.common

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Switch
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.sp
import com.stt.android.compose.theme.switchColors
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

/**
 * Generic composable for dive switches.
 *
 * Example usage:
 *
 *   var checkedState by remember { mutableStateOf(true) }
 *
 *   DiveSwitchListItem(
 *       titleText = "Title",
 *       subtitleText = "Subtitle Text",
 *       checkedState = checkedState,
 *       onClick = {
 *
 *       },
 *       onSwitchStateChanged = {
 *           checkedState = it
 *       }
 *   )
 *
 * @param titleText Title text of the switch row.
 * @param subtitleText Optional Subtitle text of the switch row.
 * @param checkedState current state of the switch.
 * @param onSwitchStateChanged method to call when switch state changes.
 * @param onClick method to call when the switch row is tapped.
 */
@Composable
fun DiveSwitchListItem(
    titleText: String,
    checkedState: Boolean,
    onSwitchStateChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    subtitleText: String? = null,
    onClick: (() -> Unit)? = null
) {
    var localModifier = modifier
    if (checkedState && onClick != null) {
        localModifier = localModifier.clickable(onClick = onClick)
    }
    Row(
        modifier = localModifier
            .padding(dimensionResource(CR.dimen.padding))
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.Start,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.fillMaxWidth(0.9f)) {
            // Text should be dimmed out if switch is off
            // Otherwise show default color
            val textColor = if (checkedState) {
                Color.Unspecified
            } else {
                colorResource(BaseR.color.dark_gray3)
            }

            Text(
                text = titleText,
                fontSize = 16.sp,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                color = textColor
            )

            subtitleText?.let {
                Text(
                    text = it,
                    fontSize = 14.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    color = textColor
                )
            }
        }

        Switch(
            modifier = Modifier.wrapContentWidth(unbounded = true),
            colors = MaterialTheme.colors.switchColors,
            checked = checkedState,
            onCheckedChange = onSwitchStateChanged
        )
    }
}
