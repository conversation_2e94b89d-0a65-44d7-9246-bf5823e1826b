package com.stt.android.divecustomization.customization.logic

import android.net.Uri
import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.domain.divecustomization.Failure
import com.stt.android.domain.divecustomization.Reason
import com.stt.android.domain.divecustomization.Resource
import com.stt.android.domain.divecustomization.Success
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.repository.commands.GetOrSetSettingsFileResponse
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.FileWriter
import java.io.IOException
import javax.inject.Inject

class PushDeviceSettingsUseCase @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel,
    private val fileProvider: DiveFileProvider,
    private val dispatcherProvider: CoroutinesDispatcherProvider
) {
    // Had to suppress it as AS unnecessarily keeps on warning about it
    @Suppress("BlockingMethodInNonBlockingContext")
    suspend fun pushSettings(
        deviceConfigString: String
    ): Resource<Boolean> =
        withContext(dispatcherProvider.io) {
            try {
                // write the config string to file
                val settingsFile = fileProvider.getFile()
                val fileWriter = FileWriter(settingsFile)
                fileWriter.write(deviceConfigString)
                fileWriter.flush()
                fileWriter.close()

                val uri = Uri.fromFile(settingsFile)
                val response = suuntoWatchModel.setSettingsFileCoroutine(uri)
                handlePushSettingsResponse(response)
            } catch (ex: IOException) {
                Timber.e(ex)
                Failure(
                    Reason(
                        "Error writing to file",
                        ex
                    )
                )
            }
        }

    private fun handlePushSettingsResponse(
        response: GetOrSetSettingsFileResponse
    ): Resource<Boolean> {
        return if (response.successful) {
            Success(response.successful)
        } else {
            val errorMessage = "Unable to set settings file: ${response.errorMessage}"
            Failure(
                Reason(
                    errorMessage,
                    Throwable(message = errorMessage)
                )
            )
        }
    }
}
