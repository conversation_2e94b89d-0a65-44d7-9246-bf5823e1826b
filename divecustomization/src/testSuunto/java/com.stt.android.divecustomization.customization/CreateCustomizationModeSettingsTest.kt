package com.stt.android.divecustomization.customization

import com.soy.algorithms.divemodecustomization.entities.DiveDeviceConfig
import com.stt.android.divecustomization.customization.logic.CreateCustomizationModeSettings
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.times
import org.mockito.kotlin.verify

@RunWith(MockitoJUnitRunner::class)
class CreateCustomizationModeSettingsTest : DiveCustomizationTestBase() {

    private lateinit var createModeSettings: CreateCustomizationModeSettings

    @Before
    override fun setup() = runTest {
        super.setup()
        createModeSettings = viewModel
    }

    @Test
    fun `check if create dive mode flow returns correct values`() = runTest {
        val data = createModeSettings.getCreateDiveModeSettingsFlow().first().data

        assertThat(data?.diveModeName).isEqualTo("Scuba/OC")

        // Check that correct dive modes are returned
        val diveModesList = data?.diveMode?.optionsList
        assertThat(diveModesList?.size).isEqualTo(2)
        assertThat(diveModesList?.get(0)?.optionType?.value)
            .isEqualTo("Gauge")
        assertThat(diveModesList?.get(1)?.optionType?.value)
            .isEqualTo("OC")

        // Check that correct dive styles are returned
        val diveStylesList = data?.diveStyle?.optionsList
        assertThat(diveStylesList?.size).isEqualTo(3)
        assertThat(diveStylesList?.get(0)?.optionType?.value)
            .isEqualTo("Free")
        assertThat(diveStylesList?.get(1)?.optionType?.value)
            .isEqualTo("Off")
        assertThat(diveStylesList?.get(2)?.optionType?.value)
            .isEqualTo("Scuba")

        // newly created mode should not be active
        assertThat(data?.isActiveMode).isFalse()
    }

    @Test
    fun `setting active mode should call validation with correct change if triggerValidation is true`() =
        runTest {
            createModeSettings.setActiveMode(modeName = "activeModeName", triggerValidation = true)

            val argumentCaptor = argumentCaptor<DiveDeviceConfig>()
            verify(diveDeviceModeUseCase).validateCustomizationMode(
                argumentCaptor.capture(),
                any(),
                any()
            )

            assertThat(argumentCaptor.firstValue.activeMode.value).isEqualTo("activeModeName")
        }

    @Test
    fun `setting active mode should not call validation if triggerValidation is false`() =
        runTest {
            val mode = createCustomizationModeResponse.validModeWithAvailability
            createModeSettings.setActiveMode(modeName = "activeModeName", triggerValidation = false)

            verify(diveDeviceModeUseCase, times(0)).validateCustomizationMode(
                diveDeviceConfig,
                mode,
                DEVICE_NAME
            )
        }
}
