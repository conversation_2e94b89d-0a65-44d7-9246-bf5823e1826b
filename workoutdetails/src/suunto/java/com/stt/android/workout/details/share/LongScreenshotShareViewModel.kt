package com.stt.android.workout.details.share

import android.net.Uri
import androidx.core.widget.NestedScrollView
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.workout.WorkoutHeaderRepository
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.utils.CompressImageUtils
import com.stt.android.workouts.SyncSingleWorkoutUseCase
import com.stt.android.workouts.setSharingLinkIfPrivate
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class LongScreenshotShareViewModel @Inject constructor(
    private val workoutHeaderController: WorkoutHeaderController,
    private val syncSingleWorkoutUseCase: SyncSingleWorkoutUseCase,
    private val workoutHeaderRepository: WorkoutHeaderRepository,
    private val currentUserController: CurrentUserController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    // ture: prepare finish
    private val _prepareShareLinkEvent = Channel<Pair<Boolean, Boolean>>(Channel.BUFFERED)
    val prepareShareLinkEvent = _prepareShareLinkEvent.receiveAsFlow()

    private val _shareLinkErrorEvent = Channel<Throwable>(Channel.BUFFERED)
    val shareLinkErrorEvent = _shareLinkErrorEvent.receiveAsFlow()

    private val _longScreenshotShareEvent = Channel<Uri>(Channel.BUFFERED)
    val longScreenshotShareEvent = _longScreenshotShareEvent.receiveAsFlow()

    private val _workoutHeaderState = MutableStateFlow<WorkoutHeader?>(null)
    val workoutHeaderState = _workoutHeaderState.asStateFlow()

    private val _userInfoState = MutableStateFlow<User?>(null)
    val userInfoState = _userInfoState.asStateFlow()

    private val _shareImageEnableState = MutableStateFlow(false)
    val shareImageEnable = _shareImageEnableState.asStateFlow()

    fun loadUserInfo() {
        _userInfoState.tryEmit(currentUserController.currentUser)
    }

    fun loadWorkoutHeader(workoutId: Int) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            _workoutHeaderState.value = workoutHeaderRepository.findById(workoutId)
        }
    }

    fun prepareShareLink(isSummary: Boolean = false) {
        _workoutHeaderState.value?.let { workoutHeader ->
            _prepareShareLinkEvent.trySend(false to isSummary)

            viewModelScope.launch {
                runSuspendCatching {
                    setSharingLinkIfPrivate(
                        workoutHeader,
                        workoutHeaderController,
                        syncSingleWorkoutUseCase,
                    )
                    _prepareShareLinkEvent.trySend(true to isSummary)
                }.onFailure { e ->
                    _shareLinkErrorEvent.trySend(e)
                    Timber.w(e, "Error while creating sharing link")
                }
            }
        }
    }

    fun longScreenshotShare(nestedScrollView: NestedScrollView) {
        val bitmap = CapturePictureUtil.screenshotByNestedScrollView(nestedScrollView) ?: return
        viewModelScope.launch(coroutinesDispatchers.io) {
            val fileName = UUID.randomUUID().toString().take(8) + ".jpg"
            _longScreenshotShareEvent.trySend(
                MediaStoreUtils.saveImageToExternalFilesDir(
                    nestedScrollView.context,
                    fileName
                ) { file ->
                    CompressImageUtils.compressImageToLimitedSize(bitmap, file)
                }
            )
        }
    }

    fun setShareImageBtnEnabled(enable: Boolean) {
        _shareImageEnableState.value = enable
    }
}
