package com.stt.android.workout.details.share.analysis

import android.view.View
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.common.viewstate.ViewState
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.ui.fragments.workout.analysis.WorkoutAnalysisHelper
import com.stt.android.ui.utils.WideScreenPaddingDecoration
import com.stt.android.utils.EpoxyNonSharingRecyclerView
import com.stt.android.workout.details.R
import com.stt.android.workout.details.WorkoutAnalysisPagerData
import kotlinx.coroutines.CoroutineScope
import java.lang.ref.WeakReference

@EpoxyModelClass
abstract class LongScreenshotWorkoutAnalysisModel :
    EpoxyModelWithHolder<WorkoutAnalysisViewHolder>() {
    private lateinit var viewHolder: WeakReference<WorkoutAnalysisViewHolder>

    @EpoxyAttribute
    var multisportPartActivity: MultisportPartActivity? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var pagerData: WorkoutAnalysisPagerData

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var lifecycleScope: CoroutineScope? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var longScreenshotWorkoutAnalysisGraphController: LongScreenshotWorkoutAnalysisGraphController

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var workoutAnalysisHelper: WorkoutAnalysisHelper

    override fun getDefaultLayout() = R.layout.model_workout_analysis_share

    override fun bind(holder: WorkoutAnalysisViewHolder) {
        if (::viewHolder.isInitialized) viewHolder.clear()
        viewHolder = WeakReference(holder)
        setupRecyclerView(holder)
    }

    private fun setupRecyclerView(holder: WorkoutAnalysisViewHolder) {
        longScreenshotWorkoutAnalysisGraphController.lifecycleScope = lifecycleScope
        longScreenshotWorkoutAnalysisGraphController.workoutAnalysisHelper = workoutAnalysisHelper
        holder.list.adapter = longScreenshotWorkoutAnalysisGraphController.adapter
        holder.list.addItemDecoration(
            WideScreenPaddingDecoration(
                holder.list.resources,
                holder.list.resources.newTheme()
            )
        )
        longScreenshotWorkoutAnalysisGraphController.setData(
            ViewState.Loaded(pagerData)
        )
    }
}

class WorkoutAnalysisViewHolder : KotlinEpoxyHolder() {
    val root by bind<View>(R.id.container)
    val list by bind<EpoxyNonSharingRecyclerView>(R.id.list)
}
