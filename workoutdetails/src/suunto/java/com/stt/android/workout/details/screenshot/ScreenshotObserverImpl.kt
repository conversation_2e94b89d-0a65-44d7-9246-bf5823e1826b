package com.stt.android.workout.details.screenshot

import android.Manifest
import android.app.Activity
import android.content.ContentResolver
import android.content.Context
import android.database.ContentObserver
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import androidx.annotation.RequiresPermission
import androidx.lifecycle.LifecycleOwner
import com.stt.android.workouts.sharepreview.customshare.ScreenshotObserver
import timber.log.Timber
import java.util.Locale

class ScreenshotObserverImpl(context: Context) : ScreenshotObserver {
    private val internalObserver: ContentObserver =
        MediaContentObserver(MediaStore.Images.Media.INTERNAL_CONTENT_URI)
    private val externalObserver: ContentObserver =
        MediaContentObserver(MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
    private val resolver: ContentResolver = context.applicationContext.contentResolver
    private val handler: Handler = Handler(Looper.getMainLooper())
    private var observable: ScreenshotObserver.ScreenshotObservable? = null
    private var lastData: String? = null

    private val shotCallBack = Runnable {
        observable?.onScreenshot(lastData)
    }

    private val screenCaptureCallback by lazy {
        Activity.ScreenCaptureCallback {
            Timber.d("on screen captured")
            observable?.onScreenshot(null)
        }
    }

    @RequiresPermission(anyOf = [Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.READ_MEDIA_IMAGES])
    override fun onStart(owner: LifecycleOwner) {
        observable = owner as? ScreenshotObserver.ScreenshotObservable
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            (owner as? Activity)?.run {
                registerScreenCaptureCallback(mainExecutor, screenCaptureCallback)
            }
        } else {
            resolver.registerContentObserver(
                MediaStore.Images.Media.INTERNAL_CONTENT_URI,
                true,
                internalObserver
            )
            resolver.registerContentObserver(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                true,
                externalObserver
            )
        }
    }

    override fun onStop(owner: LifecycleOwner) {
        observable = null
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            (owner as? Activity)?.run {
                unregisterScreenCaptureCallback(screenCaptureCallback)
            }
        } else {
            resolver.unregisterContentObserver(internalObserver)
            resolver.unregisterContentObserver(externalObserver)
        }
    }

    private fun handleMediaContentChange(contentUri: Uri) {
        val queryArgs = Bundle().apply {
            putInt(
                ContentResolver.QUERY_ARG_SORT_DIRECTION,
                ContentResolver.QUERY_SORT_DIRECTION_DESCENDING
            )
            putStringArray(
                ContentResolver.QUERY_ARG_SORT_COLUMNS,
                arrayOf(MediaStore.Images.ImageColumns.DATE_ADDED)
            )
            putInt(ContentResolver.QUERY_ARG_OFFSET, 0)
            putInt(ContentResolver.QUERY_ARG_LIMIT, 1)
        }

        resolver.query(contentUri, MEDIA_PROJECTIONS, queryArgs, null)?.use {
            if (it.moveToFirst()) {
                val data =
                    it.getString(it.getColumnIndexOrThrow(MediaStore.Images.ImageColumns.DATA))
                val dateTaken =
                    it.getLong(it.getColumnIndexOrThrow(MediaStore.Images.ImageColumns.DATE_TAKEN))
                val dateAdded =
                    it.getLong(it.getColumnIndexOrThrow(MediaStore.Images.ImageColumns.DATE_ADDED))

                if (lastData != data) {
                    if (dateTaken == 0L || dateTaken == dateAdded * 1000) { // thumbnail
                        handler.removeCallbacks(shotCallBack);
                        handler.post { observable?.onScreenshot(null) }
                    } else if (checkScreenShot(data)) { // screenshot image
                        handler.removeCallbacks(shotCallBack);
                        lastData = data
                        handler.post(shotCallBack)
                    }
                } else if (System.currentTimeMillis() - dateTaken < 7200) { // Changing the resource file name also triggers it and passes over the previous screenshot file, so it's only valid for up to 2 minutes
                    handler.removeCallbacks(shotCallBack);
                    handler.post(shotCallBack)
                }
            }
        }
    }

    private fun checkScreenShot(data: String): Boolean {
        val lowercaseData = data.lowercase(Locale.getDefault())
        return KEYWORDS.any { lowercaseData.contains(it) }
    }

    private inner class MediaContentObserver(private val contentUri: Uri) :
        ContentObserver(null) {
        override fun onChange(selfChange: Boolean) {
            handleMediaContentChange(contentUri)
        }
    }

    companion object {
        private val KEYWORDS = arrayOf(
            "screenshot", "screen_shot", "screen-shot", "screen shot",
            "screencapture", "screen_capture", "screen-capture", "screen capture",
            "screencap", "screen_cap", "screen-cap", "screen cap", "snap", "截屏"
        )

        private val MEDIA_PROJECTIONS = arrayOf(
            MediaStore.Images.ImageColumns.DATA,
            MediaStore.Images.ImageColumns.DATE_TAKEN,
            MediaStore.Images.ImageColumns.DATE_ADDED
        )
    }
}
