package com.stt.android.workout.details.aerobiczone

import android.os.Bundle
import androidx.fragment.app.DialogFragment
import com.stt.android.aerobiczone.AerobicZoneDialogFragmentCreator
import com.stt.android.aerobiczone.AerobicZonesInfoSheet
import javax.inject.Inject

class AerobicZoneDialogFragmentCreatorImpl @Inject constructor() :
    AerobicZoneDialogFragmentCreator {
    companion object {
        const val AEROBIC_ZONE_INFO_SHEET_NAME = "AEROBIC_ZONE_INFO_SHEET_NAME"
        const val AEROBIC_ZONE_INFO_SHEET_WORKOUT_ID = "AEROBIC_ZONE_INFO_SHEET_WORKOUT_ID"
        const val AEROBIC_ZONE_INFO_SHEET_WORKOUT_KEY = "AEROBIC_ZONE_INFO_SHEET_WORKOUT_KEY"
    }

    override fun create(
        dest: AerobicZonesInfoSheet,
        workoutId: Int,
        workoutKey: String?,
    ): DialogFragment {
        return AerobicZoneDialogFragment().apply {
            arguments = Bundle().apply {
                putString(AEROBIC_ZONE_INFO_SHEET_NAME, dest.name)
                putInt(AEROBIC_ZONE_INFO_SHEET_WORKOUT_ID, workoutId)
                workoutKey?.let {
                    putString(AEROBIC_ZONE_INFO_SHEET_WORKOUT_KEY, workoutKey)
                }
            }
        }
    }
}
