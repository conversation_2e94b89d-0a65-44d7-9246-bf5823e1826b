package com.stt.android.workout.details.share.video

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.SurfaceView
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.core.view.children
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.commitNow
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.google.android.material.snackbar.Snackbar
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.ui.SimpleProgressDialogFragment
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMaps
import com.stt.android.maps.SuuntoSupportMapFragment
import com.stt.android.maps.mapbox.MapboxMapsProvider
import com.stt.android.maps.newLatLngBounds
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.multimedia.sportie.SportieShareType
import com.stt.android.ui.map.RouteMarkerHelper
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.STTConstants
import com.stt.android.workout.details.R
import com.stt.android.workout.details.databinding.FragmentVideoShareBinding
import com.stt.android.workout.details.share.WorkoutMapPlaybackActivity
import com.stt.android.workout.details.share.util.ButtonGroupHelper
import com.stt.android.workout.details.share.util.VideoFileUtil
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareTargetListDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import com.stt.android.R as BR

@AndroidEntryPoint
class VideoShareFragment : Fragment() {

    private lateinit var binding: FragmentVideoShareBinding

    private val graphTypeGroupHelper = ButtonGroupHelper<WorkoutMapPlaybackGraphType>()
    private val mapTypeGroupHelper = ButtonGroupHelper<WorkoutMapPlaybackMapType>()

    private val viewModel: VideoShareViewModel by activityViewModels()

    private lateinit var mapFragment: SuuntoSupportMapFragment

    private var lineWidth: Int = 0

    @Inject
    lateinit var workoutShareHelper: WorkoutShareHelper

    @Inject
    lateinit var suuntoMaps: SuuntoMaps

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lineWidth = resources.getDimensionPixelSize(R.dimen.map_playback_color_track_line_width)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentVideoShareBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews()
        initGroupHelpers()
        initObservers()
    }

    override fun onResume() {
        super.onResume()
        viewModel.setShowEditIcon(true)
    }

    private fun initViews() {
        binding.composeView.setContentWithM3Theme {
            val infoData by viewModel.infoData.collectAsState()
            val dataOptions by viewModel.dataOptions.collectAsState()
            val showEditIcon by viewModel.showEditIcon.collectAsState()
            infoData?.let { data ->
                VideoShareInfoView(
                    data = data,
                    options = dataOptions,
                    modifier = Modifier.fillMaxWidth(),
                    showEditIcon = showEditIcon,
                    onHideEditIcon = { viewModel.setShowEditIcon(false) },
                    onClick = {
                        launchMapPlaybackOptions(with(data) { !productName.isNullOrBlank() && productType != null })
                    }
                )
            }
        }
        setupMapFragment()
        binding.mapContainer.setOnClickListener {
            viewModel.setShowEditIcon(true)
        }
        binding.mapContainer.foreground = GradientDrawable(
            GradientDrawable.Orientation.TOP_BOTTOM,
            intArrayOf(
                Color.argb(0.4f, 0f, 0f, 0f),
                Color.TRANSPARENT,
                Color.TRANSPARENT,
                Color.TRANSPARENT,
            ),
        )
        binding.playFab.setOnClickListener {
            launchMapPlayback(false)
        }
        binding.shareVideoBtn.setOnClickListener {
            shareVideoOrLaunchMapPlayback()
        }
        binding.shareSummaryLinkBtn.setOnClickListenerThrottled {
            lifecycleScope.launch {
                showLoading()
                val result = runSuspendCatching {
                    viewModel.prepareShareLink()
                }
                hideLoading()
                with(this@VideoShareFragment) { result.fold(::shareLink, ::showErrorSnackbar) }
            }
        }
    }

    private fun setupMapFragment() {
        val fm = childFragmentManager
        mapFragment = (fm.findFragmentByTag(TAG_MAP_FRAGMENT) as SuuntoSupportMapFragment?) ?: run {
            val options = SuuntoMapOptions(
                mapType = viewModel.mapType.value.toMapType.getBaseMapType(),
                map3dMode = false,
                enable3dLocation = false,
            )
            SuuntoSupportMapFragment.newInstance(options).also {
                fm.commitNow { add(R.id.mapContainer, it, TAG_MAP_FRAGMENT) }
            }
        }
        mapFragment.getMapAsync {
            mapFragment.mapView?.clicksDisabled = true
            // on top of map snapshotter map views, works only with amap
            mapFragment.mapView?.let { findSurfaceView(it) }?.setZOrderMediaOverlay(true)
        }
    }

    private fun findSurfaceView(view: View): SurfaceView? {
        if (view is SurfaceView) return view

        if (view is ViewGroup) {
            view.children.forEach {
                val surfaceView = findSurfaceView(it)
                if (surfaceView != null) {
                    return surfaceView
                }
            }
        }

        return null
    }

    private fun initGroupHelpers() {
        graphTypeGroupHelper.setup(
            binding.graphTypeFabMain,
            listOf(binding.graphTypeFabSub1, binding.graphTypeFabSub2),
            WorkoutMapPlaybackGraphType.entries,
            iconCallback = { it.iconRes },
            onSelected = { viewModel.setGraphType(it) },
        )
        mapTypeGroupHelper.setup(
            binding.mapStyleFabMain,
            listOf(binding.mapStyleFabSub1),
            WorkoutMapPlaybackMapType.entries,
            iconCallback = { it.iconRes },
            onSelected = { viewModel.setMapType(it) },
        )
        if (suuntoMaps.defaultProvider?.name != MapboxMapsProvider.NAME) {
            binding.mapStyleFabMain.isVisible = false
            binding.mapStyleFabSub1.isVisible = false
        }
    }

    private fun initObservers() {
        launchOnStart {
            viewModel.graphType.collectLatest {
                graphTypeGroupHelper.setSelected(it)
            }
        }
        launchOnStart {
            viewModel.mapType.collectLatest { mapType ->
                mapTypeGroupHelper.setSelected(mapType)
            }
        }
        launchOnStart {
            viewModel.cameraBounds.filterNotNull().collectLatest {
                mapFragment.getMapAsync { map ->
                    val padding = resources.getDimensionPixelSize(BR.dimen.size_spacing_xxlarge)
                    map.moveCamera(newLatLngBounds(it, padding))
                }
            }
        }
        launchOnStart {
            viewModel.startEndPoints.filterNotNull().collectLatest { (start, end) ->
                mapFragment.getMapAsync { map ->
                    val context = requireContext()
                    RouteMarkerHelper.drawStartPoint(context, map, start.latLng, false, true)
                    RouteMarkerHelper.drawEndPoint(context, map, end.latLng, true)
                }
            }
        }
        launchOnStart {
            combine(viewModel.mapType, viewModel.colorTrack) { mapType, descriptor ->
                mapType.toMapType.getBaseMapType() to descriptor
            }.collectLatest { (mapType, descriptor) ->
                mapFragment.getMapAsync { map ->
                    map.setMapType(mapType) {
                        descriptor?.let { map.addColorTrack(it, lineWidth.toDouble()) }
                    }
                }
            }
        }
    }

    private fun launchOnStart(block: suspend CoroutineScope.() -> Unit) {
        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED, block)
        }
    }

    private fun shareVideoOrLaunchMapPlayback() {
        lifecycleScope.launch {
            val outputFile = viewModel.getOutputFile()
            val exist = withContext(viewModel.dispatchers.io) {
                outputFile.exists() && outputFile.length() > 0
            }
            if (exist) {
                val context = requireContext()
                VideoFileUtil.getVideoShareUri(context, outputFile).let { uri ->
                    if (workoutShareHelper.hasCustomIntentHandling()) {
                        WorkoutShareTargetListDialogFragment.newInstanceForVideoSharing(uri)
                            .show(childFragmentManager, TAG_VIDEO_SHARE_DIALOG)
                    } else {
                        workoutShareHelper.sendImplicitVideoShareIntent(
                            requireActivity(),
                            uri,
                            SportieShareType.VIDEO_3D,
                        )
                    }
                }
            } else {
                launchMapPlayback(true)
            }
        }
    }

    private fun launchMapPlayback(sharePopup: Boolean) {
        startActivity(
            WorkoutMapPlaybackActivity.newStartIntent(
                requireContext(),
                workoutId = viewModel.workoutId,
                sharePopup = sharePopup,
            )
        )
    }

    private fun launchMapPlaybackOptions(showDeviceOption: Boolean) {
        startActivityForResult(
            WorkoutMapPlaybackOptionsActivity.newStartIntent(requireContext(), showDeviceOption),
            REQUEST_CODE_MAP_PLAYBACK_OPTIONS,
        )
    }

    @Suppress("OVERRIDE_DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            REQUEST_CODE_MAP_PLAYBACK_OPTIONS -> {
                if (resultCode == Activity.RESULT_OK) {
                    viewModel.updateDataOptions()
                }
            }

            else -> super.onActivityResult(requestCode, resultCode, data)
        }
    }

    private fun showLoading() {
        SimpleProgressDialogFragment.newInstance(getString(BR.string.creating_share_link))
            .show(childFragmentManager, SimpleProgressDialogFragment.FRAGMENT_TAG)
    }

    private fun hideLoading() {
        val fm = childFragmentManager
        (fm.findFragmentByTag(SimpleProgressDialogFragment.FRAGMENT_TAG) as? DialogFragment)?.dismiss()
    }

    private fun shareLink(workoutHeader: WorkoutHeader?) {
        if (workoutHeader == null) return
        // share link
        // china flavor should show different platform items, other jump to the system share
        if (workoutShareHelper.hasCustomIntentHandling()) {
            WorkoutShareTargetListDialogFragment.newInstanceForLinkSharing(
                workoutHeader, SportieShareSource.WORKOUT_SUMMARY, 0,
            ).show(childFragmentManager, TAG_SHARE_DIALOG)
        } else {
            workoutShareHelper.sendImplicitWorkoutLinkShareIntent(
                requireActivity(),
                workoutHeader,
                SportieShareSource.WORKOUT_SUMMARY,
                0,
            )
        }
    }

    private fun showErrorSnackbar(error: Throwable) {
        val event = ErrorEvent.get(error::class)
        Snackbar.make(
            view ?: return,
            event.errorStringRes,
            if (event.showCloseButton) Snackbar.LENGTH_INDEFINITE else Snackbar.LENGTH_LONG
        ).apply {
            if (event.showCloseButton) {
                setAction(BR.string.close) {}
            }
        }.show()
    }

    companion object {
        private const val REQUEST_CODE_MAP_PLAYBACK_OPTIONS = 1

        private const val TAG_MAP_FRAGMENT = "map_fragment"
        private const val TAG_SHARE_DIALOG = "share_dialog"
        private const val TAG_VIDEO_SHARE_DIALOG = "video_share_dialog"

        fun newInstance(workoutId: Int): VideoShareFragment {
            return VideoShareFragment().also { fragment ->
                fragment.arguments = Bundle().apply {
                    putInt(STTConstants.ExtraKeys.WORKOUT_ID, workoutId)
                }
            }
        }
    }
}
