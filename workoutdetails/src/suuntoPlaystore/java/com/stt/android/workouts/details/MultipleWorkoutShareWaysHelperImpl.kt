package com.stt.android.workouts.details

import android.content.Context
import com.google.maps.android.PolyUtil
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.ui.extensions.supportWorkoutAnalysisOnMap
import com.stt.android.workout.details.share.MultipleWaysWorkoutShareActivity
import com.stt.android.workouts.sharepreview.WorkoutShareHelperImpl
import javax.inject.Inject

class MultipleWorkoutShareWaysHelperImpl @Inject constructor(
    override val emarsysAnalytics: EmarsysAnalytics,
    override val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    override val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    dispatchers: CoroutinesDispatchers
) : WorkoutShareHelperImpl(
    emarsysAnalytics,
    firebaseAnalyticsTracker,
    amplitudeAnalyticsTracker,
    dispatchers
) {

    override fun showMultipleWorkoutShareWays(): Boolean = true
    override fun toMultipleWorkoutShareWays(
        context: Context,
        workoutHeader: WorkoutHeader,
        imageIndex: Int,
        watchName: String,
        byScreenshot: Boolean,
    ) {
        val points = workoutHeader.polyline
            ?.takeUnless(String::isEmpty)
            ?.let(PolyUtil::decode)
            ?: emptyList()
        val intentAndOptions = MultipleWaysWorkoutShareActivity.getIntent(
            workoutHeaderId = workoutHeader.id,
            context = context,
            itemIndex = imageIndex,
            workoutDetails = if (byScreenshot) SportieShareSource.WORKOUT_DETAILS_SCREENSHOT else SportieShareSource.WORKOUT_DETAILS,
            watchName = watchName,
            supportVideoShare = workoutHeader.supportWorkoutAnalysisOnMap(points),
        )

        val intent = intentAndOptions.first
        val options = intentAndOptions.second
        if (intent != null && options != null) {
            context.startActivity(intent, options.toBundle())
        }
    }
}
