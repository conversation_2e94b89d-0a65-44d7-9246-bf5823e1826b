package com.stt.android.suuntoplusstore.firsttimeuse.domain.usecases

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.utils.STTConstants.SuuntoPreferences.KEY_SUUNTO_PLUS_STORE_FIRST_TIME_USE_DIALOG_SHOWN
import javax.inject.Inject

class ShouldShowFirstTimeUseDialogUseCase
@Inject constructor(
    @SuuntoSharedPrefs private val suuntoSharedPreferences: SharedPreferences,
) {
    fun shouldShowFirstTimeUseDialog() = !suuntoSharedPreferences.getBoolean(
        KEY_SUUNTO_PLUS_STORE_FIRST_TIME_USE_DIALOG_SHOWN,
        false
    )

    fun markFirstTimeUseDialogAsShown() = suuntoSharedPreferences.edit {
        putBoolean(KEY_SUUNTO_PLUS_STORE_FIRST_TIME_USE_DIALOG_SHOWN, true)
    }
}
