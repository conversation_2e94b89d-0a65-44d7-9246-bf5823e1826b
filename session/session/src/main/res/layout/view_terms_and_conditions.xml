<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/signup_tos_text"
        style="@style/Body.Medium.URL"
        android:layout_width="match_parent"
        android:gravity="center"
        android:minHeight="@dimen/height_button"
        android:paddingStart="@dimen/size_spacing_xlarge"
        android:paddingEnd="@dimen/size_spacing_xlarge"
        android:transitionName="@string/transition_name_terms_and_conditions"
        app:linkHtmlToCustomTab="@{@string/tos_and_privacy_links}"
        tools:text="Terms of Service and Privacy Policy" />
</layout>
