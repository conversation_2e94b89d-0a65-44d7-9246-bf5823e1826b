package com.stt.android.session.phonenumberverification.existinguser

import android.content.Context
import android.content.Intent
import com.stt.android.session.PhoneNumberVerificationForExistingUserHook
import javax.inject.Inject

class PhoneNumberVerificationForExistingUserHookImpl
@Inject constructor() : PhoneNumberVerificationForExistingUserHook {
    override fun newStartIntent(context: Context): Intent =
        Intent(context, PhoneNumberVerificationForExistingUserActivity::class.java)
}
