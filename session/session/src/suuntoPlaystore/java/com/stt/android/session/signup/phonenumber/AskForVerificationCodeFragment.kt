package com.stt.android.session.signup.phonenumber

import android.content.IntentFilter
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.gms.auth.api.phone.SmsRetrieverClient
import com.google.android.material.snackbar.Snackbar
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.common.ui.observeNotNull
import com.stt.android.exceptions.remote.STTError
import com.stt.android.extensions.hideKeyboard
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.session.R
import com.stt.android.session.SessionInitializerResult
import com.stt.android.session.signin.SignInOnboardingViewModel
import com.stt.android.session.databinding.FragmentPhoneNumberAskForVerificationCodeBinding
import com.stt.android.session.phonenumberverification.SmsBroadcastReceiver
import com.stt.android.session.setupLoginFlowTransitions
import com.stt.android.session.showSnackBar
import com.stt.android.session.signin.SignInActivity
import com.stt.android.utils.OnActionDone
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Named

/**
 * Fragment for phone number sign-up flow. Shown after the phone verification code has been sent
 * as an SMS.
 */
@AndroidEntryPoint
class AskForVerificationCodeFragment : ViewModelFragment2() {

    private val args: AskForVerificationCodeFragmentArgs by navArgs()

    @Inject
    lateinit var homeActivityNavigator: HomeActivityNavigator

    @JvmField
    @Inject
    @Named("GooglePlayServicesAvailable")
    var isGooglePlayServicesAvailable = false

    @Inject
    lateinit var smsRetrieverClient: dagger.Lazy<SmsRetrieverClient>

    @Inject
    lateinit var smsBroadcastReceiver: dagger.Lazy<SmsBroadcastReceiver>

    override val viewModel: SignInOnboardingViewModel by activityViewModels()

    private val viewDataBinding: FragmentPhoneNumberAskForVerificationCodeBinding get() = requireBinding()

    override fun getLayoutResId() = R.layout.fragment_phone_number_ask_for_verification_code

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        (activity as? AppCompatActivity)?.setSupportActionBar(
            viewDataBinding.phoneNumberVerificationCodeToolbar.signupToolbar
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupLoginFlowTransitions()
        setupUi()
        initAutomaticSmsVerification()
    }

    override fun onDestroyView() {
        super.onDestroyView()

        terminateAutomaticSmsVerification()
    }

    private fun setupUi() {
        val vm = viewModel
        with(vm) {
            signUpWithPhoneNumberState.observeNotNull(
                viewLifecycleOwner,
                ::handleLoginOrSignupState
            )

            loginPhoneState.observeNotNull(viewLifecycleOwner, ::handleLoginOrSignupState)
            resendingVerificationSmsState.observeNotNull(viewLifecycleOwner, ::handleResendingCodeState)
        }

        with(viewDataBinding) {
            doLogin = args.login
            phoneNumber = vm.phoneNumberValue
            onLoginOrSignUpClicked = View.OnClickListener { handleLoginOrSignUpClicked() }
            onEditPhoneNumberClicked = View.OnClickListener {
                vm.usePhoneNumberLogin(reset = true)
                findNavController().popBackStack(R.id.continueWithEmailOrPhoneFragment, false)
            }
            onActionDone = OnActionDone { _, _ ->
                if (vm.canSignupWithVerificationCode.value == true) {
                    handleLoginOrSignUpClicked()
                }
                false
            }
        }
    }

    private fun handleLoginOrSignUpClicked() {
        if (args.login) {
            viewModel.loginWithPhoneNumber()
        } else {
            viewModel.signUpWithPhoneNumber()
        }
    }

    private fun initAutomaticSmsVerification() {
        if (isGooglePlayServicesAvailable) {
            smsRetrieverClient.get().startSmsRetriever().addOnFailureListener {
                Timber.w(it, "Start SmsRetriever failed %s", it.message)
            }
            smsBroadcastReceiver.get().onVerificationCodeReceived = {
                viewModel.phoneNumberVerificationCode.value = it
                handleLoginOrSignUpClicked()
            }
            ContextCompat.registerReceiver(
                requireActivity(),
                smsBroadcastReceiver.get(),
                IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION),
                ContextCompat.RECEIVER_EXPORTED
            )
        }
    }

    private fun terminateAutomaticSmsVerification() {
        if (isGooglePlayServicesAvailable) {
            smsBroadcastReceiver.get().onVerificationCodeReceived = null
            activity?.unregisterReceiver(smsBroadcastReceiver.get())
        }
    }

    private fun handleLoginOrSignupState(state: LiveDataSuspendState<SessionInitializerResult>) {
        state.ifNotHandled {
            if (state is LiveDataSuspendState.Success) {
                (activity as? SignInActivity)?.handleSessionInitSuccess(state.successValue)
            } else if (state is LiveDataSuspendState.Failure) {
                handleLoginError(state.throwable)
            }
        }
    }

    private fun handleResendingCodeState(state: LiveDataSuspendState<Unit>) {
        state.ifNotHandled {
            if (state is LiveDataSuspendState.Success) {
                activity?.hideKeyboard()
                Snackbar.make(
                    viewDataBinding.root,
                    R.string.phone_num_verification_new_code_sent,
                    Snackbar.LENGTH_LONG
                ).show()
            } else if (state is LiveDataSuspendState.Failure) {
                activity?.hideKeyboard()
                view?.showSnackBar(state.throwable)
            }
        }
    }

    private fun handleLoginError(throwable: Throwable) {
        if (throwable !is STTError.InvalidPinCode && throwable !is IllegalArgumentException) {
            view?.showSnackBar(throwable)
        }
    }
}
