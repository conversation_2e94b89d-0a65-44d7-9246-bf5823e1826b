package com.stt.android.session.login.email

import android.app.Activity
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.common.ui.observeNotNull
import com.stt.android.exceptions.remote.STTError
import com.stt.android.extensions.hideKeyboard
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.newemail.NewEmailActivity
import com.stt.android.newemail.isEmailValid
import com.stt.android.session.InputError
import com.stt.android.session.R
import com.stt.android.session.SessionInitializerResult
import com.stt.android.session.databinding.FragmentLoginWithEmailBinding
import com.stt.android.session.setupLoginFlowTransitions
import com.stt.android.session.showSnackBar
import com.stt.android.session.signin.SignInActivity
import com.stt.android.session.signin.SignInOnboardingViewModel
import com.stt.android.utils.OnActionDone
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class LoginWithEmailFragment : ViewModelFragment2() {

    @Inject
    lateinit var homeActivityNavigator: HomeActivityNavigator

    private val args: LoginWithEmailFragmentArgs by navArgs()

    override val viewModel: SignInOnboardingViewModel by activityViewModels()

    private val viewDataBinding: FragmentLoginWithEmailBinding get() = requireBinding()

    override fun getLayoutResId() = R.layout.fragment_login_with_email

    private val changeEmailLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            val email = result.data?.getStringExtra(NewEmailActivity.EXTRA_EMAIL_KEY)
            if (result.resultCode == Activity.RESULT_OK && !email.isNullOrEmpty()) {
                <EMAIL>?.let {
                    (activity as? SignInActivity)?.handleSessionInitSuccess(it)
                }
            }
        }

    private var result: SessionInitializerResult? = null

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        (activity as? AppCompatActivity)?.setSupportActionBar(
            viewDataBinding.loginEmailAppbar.signupToolbar
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupLoginFlowTransitions()
        setupUi()
    }

    private fun setupUi() {
        viewModel.resetPasswordInputError()
        viewModel.loginState.observeNotNull(viewLifecycleOwner, ::handleLoginState)

        args.email?.run {
            viewModel.rawEmailOrUsername.value = this
        }

        viewModel.password.value = args.password ?: ""
        args.sttError?.let {
            viewModel.loginPasswordInputError.value = InputError.WithThrowable(it)
            handleLoginError(it)
        }

        viewDataBinding.onLoginClicked = View.OnClickListener {
            activity?.hideKeyboard()
            continueWithEmailLogin()
        }

        viewDataBinding.onActionDone = OnActionDone { _, _ ->
            if (viewModel.canLogin.value == true) {
                continueWithEmailLogin()
            }
            // return false to onEditorAction means the action is not going to be handled again;
            // In this case, return false to hide keyboard when action is EditorInfo.IME_ACTION_DONE.
            false
        }

        viewModel.logLoginWithEmailScreen()
        viewModel.emailChecked.observeNotNull(viewLifecycleOwner) {
            if (it) {
                result?.let { sessionResult -> (activity as? SignInActivity)?.handleSessionInitSuccess(sessionResult) }
            }
        }
        viewModel.needReLogin.observeNotNull(viewLifecycleOwner) {
            if (it) {
                findNavController().popBackStack()
                viewModel.setReLogin(false)
            }
        }
    }

    private fun continueWithEmailLogin() {
        if (viewModel.checkInputForSignIn()) {
            viewModel.loginWithEmail()
        }
    }

    private fun handleLoginState(state: LiveDataSuspendState<SessionInitializerResult>) = state.ifNotHandled {
        when (state) {
            is LiveDataSuspendState.Success -> {
                if (viewModel.emailChecked.value == false) {
                    lifecycleScope.launch {
                        val email = viewModel.getUserEmail()
                        if (isEmailValid(email)) {
                            findNavController()
                                .navigate(LoginWithEmailFragmentDirections.actionEmailCheck(email, true))
                        } else {
                            changeEmailLauncher.launch(NewEmailActivity.newStartIntent(requireContext()))
                        }

                        result = state.successValue
                    }
                } else {
                    (activity as? SignInActivity)?.handleSessionInitSuccess(state.successValue)
                }
            }

            is LiveDataSuspendState.Failure -> handleLoginError(state.throwable)

            is LiveDataSuspendState.Idle,
            is LiveDataSuspendState.InProgress -> Unit
        }
    }

    private fun handleLoginError(throwable: Throwable) {
        if (throwable !is STTError.InvalidUserPassword) {
            view?.showSnackBar(throwable)
        } else {
            Timber.d("InvalidUserPassword - showing as loginPasswordInputError")
        }
    }
}
