package com.stt.android.session.signup.phonenumber

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.activityViewModels
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.common.ui.observeNotNull
import com.stt.android.exceptions.remote.STTError
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.session.R
import com.stt.android.session.SessionInitializerResult
import com.stt.android.session.signin.SignInOnboardingViewModel
import com.stt.android.session.databinding.FragmentPhoneNumberAskForPasswordBinding
import com.stt.android.session.setupLoginFlowTransitions
import com.stt.android.session.showSnackBar
import com.stt.android.session.signin.SignInActivity
import com.stt.android.utils.OnActionDone
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

/**
 * Fragment for phone number sign-up flow. Shown when the phone number is UNVERIFIED and given
 * email exists on the backend.
 */
@AndroidEntryPoint
class AskForPasswordFragment : ViewModelFragment2() {

    @Inject
    lateinit var homeActivityNavigator: HomeActivityNavigator

    override val viewModel: SignInOnboardingViewModel by activityViewModels()

    private val viewDataBinding: FragmentPhoneNumberAskForPasswordBinding get() = requireBinding()

    override fun getLayoutResId() = R.layout.fragment_phone_number_ask_for_password

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        (activity as? AppCompatActivity)?.setSupportActionBar(
            viewDataBinding.phoneAskForPasswordToolbar.signupToolbar
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupLoginFlowTransitions()
        setupUi()
    }

    private fun setupUi() {
        viewModel.loginState.observeNotNull(viewLifecycleOwner, ::handleLoginState)

        viewDataBinding.onLoginClicked = View.OnClickListener {
            continueWithEmailLogin()
        }
        viewDataBinding.onActionDone = OnActionDone { _, _ ->
            if (viewModel.canLogin.value == true) {
                continueWithEmailLogin()
            }
            false
        }
    }

    private fun continueWithEmailLogin() {
        if (viewModel.checkInputForSignIn()) {
            viewModel.loginWithEmail()
        }
    }

    private fun handleLoginState(state: LiveDataSuspendState<SessionInitializerResult>) {
        state.ifNotHandled {
            if (state is LiveDataSuspendState.Success) {
                (activity as? SignInActivity)?.handleSessionInitSuccess(state.successValue)
            } else if (state is LiveDataSuspendState.Failure) {
                handleLoginError(state.throwable)
            }
        }
    }

    private fun handleLoginError(throwable: Throwable) {
        if (throwable !is STTError.InvalidUserPassword) {
            view?.showSnackBar(throwable)
        } else {
            Timber.d("InvalidUserPassword - showing as loginPasswordInputError")
        }
    }
}
