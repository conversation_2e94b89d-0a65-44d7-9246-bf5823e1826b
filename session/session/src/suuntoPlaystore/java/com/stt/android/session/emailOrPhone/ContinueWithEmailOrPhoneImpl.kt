package com.stt.android.session.emailOrPhone

import androidx.annotation.StringRes
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.coroutines.LiveDataSuspend
import com.stt.android.common.coroutines.ViewModelScopeProvider
import com.stt.android.common.coroutines.liveDataSuspend
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.session.EmailOrUsernameStatus
import com.stt.android.domain.session.FetchEmailStatusUseCase
import com.stt.android.domain.session.FetchPhoneNumberStatusUseCase
import com.stt.android.domain.session.FetchUsernameStatusUseCase
import com.stt.android.domain.session.LoginMethod
import com.stt.android.domain.session.PhoneNumberStatus
import com.stt.android.domain.session.UsernameStatus
import com.stt.android.domain.session.phonenumberverification.RequestPhoneNumberVerificationSMSUseCase
import com.stt.android.extensions.combineLatest
import com.stt.android.lifecycle.mapAndObserve
import com.stt.android.session.InputError
import com.stt.android.session.R
import com.stt.android.session.SignInUserData
import com.stt.android.session.configuration.SignInConfiguration
import com.stt.android.session.logOnboardingStartScreenAnalytics
import com.stt.android.session.logOnboardingUsernameNotFound
import com.stt.android.session.logSignupScreenEventAnalytics
import kotlinx.coroutines.CoroutineScope
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR

class ContinueWithEmailOrPhoneImpl
@Inject constructor(
    private val fetchEmailStatusUseCase: FetchEmailStatusUseCase,
    private val fetchUsernameStatusUseCase: FetchUsernameStatusUseCase,
    private val fetchPhoneNumberStatusUseCase: FetchPhoneNumberStatusUseCase,
    private val requestPhoneNumberVerificationSMSUseCase: RequestPhoneNumberVerificationSMSUseCase,
    private val signInUserData: SignInUserData,
    private val config: SignInConfiguration,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    override val viewModelScope: CoroutineScope,
    dispatchers: CoroutinesDispatchers
) : ContinueWithEmailOrPhone, ViewModelScopeProvider, CoroutinesDispatchers by dispatchers {

    private val checkEmailOrUsernameSuspend: LiveDataSuspend<ContinueWithEmailOrPhone.ContinueAction> =
        liveDataSuspend(io) {
            signInUserData.activeBrandForEmail.postValue(null)
            val emailOrUsername = signInUserData.emailOrUsername.value
            val isEmailValid = signInUserData.emailValid.value == true
            Timber.d("Checking if account exists for username/email '$emailOrUsername'")
            if (emailOrUsername.isNullOrBlank()) {
                // Empty field should trigger "Invalid email" error state for the input layout
                emailInputError.postValue(InputError(BaseR.string.required))
                ContinueWithEmailOrPhone.ContinueAction.INVALID_INPUT
            } else {
                runSuspendCatching {
                    if (config.supportsUsernameLogin && !isEmailValid) {
                        // Check username status, if enabled
                        checkUserNameStatus(emailOrUsername)
                    } else {
                        // Check email status
                        checkEmailStatus(emailOrUsername)
                    }
                }.getOrElse { e ->
                    Timber.w(e, "Failed to get email status")
                    // Failed to check if account exists
                    throw e
                }
            }
        }

    private suspend fun checkUserNameStatus(
        username: String
    ): ContinueWithEmailOrPhone.ContinueAction {
        signInUserData.emailExistsInMovescount.postValue(false)
        val usernameStatus = fetchUsernameStatusUseCase(username)
        Timber.d("Got usernameStatus: $usernameStatus")
        when (usernameStatus) {
            UsernameStatus.USER_EXISTS -> {
                logSignupScreenEventAnalytics(
                    EmailOrUsernameStatus(usernameStatus = usernameStatus),
                    null,
                    emarsysAnalytics,
                    amplitudeAnalyticsTracker,
                    LoginMethod.EMAIL
                )

                return ContinueWithEmailOrPhone.ContinueAction.PROCEED_TO_EMAIL_LOGIN
            }
            UsernameStatus.USER_DOES_NOT_EXIST -> {
                logOnboardingUsernameNotFound(emarsysAnalytics, amplitudeAnalyticsTracker)
                // Username does not exist, show error
                emailInputError.postValue(InputError(R.string.username_does_not_exist))

                return ContinueWithEmailOrPhone.ContinueAction.INVALID_INPUT
            }
        }
    }

    private suspend fun checkEmailStatus(
        email: String
    ): ContinueWithEmailOrPhone.ContinueAction {
        val emailStatus = fetchEmailStatusUseCase(email)
        Timber.d("Got account status: $emailStatus")
        signInUserData.emailExistsInMovescount.postValue(
            emailStatus.existsInMovescount
        )
        logSignupScreenEventAnalytics(
            EmailOrUsernameStatus(emailStatus = emailStatus),
            null,
            emarsysAnalytics,
            amplitudeAnalyticsTracker,
            LoginMethod.EMAIL
        )

        signInUserData.activeBrandForEmail.postValue(emailStatus.activeBrand)
        return if (emailStatus.existsInAsko) {
            // Asko account exists, an existing ST, Suunto or Atomic user
            ContinueWithEmailOrPhone.ContinueAction.PROCEED_TO_EMAIL_LOGIN
        } else {
            // Old Movescount account or new user -> need to sign up
            ContinueWithEmailOrPhone.ContinueAction.PROCEED_TO_EMAIL_SIGN_UP
        }
    }

    private val checkPhoneNumberSuspend: LiveDataSuspend<ContinueWithEmailOrPhone.ContinueAction> =
        liveDataSuspend(io) {
            val phoneNumber = signInUserData.phoneNumberValue

            if (phoneNumber == null) {
                Timber.d("Missing region or nationalNumber")
                return@liveDataSuspend ContinueWithEmailOrPhone.ContinueAction.INVALID_INPUT
            }

            Timber.d("Checking if account exists for phone number '$phoneNumber'")
            return@liveDataSuspend try {
                val phoneNumberStatus = fetchPhoneNumberStatusUseCase(phoneNumber)
                if (phoneNumberStatus != PhoneNumberStatus.VERIFIED) {
                    logSignupScreenEventAnalytics(
                        null,
                        phoneNumberStatus,
                        emarsysAnalytics,
                        amplitudeAnalyticsTracker,
                        LoginMethod.PHONE
                    )
                }
                Timber.d("Got phone number status: $phoneNumberStatus")
                when (phoneNumberStatus) {
                    PhoneNumberStatus.MISSING -> {
                        Timber.d("Phone number status MISSING: continue to sign-up")
                        ContinueWithEmailOrPhone.ContinueAction.ASK_NAME_FOR_PHONE_NUMBER_SIGN_UP
                    }
                    PhoneNumberStatus.UNVERIFIED -> {
                        Timber.d("Phone number status UNVERIFIED: continue to ask for email")
                        ContinueWithEmailOrPhone.ContinueAction.ASK_EMAIL_FOR_PHONE_NUMBER_SIGN_UP
                    }
                    PhoneNumberStatus.VERIFIED -> {
                        Timber.d("Phone number status VERIFIED: Sending verification SMS")
                        requestPhoneNumberVerificationSMSUseCase(phoneNumber)
                        signInUserData.updateVerificationCodeSentTimestamp()
                        ContinueWithEmailOrPhone.ContinueAction.ASK_CODE_FOR_PHONE_NUMBER_LOGIN
                    }
                }
            } catch (e: Exception) {
                Timber.w(e, "Failed to get phone number status")
                throw e
            }
        }

    @StringRes
    override val continueWithEmailHint = if (config.supportsUsernameLogin) {
        BaseR.string.email_or_username
    } else {
        BaseR.string.email
    }

    override val emailInputError = MutableLiveData<InputError>()
    override val phoneNumberInputError = MutableLiveData<InputError>()
    override val preApprovedTermsAndConditions = MutableLiveData<Boolean>(false)

    override val emailOrUsernameStatusState = checkEmailOrUsernameSuspend.state
    override val phoneNumberStatusState = checkPhoneNumberSuspend.state
    override val fetchEmailOrPhoneNumberStatusInProgress =
        combineLatest(
            checkEmailOrUsernameSuspend.state,
            checkPhoneNumberSuspend.state
        ).map { (email, phone) ->
            email.isInProgress || phone.isInProgress
        }

    override fun preApprovedTermsAndConditionsChanged(checked: Boolean) {
        preApprovedTermsAndConditions.value = checked
    }

    override fun continueWithEmailOrPhoneNumber() {
        if (signInUserData.loginMethod.value == LoginMethod.PHONE) {
            continueWithPhoneNumber()
        } else {
            continueWithEmail()
        }
    }

    override fun cancelFetchingAccountStatus() {
        checkEmailOrUsernameSuspend.cancel()
        checkPhoneNumberSuspend.cancel()
    }

    private fun continueWithEmail() {
        if (config.supportsUsernameLogin || signInUserData.emailValid.value == true) {
            // User inputted valid email, or alternatively account login is supported: check
            // if account exists
            checkEmailOrUsernameSuspend.run()
        } else {
            // User has not inputted a valid email address. Show input error.
            emailInputError.value = InputError(BaseR.string.invalid_email)
        }
    }

    private fun continueWithPhoneNumber() {
        val inputError = signInUserData.validateAndGetPhoneNumberInputError()
        if (inputError is InputError.None) {
            checkPhoneNumberSuspend.run()
        } else {
            phoneNumberInputError.value = inputError
        }
    }

    override val canContinue: LiveData<Boolean> =
        combineLatest(
            fetchEmailOrPhoneNumberStatusInProgress,
            preApprovedTermsAndConditions
        ).mapAndObserve { (fetchInProgress, approvedTerms) ->
            !fetchInProgress && (approvedTerms || !config.forcePreApproveOfTermsAndConditions)
        }

    override fun logOnboardingStartScreen() {
        logOnboardingStartScreenAnalytics(emarsysAnalytics, amplitudeAnalyticsTracker)
    }

    init {
        // Reset input error when user edits text
        signInUserData.emailOrUsername.distinctUntilChanged().mapAndObserve {
            emailInputError.value = InputError.None
        }

        signInUserData.nationalNumber.distinctUntilChanged().mapAndObserve {
            phoneNumberInputError.value = InputError.None
        }
    }
}
