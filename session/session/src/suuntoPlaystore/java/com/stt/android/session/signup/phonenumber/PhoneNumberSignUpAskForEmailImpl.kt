package com.stt.android.session.signup.phonenumber

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.coroutines.LiveDataSuspend
import com.stt.android.common.coroutines.ViewModelScopeProvider
import com.stt.android.common.coroutines.liveDataSuspend
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.session.FetchEmailStatusUseCase
import com.stt.android.domain.session.phonenumberverification.RequestPhoneNumberVerificationSMSUseCase
import com.stt.android.extensions.combineLatest
import com.stt.android.lifecycle.mapAndObserve
import com.stt.android.session.InputError
import com.stt.android.session.SignInUserData
import com.stt.android.session.logAskForEmailForUnverifiedPhoneNumberAnalytics
import kotlinx.coroutines.CoroutineScope
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR

class PhoneNumberSignUpAskForEmailImpl
@Inject constructor(
    private val fetchEmailStatusUseCase: FetchEmailStatusUseCase,
    private val requestPhoneNumberVerificationSMSUseCase: RequestPhoneNumberVerificationSMSUseCase,
    private val signInUserData: SignInUserData,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    override val viewModelScope: CoroutineScope
) : PhoneNumberSignUpAskForEmail, ViewModelScopeProvider {

    private val checkEmailSuspend: LiveDataSuspend<PhoneNumberSignUpAskForEmail.ContinueAction> =
        liveDataSuspend {
            val email = signInUserData.emailOrUsername.value
            Timber.d("Checking if account exists for username/email '$email'")
            when {
                email.isNullOrBlank() -> {
                    // Empty field should trigger "Invalid email" error state for the input layout
                    phoneNumberSignUpEmailInputError.value = InputError(
                        BaseR.string.required
                    )
                    PhoneNumberSignUpAskForEmail.ContinueAction.INVALID_INPUT
                }

                signInUserData.emailValid.value != true -> {
                    // Email address syntax is wrong
                    phoneNumberSignUpEmailInputError.value = InputError(
                        BaseR.string.invalid_email
                    )
                    PhoneNumberSignUpAskForEmail.ContinueAction.INVALID_INPUT
                }

                else ->
                    runSuspendCatching {
                        val status = fetchEmailStatusUseCase(email)
                        Timber.d("Got account status: $status")
                        if (status.existsInAsko) {
                            // Asko account exists, an existing ST, Suunto or Atomic user
                            PhoneNumberSignUpAskForEmail.ContinueAction.ASK_FOR_PASSWORD
                        } else {
                            // Continue with phone number based sign-up. Ask for full name.
                            PhoneNumberSignUpAskForEmail.ContinueAction.ASK_FOR_NAME
                        }
                    }.getOrElse { e ->
                        Timber.w(e, "Failed to get email status")
                        throw e
                    }
            }
        }

    private val sendVerificationSmsSuspend: LiveDataSuspend<Unit> = liveDataSuspend {
        Timber.d("Sending verification SMS for phone number based sign-up")
        try {
            val phoneNumber = signInUserData.phoneNumberValue
                ?: throw IllegalArgumentException("Missing phone number")
            requestPhoneNumberVerificationSMSUseCase(phoneNumber)
            signInUserData.updateVerificationCodeSentTimestamp()
            Timber.d("Verification SMS sent")
        } catch (e: Exception) {
            verificationSmsSent.value = false
            Timber.w(e, "Requesting SMS verification failed")
        }
    }

    private val resendVerificationSmsSuspend: LiveDataSuspend<Unit> = liveDataSuspend {
        Timber.d("Resending verification SMS")
        try {
            val phoneNumber = signInUserData.phoneNumberValue
                ?: throw IllegalArgumentException("Missing phone number")
            requestPhoneNumberVerificationSMSUseCase(phoneNumber)
            signInUserData.updateVerificationCodeSentTimestamp()
            Timber.d("Verification SMS sent")
        } catch (e: Exception) {
            verificationSmsSent.value = false
            Timber.w(e, "Requesting SMS verification failed")
        }
    }

    override val sendingVerificationSmsState = sendVerificationSmsSuspend.state
    override val resendingVerificationSmsState = resendVerificationSmsSuspend.state
    override val checkEmailForPhoneNumberLoginState = checkEmailSuspend.state
    override val phoneNumberSignUpEmailInputError = MutableLiveData<InputError>()
    override val verificationSmsSent = MutableLiveData(false)

    override val enableResendingVerificationCode: LiveData<Boolean> = combineLatest(
        signInUserData.resendVerificationCodeTimer,
        resendingVerificationSmsState
    ).map { (timer, state) ->
        timer == 0L && !state.isInProgress
    }

    override val emailStatusForPhoneNumberSignUpInProgress: LiveData<Boolean> =
        checkEmailSuspend.state.mapAndObserve { it.isInProgress }

    override val phoneSignUpVerificationSmsSendingInProgress: LiveData<Boolean> =
        sendVerificationSmsSuspend.state.mapAndObserve { it.isInProgress }

    override fun checkEmailStatusForPhoneNumberSignUp() {
        checkEmailSuspend.run()
    }

    override fun sendPhoneSignUpVerificationSms() {
        verificationSmsSent.value = true
        sendVerificationSmsSuspend.run()
    }

    override fun cancelSendingVerificationSms() {
        sendVerificationSmsSuspend.cancel()
        resendVerificationSmsSuspend.cancel()
        verificationSmsSent.value = false
    }

    override fun resendVerificationCode() {
        verificationSmsSent.value = true
        resendVerificationSmsSuspend.run()
    }

    override fun logAskForEmailForUnverifiedPhoneNumber() {
        logAskForEmailForUnverifiedPhoneNumberAnalytics(emarsysAnalytics, amplitudeAnalyticsTracker)
    }
}
