package com.stt.android.session.signup.phonenumber

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.coroutines.LiveDataSuspend
import com.stt.android.common.coroutines.ViewModelScopeProvider
import com.stt.android.common.coroutines.liveDataSuspend
import com.stt.android.domain.UserSession
import com.stt.android.domain.session.LoginMethod
import com.stt.android.domain.session.LoginWithPhoneNumberUseCase
import com.stt.android.domain.session.SessionInitType
import com.stt.android.domain.session.SignupWithPhoneNumberUseCase
import com.stt.android.domain.session.phonenumberverification.PhoneNumberVerificationParameter
import com.stt.android.domain.session.phonenumberverification.VerifyPhoneNumberUseCase
import com.stt.android.exceptions.remote.STTError
import com.stt.android.lifecycle.mapAndObserve
import com.stt.android.session.InputError
import com.stt.android.session.SessionInitializer
import com.stt.android.session.SessionInitializerResult
import com.stt.android.session.SignInUserData
import com.stt.android.session.logLoginOrSignupFailedToAnalytics
import kotlinx.coroutines.CoroutineScope
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR

class SignUpWithPhoneNumberImpl
@Inject constructor(
    private val signInUserData: SignInUserData,
    private val signupWithPhoneNumberUseCase: SignupWithPhoneNumberUseCase,
    private val verifyPhoneNumberUseCase: VerifyPhoneNumberUseCase,
    private val loginWithPhoneNumberUseCase: LoginWithPhoneNumberUseCase,
    private val sessionInitializer: SessionInitializer,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    override val viewModelScope: CoroutineScope,
    dispatchers: CoroutinesDispatchers
) : SignUpWithPhoneNumber, ViewModelScopeProvider, CoroutinesDispatchers by dispatchers {

    private val signUpWithPhoneNumberSuspend: LiveDataSuspend<SessionInitializerResult> =
        liveDataSuspend {
            try {
                val fullName = signInUserData.fullName.value.takeUnless { it.isNullOrBlank() }
                    ?: throw IllegalArgumentException("Missing name")
                val phoneNumber = signInUserData.phoneNumberValue
                    ?: throw IllegalArgumentException("Missing phone number")
                val code =
                    signInUserData.phoneNumberVerificationCode.value.takeUnless { it.isNullOrBlank() }

                if (code == null) {
                    signUpVerificationCodeError.value = InputError(BaseR.string.required)
                    throw IllegalArgumentException("Missing verification code")
                }

                val verificationToken = try {
                    verifyPhoneNumberUseCase(PhoneNumberVerificationParameter(phoneNumber, code))
                } catch (e: Exception) {
                    if (e is STTError.InvalidPinCode) {
                        signUpVerificationCodeError.value = InputError(BaseR.string.error_1412)
                    }

                    throw e
                }

                /*
                it's possible user has input the email during phone number signup flow
                (in case the phone number was existing in backend and not verified) so let's
                make sure we save it if it is a valid email.
                 */
                val email = signInUserData.emailOrUsername.value.takeIf {
                    signInUserData.emailValid.value == true
                }

                Timber.d("Signing up new user: phoneNumber=$phoneNumber fullName=$fullName")
                val newUser = signupWithPhoneNumberUseCase(
                    fullName = fullName,
                    phoneNumber = phoneNumber,
                    phoneNumberVerificationToken = verificationToken,
                    email = email
                )
                Timber.d("Sign-up done: user=$newUser")

                val session = loginWithPhoneNumberUseCase(phoneNumber, verificationToken)
                Timber.d("Login-in done: session=$session")

                sessionInitializer.initialiseSession(
                    userSession = UserSession.fromDomainSession(session),
                    loginMethod = LoginMethod.PHONE,
                    sessionInitType = SessionInitType.SIGNUP,
                    existsInMovescount = false,
                )
            } catch (e: Exception) {
                Timber.w(e, "Error during sign-up")
                logLoginOrSignupFailedToAnalytics(
                    firebaseAnalyticsTracker,
                    amplitudeAnalyticsTracker,
                    AnalyticsEvent.SIGN_UP_ERROR,
                    LoginMethod.PHONE,
                    e
                )
                // propagate to UI
                throw e
            }
        }

    override val signUpWithPhoneNumberState = signUpWithPhoneNumberSuspend.state

    override val signUpWithPhoneNumberInProgress = signUpWithPhoneNumberState.map {
        // For signing up, consider Success state also as a kind of 'in progress' state. This makes
        // sure that the progress bar still shows after the network call completes, but before
        // HomeActivity has become visible
        it.isInProgress || it.isSuccess
    }

    override val signUpWithPhoneRealNameError = MutableLiveData<InputError>()
    override val signUpWithPhonePhoneNumberError = MutableLiveData<InputError>()
    override val signUpWithPhoneEmailError = MutableLiveData<InputError>()

    override fun checkInputForPhoneNumberSignUp(): Boolean {
        val name = signInUserData.fullName.value
        val nameError = if (name.isNullOrBlank()) {
            InputError(BaseR.string.required)
        } else {
            InputError.None
        }

        signUpWithPhoneRealNameError.value = nameError

        val phoneNumberError = signInUserData.validateAndGetPhoneNumberInputError()
        signUpWithPhonePhoneNumberError.value = phoneNumberError

        return nameError == InputError.None && phoneNumberError == InputError.None
    }

    override fun signUpWithPhoneNumber() {
        signUpWithPhoneNumberSuspend.run()
    }

    override val signUpVerificationCodeError = MutableLiveData<InputError>()

    init {
        // Reset input error when user edits text
        signInUserData.fullName.distinctUntilChanged().mapAndObserve {
            signUpWithPhonePhoneNumberError.value = InputError.None
        }

        signInUserData.nationalNumber.distinctUntilChanged().mapAndObserve {
            signUpWithPhonePhoneNumberError.value = InputError.None
        }

        signInUserData.phoneNumberVerificationCode.distinctUntilChanged().mapAndObserve {
            signUpVerificationCodeError.value = InputError.None
        }
    }
}
