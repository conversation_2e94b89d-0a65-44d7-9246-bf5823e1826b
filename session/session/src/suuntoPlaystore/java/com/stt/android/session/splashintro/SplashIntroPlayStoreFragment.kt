package com.stt.android.session.splashintro

import androidx.navigation.NavDirections
import androidx.navigation.fragment.findNavController
import com.stt.android.exceptions.remote.STTError

class SplashIntroPlayStoreFragment : SplashIntroFragment() {

    override fun showTermsAndConditions() {
        findNavController().navigate(SplashIntroPlayStoreFragmentDirections.actionShowTermsAndConditions())
    }

    override fun continueWithEmail(email: String?) {
        findNavController().navigate(
            SplashIntroPlayStoreFragmentDirections.actionContinueWithEmailOrPhone()
                .setEmail(email)
        )
    }

    override fun continueToEmailSignup(email: String) {
        findNavController().navigate(
            SplashIntroPlayStoreFragmentDirections.actionContinueToSignupWithEmail()
                .setEmail(email)
        )
    }

    override fun continueToLoginWithError(email: String, password: String, sttError: STTError) {
        findNavController().navigate(
            SplashIntroPlayStoreFragmentDirections.actionContinueToLoginWithError()
                .setEmail(email)
                .setPassword(password)
                .setSttError(sttError)
        )
    }


    override fun getActionContinueWithMissingEmail(): NavDirections =
        SplashIntroPlayStoreFragmentDirections.actionContinueWithMissingEmail()
}
