package com.stt.android.session.login.phonenumber

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.common.coroutines.LiveDataSuspend
import com.stt.android.common.coroutines.ViewModelScopeProvider
import com.stt.android.common.coroutines.liveDataSuspend
import com.stt.android.domain.UserSession
import com.stt.android.domain.session.LoginMethod
import com.stt.android.domain.session.LoginWithPhoneNumberUseCase
import com.stt.android.domain.session.SessionInitType
import com.stt.android.domain.session.phonenumberverification.PhoneNumberVerificationParameter
import com.stt.android.domain.session.phonenumberverification.VerifyPhoneNumberUseCase
import com.stt.android.exceptions.remote.STTError
import com.stt.android.lifecycle.mapAndObserve
import com.stt.android.session.InputError
import com.stt.android.session.SessionInitializer
import com.stt.android.session.SessionInitializerResult
import com.stt.android.session.SignInUserData
import com.stt.android.session.logLoginOrSignupFailedToAnalytics
import kotlinx.coroutines.CoroutineScope
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR

class LoginWithPhoneNumberImpl
@Inject constructor(
    private val signInUserData: SignInUserData,
    private val verifyPhoneNumberUseCase: VerifyPhoneNumberUseCase,
    private val loginWithPhoneNumberUseCase: LoginWithPhoneNumberUseCase,
    private val sessionInitializer: SessionInitializer,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    override val viewModelScope: CoroutineScope
) : LoginWithPhoneNumber, ViewModelScopeProvider {

    private val loginPhoneSuspend: LiveDataSuspend<SessionInitializerResult> = liveDataSuspend {
        try {
            val phone = signInUserData.phoneNumberValue
            val code = signInUserData.phoneNumberVerificationCode.value?.takeUnless { it.isEmpty() }

            if (code == null) {
                loginWithPhoneNumberCodeInputError.value = InputError(BaseR.string.required)
                throw IllegalArgumentException("Missing code")
            }

            if (phone == null) {
                throw IllegalArgumentException("Missing phone number")
            }

            Timber.d("Logging in with $phone")
            val verificationToken = verifyPhoneNumberUseCase(PhoneNumberVerificationParameter(phone, code))
            Timber.d("Got token $verificationToken")
            val session = loginWithPhoneNumberUseCase(phone, verificationToken)

            sessionInitializer.initialiseSession(
                UserSession.fromDomainSession(session),
                LoginMethod.PHONE,
                SessionInitType.LOGIN,
            )
        } catch (e: Exception) {
            Timber.w(e, "Error during login with phone number")
            logLoginOrSignupFailedToAnalytics(
                firebaseAnalyticsTracker,
                amplitudeAnalyticsTracker,
                AnalyticsEvent.LOGIN_ERROR,
                LoginMethod.PHONE,
                e
            )

            if (e is STTError.InvalidPinCode) {
                loginWithPhoneNumberCodeInputError.value = InputError(BaseR.string.error_1412)
            }

            // propagate to UI
            throw e
        }
    }

    override val loginWithPhoneNumberCodeInputError = MutableLiveData<InputError>()

    override val loginPhoneState = loginPhoneSuspend.state
    override val loginWithPhoneNumberInProgress = loginPhoneState.map { it.isInProgress }

    override fun loginWithPhoneNumber() {
        loginPhoneSuspend.run()
    }

    override fun cancelLoginWithPhoneNumber() {
        loginPhoneSuspend.cancel()
    }

    init {
        signInUserData.phoneNumberVerificationCode.distinctUntilChanged().mapAndObserve {
            loginWithPhoneNumberCodeInputError.value = InputError.None
        }
    }
}
