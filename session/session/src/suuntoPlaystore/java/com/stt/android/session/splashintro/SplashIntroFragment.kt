package com.stt.android.session.splashintro

import android.app.Activity
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.transition.ChangeBounds
import android.transition.Fade
import android.transition.TransitionManager
import android.transition.TransitionSet
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.StringRes
import androidx.constraintlayout.widget.ConstraintSet
import androidx.navigation.fragment.findNavController
import androidx.work.WorkManager
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.domain.session.LoginMethod
import com.stt.android.exceptions.remote.STTError
import com.stt.android.newemail.NewEmailActivity
import com.stt.android.session.R
import com.stt.android.session.SessionInitializerResult
import com.stt.android.session.databinding.FragmentLoginIntroBinding
import com.stt.android.session.facebook.FacebookAndGmailSignInFragment
import com.stt.android.session.signin.SignInActivity
import com.stt.android.session.status.LoginWarningType
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.stt.android.R as BaseR

@AndroidEntryPoint
abstract class SplashIntroFragment : FacebookAndGmailSignInFragment() {

    @Inject
    lateinit var workManager: WorkManager

    private val viewDataBinding: FragmentLoginIntroBinding get() = requireBinding()

    private val animateLayoutHandler = Handler(Looper.getMainLooper())

    private val changeEmailLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            val email = result.data?.getStringExtra(NewEmailActivity.EXTRA_EMAIL_KEY)
            if (result.resultCode == Activity.RESULT_OK && !email.isNullOrEmpty()) {
                <EMAIL>?.let {
                    (activity as? SignInActivity)?.handleSessionInitSuccess(it)
                }
            }
        }

    private var result: SessionInitializerResult? = null

    override fun getLayoutResId() = R.layout.fragment_login_intro

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewDataBinding.onContinueWithEmail = View.OnClickListener {
            if (findNavController().currentDestination?.id == R.id.splashIntroFragment) {
                viewModel.resetForNewLoginMethod(viewModel.config.defaultLogin)
                continueWithEmail()
            }
        }

        viewDataBinding.onContinueWithFacebook = View.OnClickListener {
            if (findNavController().currentDestination?.id == R.id.splashIntroFragment) {
                viewModel.useFacebookLogin()
                showTermsAndConditions()
            }
        }

        viewDataBinding.onContinueWithGmail = View.OnClickListener {
            if (findNavController().currentDestination?.id == R.id.splashIntroFragment) {
                viewModel.useGmailLogin()
                showTermsAndConditions()
            }
        }

        if (viewModel.termsAccepted.value == true &&
            viewModel.loginMethodValue == LoginMethod.FACEBOOK
        ) {
            viewModel.termsAccepted.value = false
            doFacebookLogin()
        }

        if (viewModel.termsAccepted.value == true &&
            viewModel.loginMethodValue == LoginMethod.GMAIL
        ) {
            viewModel.termsAccepted.value = false
            doGmailSignIn()
        }

        checkSessionStatusWarning()

        // Fade in buttons and animate logo
        viewDataBinding.showButtons = false
        animateLayoutDelayed()

        // SplashIntoFragment analysis are sent directly when the fragment is open, but in china this can't be done
        // since Amplitude is only initialized when the user approve on the terms of usage
        // work manager guarantees the delivery of the event when the conditions are met (Internet+Amplitude initialized)
        SplashIntroAnalyticsWorker.schedule(workManager)
    }

    abstract fun showTermsAndConditions()

    override fun onDestroyView() {
        // clear callback that could come after view is destroyed
        animateLayoutHandler.removeCallbacksAndMessages(null)
        super.onDestroyView()
    }

    private fun checkSessionStatusWarning() {
        val loginWarningType = viewModel.getLoginWarningType()
        if (loginWarningType != LoginWarningType.NONE) {
            @StringRes val title: Int

            @StringRes val message: Int
            if (loginWarningType == LoginWarningType.SESSION_EXPIRED) {
                title = BaseR.string.login_dialog_warning_session_expired_title
                message = BaseR.string.login_dialog_warning_session_expired_message
            } else {
                title = BaseR.string.login_dialog_warning_password_reset_title
                message = BaseR.string.login_dialog_warning_password_reset_message
            }
            SimpleDialogFragment.newInstance(
                getString(message),
                getString(title)
            ).show(childFragmentManager, "SessionStatusWarningDialog")
            viewModel.setWarningMessageShown()
        }
    }

    abstract fun continueWithEmail(email: String? = null)

    abstract fun continueToEmailSignup(email: String)

    abstract fun continueToLoginWithError(email: String, password: String, sttError: STTError)

    private fun animateLayoutDelayed() {
        val sceneRoot = viewDataBinding.sceneRoot

        val transitionSet = TransitionSet().apply {
            ordering = TransitionSet.ORDERING_TOGETHER
            duration = ANIMATION_DURATION_MS
            addTransition(ChangeBounds())
            addTransition(
                Fade().apply {
                    addTarget(R.id.content)
                }
            )
        }

        animateLayoutHandler.postDelayed({
            if (view != null) {
                TransitionManager.beginDelayedTransition(sceneRoot, transitionSet)
                viewDataBinding.showButtons = true

                ConstraintSet().apply {
                    clone(sceneRoot)
                    setVerticalBias(R.id.logo, LOGO_VERTICAL_BIAS)
                    applyTo(sceneRoot)
                }
            }
        }, DELAY_ANIMATION_MILLIS)
    }

    companion object {
        private const val ANIMATION_DURATION_MS = 400L
        private const val DELAY_ANIMATION_MILLIS = 1000L
        private const val LOGO_VERTICAL_BIAS = 0.18f
    }
}
