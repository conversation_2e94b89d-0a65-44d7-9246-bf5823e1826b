package com.stt.android.session.signup.phonenumber

import androidx.lifecycle.LiveData
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.session.InputError
import com.stt.android.session.SessionInitializerResult

interface SignUpWithPhoneNumber {
    // State
    val signUpWithPhoneNumberState: LiveData<LiveDataSuspendState<SessionInitializerResult>>
    val signUpWithPhoneNumberInProgress: LiveData<Boolean>

    // Input validation
    val signUpWithPhoneRealNameError: LiveData<InputError>
    val signUpWithPhonePhoneNumberError: LiveData<InputError>
    val signUpWithPhoneEmailError: LiveData<InputError>
    val signUpVerificationCodeError: LiveData<InputError>

    // Operations
    fun checkInputForPhoneNumberSignUp(): Boolean
    fun signUpWithPhoneNumber()
}
