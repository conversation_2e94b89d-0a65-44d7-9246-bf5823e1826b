package com.stt.android.session.signin

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.session.LoginMethod
import com.stt.android.extensions.combineLatest
import com.stt.android.extensions.combineLatestAsList
import com.stt.android.lifecycle.mapAndObserve
import com.stt.android.session.SignInUserData
import com.stt.android.session.configuration.SignInConfiguration
import com.stt.android.session.emailOrPhone.ContinueWithEmailOrPhone
import com.stt.android.session.emailOrPhone.ContinueWithEmailOrPhoneImpl
import com.stt.android.session.facebook.FacebookSignIn
import com.stt.android.session.facebook.FacebookSignInImpl
import com.stt.android.session.gmail.GmailSignIn
import com.stt.android.session.gmail.GmailSignInImpl
import com.stt.android.session.logTermsAcceptedAnalytics
import com.stt.android.session.logTermsScreenAnalytics
import com.stt.android.session.login.apple.SignInWithApple
import com.stt.android.session.login.apple.SignInWithAppleImpl
import com.stt.android.session.login.email.LoginWithEmail
import com.stt.android.session.login.email.LoginWithEmailImpl
import com.stt.android.session.login.phonenumber.LoginWithPhoneNumber
import com.stt.android.session.login.phonenumber.LoginWithPhoneNumberImpl
import com.stt.android.session.signup.SignUp
import com.stt.android.session.signup.SignUpImpl
import com.stt.android.session.signup.phonenumber.PhoneNumberSignUpAskForEmail
import com.stt.android.session.signup.phonenumber.PhoneNumberSignUpAskForEmailImpl
import com.stt.android.session.signup.phonenumber.SignUpWithPhoneNumber
import com.stt.android.session.signup.phonenumber.SignUpWithPhoneNumberImpl
import com.stt.android.session.status.GetSessionStatus
import com.stt.android.session.status.GetSessionStatusImpl
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.STTConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

/**
 * Shared view model for all sign-in/onboarding fragments. Only common functionality for multiple
 * fragments should be added here directly. Fragment specific functionality is separated behind
 * separate interfaces, but this view model implements all the APIs via interface delegation.
 *
 * User entered input is in handled by [SignInUserData] class, which can be accessed here and by
 * each delegate. It can also be used with two-way data binding from the layouts.
 */
@HiltViewModel
class SignInOnboardingViewModel
@Inject constructor(
    coroutinesDispatchers: CoroutinesDispatchers,
    val config: SignInConfiguration,
    private val signInUserData: SignInUserData,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val viewModelScope: CoroutineScope,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    @FeatureTogglePreferences
    private val featureTogglePreferences: SharedPreferences,
    continueWithEmailOrPhone: ContinueWithEmailOrPhoneImpl,
    loginWithEmail: LoginWithEmailImpl,
    loginWithPhoneNumber: LoginWithPhoneNumberImpl,
    signUp: SignUpImpl,
    signInWithApple: SignInWithAppleImpl,
    facebookSignIn: FacebookSignInImpl,
    phoneNumberSignUpAskForEmail: PhoneNumberSignUpAskForEmailImpl,
    signUpWithPhoneNumber: SignUpWithPhoneNumberImpl,
    getSessionStatus: GetSessionStatusImpl,
    gmailSignInImpl: GmailSignInImpl,
) : CoroutineViewModel(coroutinesDispatchers),
    SignInUserData by signInUserData,
    ContinueWithEmailOrPhone by continueWithEmailOrPhone,
    LoginWithEmail by loginWithEmail,
    LoginWithPhoneNumber by loginWithPhoneNumber,
    SignUp by signUp,
    SignInWithApple by signInWithApple,
    FacebookSignIn by facebookSignIn,
    PhoneNumberSignUpAskForEmail by phoneNumberSignUpAskForEmail,
    SignUpWithPhoneNumber by signUpWithPhoneNumber,
    GetSessionStatus by getSessionStatus,
    GmailSignIn by gmailSignInImpl {

    override val coroutineContext: CoroutineContext
        get() = viewModelScope.coroutineContext

    override fun onCleared() {
        super.onCleared()
        viewModelScope.cancel()
    }

    val termsAccepted: MutableLiveData<Boolean> = MutableLiveData(false)

    val loginMethodValue: LoginMethod
        get() = loginMethod.value ?: config.defaultLogin
    private val _onPhoneRegionClicked = SingleLiveEvent<Int>()

    val onPhoneRegionClicked: LiveData<Int>
        get() = _onPhoneRegionClicked

    val showProgressInSplashPage: LiveData<Boolean> = combineLatestAsList(
        facebookSignInInProgress,
        signUpInProgress,
        signInWithAppleInProgress,
        gmailSignInInProgress,
    ).map { list ->
        list.any { it }
    }

    val canSignupWithVerificationCode: LiveData<Boolean> = combineLatest(
        signUpWithPhoneNumberInProgress,
        loginWithPhoneNumberInProgress,
        phoneNumberVerificationCode
    ).mapAndObserve { (signUpInProgress, loginInProgress, phoneNumberCode) ->
        !signUpInProgress && !loginInProgress && phoneNumberCode.length == 6
    }

    fun usePhoneNumberLogin(reset: Boolean = false) {
        viewModelScope
        if (reset) {
            signInUserData.resetForNewLoginMethod(LoginMethod.PHONE)
        } else {
            signInUserData.loginMethod.value = LoginMethod.PHONE
        }
    }

    fun useEmailLogin(reset: Boolean = false) {
        if (reset) {
            signInUserData.resetForNewLoginMethod(LoginMethod.EMAIL)
        } else {
            signInUserData.loginMethod.value = LoginMethod.EMAIL
        }
    }

    fun useFacebookLogin() {
        signInUserData.loginMethod.value = LoginMethod.FACEBOOK
    }

    fun useGmailLogin() {
        signInUserData.loginMethod.value = LoginMethod.GMAIL
    }

    fun useSignInWithApple() {
        signInUserData.loginMethod.value = LoginMethod.APPLE
    }

    fun onPhoneRegionClicked() {
        if (!phoneRegionSelectionEnabled) {
            Timber.w("Cannot proceed with phone region selection")
            return
        }
        try {
            _onPhoneRegionClicked.value =
                signInUserData.phoneRegion.value?.split("+")?.last()?.toInt()
        } catch (e: NumberFormatException) {
            _onPhoneRegionClicked.value = -1
        }
    }

    fun showSupportPage(context: Context) {
        com.stt.android.session.showSupportPage(
            context,
            emarsysAnalytics,
            amplitudeAnalyticsTracker
        )
    }

    fun logTermsScreen() {
        logTermsScreenAnalytics(emarsysAnalytics, amplitudeAnalyticsTracker, loginMethodValue)
    }

    fun logTermsAccepted(termscode: String) {
        termsAccepted.value = true
        signInUserData.termscode.value = termscode
        logTermsAcceptedAnalytics(emarsysAnalytics, amplitudeAnalyticsTracker, loginMethodValue)
    }

    fun termscode(): String = signInUserData.termscode.value.orEmpty()

    fun showForgotPasswordPage(context: Context) {
        com.stt.android.session.showForgotPasswordPage(
            firebaseAnalyticsTracker,
            amplitudeAnalyticsTracker,
            context
        )
    }

    fun setVerificationToken(token: String) {
        signInUserData.verificationToken.value = token
    }

    fun signInWithGmail(authCode: String, email: String) {
        viewModelScope.launch {
            signWithGmail(authCode, email, termscode())
        }
    }

    fun showGmailLogin(): Boolean  = supportGmailLogin() && showProgressInSplashPage.value == false

    private fun supportGmailLogin(): Boolean {
        return config.supportsGmailLogin && featureTogglePreferences.getBoolean(
            STTConstants.FeatureTogglePreferences.KEY_GMAIL_LOGIN,
            STTConstants.FeatureTogglePreferences.KEY_GMAIL_LOGIN_DEFAULT
        )
    }
}
