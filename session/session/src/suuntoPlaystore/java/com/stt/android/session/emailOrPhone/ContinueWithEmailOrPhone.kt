package com.stt.android.session.emailOrPhone

import androidx.lifecycle.LiveData
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.session.InputError

interface ContinueWithEmailOrPhone {
    enum class ContinueAction {
        PROCEED_TO_EMAIL_LOGIN,
        PROCEED_TO_EMAIL_SIGN_UP,
        ASK_EMAIL_FOR_PHONE_NUMBER_SIGN_UP,
        ASK_NAME_FOR_PHONE_NUMBER_SIGN_UP,
        ASK_CODE_FOR_PHONE_NUMBER_LOGIN,
        INVALID_INPUT
    }

    // UI data
    val continueWithEmailHint: Int
    val emailInputError: LiveData<InputError>
    val phoneNumberInputError: LiveData<InputError>

    // State
    val preApprovedTermsAndConditions: LiveData<Boolean>
    fun preApprovedTermsAndConditionsChanged(checked: Boolean)

    val canContinue: LiveData<Boolean>
    val emailOrUsernameStatusState: LiveData<LiveDataSuspendState<ContinueAction>>
    val phoneNumberStatusState: LiveData<LiveDataSuspendState<ContinueAction>>
    val fetchEmailOrPhoneNumberStatusInProgress: LiveData<Boolean>

    // Actions
    fun continueWithEmailOrPhoneNumber()
    fun cancelFetchingAccountStatus()
    fun logOnboardingStartScreen()
}
