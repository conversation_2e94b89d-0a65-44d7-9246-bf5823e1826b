package com.stt.android.session.login.email

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.session.InputError
import com.stt.android.session.SessionInitializerResult
import com.stt.android.R as BaseR

interface LoginWithEmail {
    data class ExistingAccountInfo constructor(
        @StringRes val titleString: Int,
        @StringRes val descriptionString: Int?,
        @DrawableRes val logo: Int?
    ) {
        val visible: Boolean
            get() = descriptionString != null

        companion object {
            val DEFAULT = ExistingAccountInfo(BaseR.string.login, null, null)
        }
    }

    val userNameOrEmailInputError: LiveData<InputError>
    val loginPasswordInputError: MutableLiveData<InputError>

    val existingAccountInfo: LiveData<ExistingAccountInfo>
    val canLogin: LiveData<Boolean>
    val loginInProgress: LiveData<Boolean>
    val loginState: LiveData<LiveDataSuspendState<SessionInitializerResult>>
    val emailChecked: LiveData<Boolean>
    val needReLogin: LiveData<Boolean>
    fun checkInputForSignIn(): Boolean
    fun loginWithEmail()
    fun cancelLogin()
    fun logLoginWithEmailScreen()
    fun resetPasswordInputError()
    fun storeEmailChecked(checked: Boolean)
    fun setEmailChecked(checked: Boolean)
    fun setReLogin(reLogin: Boolean)
    fun updateEmailInput(email: String)
    suspend fun getUserEmail(): String
}
