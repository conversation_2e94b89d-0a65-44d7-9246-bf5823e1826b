package com.stt.android.session.signup.phonenumber

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.common.ui.observeNotNull
import com.stt.android.extensions.hideKeyboard
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.session.R
import com.stt.android.session.signin.SignInOnboardingViewModel
import com.stt.android.session.databinding.FragmentPhoneNumberAskForNameBinding
import com.stt.android.session.setupLoginFlowTransitions
import com.stt.android.session.showSnackBar
import com.stt.android.utils.OnActionDone
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

/**
 * Fragment for phone number sign-up flow. Shown when the phone number does not exist (MISSING).
 */
@AndroidEntryPoint
class AskForFullNameFragment : ViewModelFragment2() {

    @Inject
    lateinit var homeActivityNavigator: HomeActivityNavigator

    private val args: AskForFullNameFragmentArgs by navArgs()

    override val viewModel: SignInOnboardingViewModel by activityViewModels()

    private val viewDataBinding: FragmentPhoneNumberAskForNameBinding get() = requireBinding()

    override fun getLayoutResId() = R.layout.fragment_phone_number_ask_for_name

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        (activity as? AppCompatActivity)?.setSupportActionBar(
            viewDataBinding.phoneAskForNameToolbar.signupToolbar
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupLoginFlowTransitions()
        setupUi()

        if (viewModel.termsAccepted.value == true && viewModel.verificationSmsSent.value == false) {
            viewModel.termsAccepted.value = false
            viewModel.sendPhoneSignUpVerificationSms()
        }
    }

    private fun setupUi() {
        val vm = viewModel

        with(viewDataBinding) {
            showEmailInput = args.requireEmail

            onSignUpClicked = View.OnClickListener {
                handleSignUpClick()
            }

            onActionDone = OnActionDone { _, _ ->
                if (vm.phoneSignUpVerificationSmsSendingInProgress.value != true) {
                    handleSignUpClick()
                }
                true
            }
        }

        vm.sendingVerificationSmsState.observeNotNull(
            viewLifecycleOwner,
            ::handleVerificationSmsState
        )
    }

    private fun handleVerificationSmsState(state: LiveDataSuspendState<Unit>) {
        state.ifNotHandled {
            if (state.isSuccess) {
                // Intentionally omit navigationExtras here as this transition happens immediately
                // after returning from TermsAndConditionsFragment and thus shared element
                // transition is not needed
                findNavController().navigate(
                    AskForFullNameFragmentDirections.actionAskForVerificationCode(false)
                )
            } else if (state is LiveDataSuspendState.Failure) {
                Timber.w(state.throwable, "Failed to request verification SMS")
                view?.showSnackBar(state.throwable)
            }
        }
    }

    private fun handleSignUpClick() {
        if (viewModel.checkInputForPhoneNumberSignUp()) {
            showTerms()
        }
    }

    private fun showTerms() {
        // Reset SMS sent flag so that re-sending is possible after agreeing to terms again
        viewModel.cancelSendingVerificationSms()

        activity?.hideKeyboard()
        findNavController().navigate(
            AskForFullNameFragmentDirections.actionShowTermsAndConditions()
        )
    }
}
