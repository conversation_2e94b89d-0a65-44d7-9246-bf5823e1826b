package com.stt.android.session.emailOrPhone

import android.os.Bundle
import android.transition.AutoTransition
import android.transition.TransitionManager
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.activityViewModels
import androidx.navigation.NavDirections
import androidx.navigation.Navigator
import androidx.navigation.fragment.FragmentNavigatorExtras
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.google.android.material.button.MaterialButtonToggleGroup
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.domain.session.LoginMethod
import com.stt.android.extensions.hideKeyboard
import com.stt.android.featuretoggle.OpenFeatureToggleHandler
import com.stt.android.session.R
import com.stt.android.session.databinding.FragmentContinueWithEmailOrPhoneBinding
import com.stt.android.session.phonenumberverification.PhoneRegionDialogFragment
import com.stt.android.session.showSnackBar
import com.stt.android.session.signin.SignInOnboardingViewModel
import com.stt.android.ui.extensions.closeKeyboard
import com.stt.android.utils.OnActionDone
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class ContinueWithEmailOrPhoneFragment : ViewModelFragment2() {

    private val args: ContinueWithEmailOrPhoneFragmentArgs by navArgs()

    override val viewModel: SignInOnboardingViewModel by activityViewModels()

    private val viewDataBinding: FragmentContinueWithEmailOrPhoneBinding get() = requireBinding()

    override fun getLayoutResId() = R.layout.fragment_continue_with_email_or_phone

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupViewModel()
        setupView()
    }

    private fun setupViewModel() {
        viewModel.run {
            onPhoneRegionClicked.observeK(viewLifecycleOwner) { code ->
                if (isAdded) {
                    val dialogFragment = PhoneRegionDialogFragment().apply {
                        val action =
                            ContinueWithEmailOrPhoneFragmentDirections.actionContinueWithEmailOrPhoneToPhoneRegionDialogFragment(
                                code ?: -1
                            )
                        arguments = action.arguments
                    }
                    dialogFragment.onItemClickListener =
                        { region -> viewModel.phoneRegion.value = "+$region" }
                    dialogFragment.show(parentFragmentManager, "PhoneNumberConfirmationFragment")
                }
            }

            emailOrUsernameStatusState.observeNotNull(viewLifecycleOwner, ::handleContinueActionState)
            phoneNumberStatusState.observeNotNull(viewLifecycleOwner, ::handleContinueActionState)

            logOnboardingStartScreen()
        }
    }

    private fun setupView() {
        val vm = viewModel
        args.email?.let { vm.rawEmailOrUsername.value = it }
        vm.password.value = ""
        with(viewDataBinding) {
            OpenFeatureToggleHandler(continueWithEmailOrPhoneWelcomeText)

            continueWithEmailOrPhoneToggle.emailOrPhoneToggleGroup.check(
                if (vm.loginMethodValue == LoginMethod.PHONE) {
                    R.id.use_phone_number_toggle_button
                } else {
                    R.id.use_email_toggle_button
                }
            )
            continueWithEmailOrPhoneEmailInput.emailEditText.imeOptions = EditorInfo.IME_ACTION_DONE
            if (vm.loginMethodValue == LoginMethod.PHONE) {
                viewDataBinding.viewPhoneNumber.edittextPhone.requestFocus()
            } else {
                viewDataBinding.continueWithEmailOrPhoneEmailInput.emailInputLayout.requestFocus()
            }

            onToggleButtonChecked =
                MaterialButtonToggleGroup.OnButtonCheckedListener { _, checkedId, isChecked ->
                    if (isChecked) {
                        if (checkedId == R.id.use_email_toggle_button) {
                            beginDelayedTransition()
                            vm.useEmailLogin()
                            viewDataBinding.continueWithEmailOrPhoneEmailInput.emailInputLayout.requestFocus()
                        } else if (checkedId == R.id.use_phone_number_toggle_button) {
                            beginDelayedTransition()
                            vm.usePhoneNumberLogin()
                            viewDataBinding.viewPhoneNumber.edittextPhone.requestFocus()
                        }
                    }
                }

            onSignInWithAppleClicked = View.OnClickListener {
                it.closeKeyboard()
                findNavController().navigate(
                    ContinueWithEmailOrPhoneFragmentDirections.actionShowTermsForSignInWithApple()
                        .setLaunchSignInWithApple(true),
                    navigationExtras
                )
            }

            onActionDone = OnActionDone { view, _ ->
                if (vm.canContinue.value == true) {
                    vm.continueWithEmailOrPhoneNumber()
                }
                false
            }
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        (activity as? AppCompatActivity)?.setSupportActionBar(
            viewDataBinding.continueWithEmailOrPhoneAppbar.signupToolbar
        )
    }

    override fun onDestroy() {
        try {
            viewModel.cancelFetchingAccountStatus()
        } catch (e: Exception) {
            // there is a weird Hilt crash connected to the shared viewmodel used by this fragment
            // try catching just in case for now to see if the problem still reproduces after the fix
            // https://console.firebase.google.com/u/0/project/suunto-app/crashlytics/app/android:com.stt.android.suunto/issues/05f16998f5c4a66eaaef9e1d5cc8f5da?time=last-seven-days&versions=4.53.1%20(4053001)&types=crash&sessionEventKey=624F9C93039500015176E13ABB33C2D7_1662557495034470826
            Timber.w(e, "Error cancelling fetching account status in fragment onDestroy")
        }
        super.onDestroy()
    }

    private fun beginDelayedTransition() {
        val autoTransition = AutoTransition().apply {
            duration = 75L
        }

        TransitionManager.beginDelayedTransition(viewDataBinding.root as ViewGroup, autoTransition)
    }

    private fun handleContinueActionState(
        state: LiveDataSuspendState<ContinueWithEmailOrPhone.ContinueAction>
    ) {
        state.ifNotHandled {
            var action: NavDirections? = null
            when (state) {
                is LiveDataSuspendState.Success -> {
                    action = when (state.successValue) {
                        ContinueWithEmailOrPhone.ContinueAction.PROCEED_TO_EMAIL_LOGIN ->
                            ContinueWithEmailOrPhoneFragmentDirections.actionLoginWithEmail()
                        ContinueWithEmailOrPhone.ContinueAction.PROCEED_TO_EMAIL_SIGN_UP ->
                            ContinueWithEmailOrPhoneFragmentDirections.actionSignupWithEmail()
                        ContinueWithEmailOrPhone.ContinueAction.ASK_EMAIL_FOR_PHONE_NUMBER_SIGN_UP ->
                            ContinueWithEmailOrPhoneFragmentDirections.actionAskForEmail()
                        ContinueWithEmailOrPhone.ContinueAction.ASK_NAME_FOR_PHONE_NUMBER_SIGN_UP ->
                            ContinueWithEmailOrPhoneFragmentDirections.actionAskForFullName(false)
                        ContinueWithEmailOrPhone.ContinueAction.ASK_CODE_FOR_PHONE_NUMBER_LOGIN ->
                            ContinueWithEmailOrPhoneFragmentDirections.actionAskForVerificationCode(true)
                        ContinueWithEmailOrPhone.ContinueAction.INVALID_INPUT ->
                            null
                    }
                }
                is LiveDataSuspendState.Failure -> {
                    view?.showSnackBar(state.throwable)
                }
                is LiveDataSuspendState.InProgress -> {
                    activity?.hideKeyboard()
                }

                is LiveDataSuspendState.Idle -> {
                    // do nothing
                }
            }

            action?.let {
                findNavController().navigate(it, navigationExtras)
            }
        }
    }

    private val navigationExtras: Navigator.Extras
        get() = FragmentNavigatorExtras(
            viewDataBinding.continueWithEmailOrPhoneAppbar.root to
                getString(R.string.transition_name_app_bar),
            viewDataBinding.viewPhoneNumber.root to
                getString(R.string.transition_name_phone_number_input),
            viewDataBinding.continueWithEmailOrPhoneToggle.root to
                getString(R.string.transition_name_phone_email_toggle),
            viewDataBinding.continueWithEmailContinueButton to
                getString(R.string.transition_name_main_button),
            viewDataBinding.continueWithEmailOrPhoneTermsAndConditions.signupTosText to
                getString(R.string.transition_name_terms_and_conditions),
            viewDataBinding.continueWithEmailOrPhoneEmailInput.emailInputLayout to
                getString(R.string.transition_name_email_input)
        )
}
