package com.stt.android.session.signin

import android.content.res.Resources
import com.stt.android.domain.session.LoginMethod
import com.stt.android.domain.session.MobileApp
import com.stt.android.session.configuration.SignInConfiguration
import dagger.Module
import dagger.Provides
import com.stt.android.R as BaseR

@Module
object ProductSignInModule {

    @Provides
    fun provideSignInConfiguration(
        resources: Resources
    ) = SignInConfiguration(
        supportsFacebookLogin = true,
        supportsAppleLogin = true,
        supportsEmailLogin = true,
        supportsUsernameLogin = true,
        supportsPhoneNumberLogin = false,
        requirePhoneNumberInSignup = false,
        defaultLogin = LoginMethod.EMAIL,
        termsUrl = resources.getString(BaseR.string.terms_url),
        defaultPhoneNumberCountryCode = null,
        forcePreApproveOfTermsAndConditions = false,
        requireFirstLaunchTermsAcceptance = false,
        supportsGmailLogin = true
    )

    @Provides
    fun provideMobileApp() = MobileApp.SUUNTO_APP
}
