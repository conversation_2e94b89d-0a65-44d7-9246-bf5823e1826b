package com.stt.android.session.signup.phonenumber

import androidx.lifecycle.LiveData
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.session.InputError

interface PhoneNumberSignUpAskForEmail {
    enum class ContinueAction {
        ASK_FOR_PASSWORD,
        ASK_FOR_NAME,
        INVALID_INPUT
    }

    // State
    val sendingVerificationSmsState: LiveData<LiveDataSuspendState<Unit>>
    val resendingVerificationSmsState: LiveData<LiveDataSuspendState<Unit>>
    val checkEmailForPhoneNumberLoginState: LiveData<LiveDataSuspendState<ContinueAction>>
    val phoneNumberSignUpEmailInputError: LiveData<InputError>
    val emailStatusForPhoneNumberSignUpInProgress: LiveData<Boolean>
    val phoneSignUpVerificationSmsSendingInProgress: LiveData<Boolean>
    val verificationSmsSent: LiveData<Boolean>
    val enableResendingVerificationCode: LiveData<Boolean>

    // Operations
    fun checkEmailStatusForPhoneNumberSignUp()
    fun sendPhoneSignUpVerificationSms()
    fun cancelSendingVerificationSms()
    fun resendVerificationCode()
    fun logAskForEmailForUnverifiedPhoneNumber()
}
