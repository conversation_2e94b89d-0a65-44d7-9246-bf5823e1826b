package com.stt.android.session.login.phonenumber

import androidx.lifecycle.LiveData
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.session.InputError
import com.stt.android.session.SessionInitializerResult

interface LoginWithPhoneNumber {
    val loginWithPhoneNumberInProgress: LiveData<Boolean>
    val loginWithPhoneNumberCodeInputError: LiveData<InputError>
    val loginPhoneState: LiveData<LiveDataSuspendState<SessionInitializerResult>>

    fun loginWithPhoneNumber()
    fun cancelLoginWithPhoneNumber()
}
