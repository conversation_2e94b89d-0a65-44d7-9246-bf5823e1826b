package com.stt.android.session.signup.phonenumber

import android.os.Bundle
import android.view.View
import android.view.inputmethod.EditorInfo
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.activityViewModels
import androidx.navigation.Navigator
import androidx.navigation.fragment.FragmentNavigatorExtras
import androidx.navigation.fragment.findNavController
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.common.ui.observeNotNull
import com.stt.android.session.R
import com.stt.android.session.signin.SignInOnboardingViewModel
import com.stt.android.session.databinding.FragmentPhoneNumberAskForEmailBinding
import com.stt.android.session.setupLoginFlowTransitions
import com.stt.android.session.showSnackBar
import com.stt.android.utils.OnActionDone
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 * Fragment for phone number sign-up flow. Shown when the phone number exists, but it is not
 * verified (e.g. UNVERIFIED).
 */
@AndroidEntryPoint
class AskForEmailFragment : ViewModelFragment2() {

    override val viewModel: SignInOnboardingViewModel by activityViewModels()

    private val viewDataBinding: FragmentPhoneNumberAskForEmailBinding get() = requireBinding()

    override fun getLayoutResId() = R.layout.fragment_phone_number_ask_for_email

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        (activity as? AppCompatActivity)?.setSupportActionBar(
            viewDataBinding.phoneAskForEmailToolbar.signupToolbar
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupLoginFlowTransitions()
        setupUi()
    }

    private fun setupUi() {
        viewModel.checkEmailForPhoneNumberLoginState.observeNotNull(viewLifecycleOwner, ::handleEmailCheckState)
        viewModel.logAskForEmailForUnverifiedPhoneNumber()

        viewDataBinding.phoneAskForEmailEmailInput.emailEditText.imeOptions = EditorInfo.IME_ACTION_DONE
        viewDataBinding.onActionDone = OnActionDone { _, _ ->
            if (viewModel.emailStatusForPhoneNumberSignUpInProgress.value != true) {
                viewModel.checkEmailStatusForPhoneNumberSignUp()
            }
            false
        }
    }

    private fun handleEmailCheckState(state: LiveDataSuspendState<PhoneNumberSignUpAskForEmail.ContinueAction>) {
        state.ifNotHandled {
            if (state is LiveDataSuspendState.Success) {
                when (state.successValue) {
                    PhoneNumberSignUpAskForEmail.ContinueAction.ASK_FOR_PASSWORD -> {
                        findNavController().navigate(
                            AskForEmailFragmentDirections.actionAskForPassword(),
                            navigationExtras
                        )
                    }
                    PhoneNumberSignUpAskForEmail.ContinueAction.ASK_FOR_NAME -> {
                        findNavController().navigate(
                            AskForEmailFragmentDirections.actionAskForFullNameWithEmail(true),
                            navigationExtras
                        )
                    }
                    PhoneNumberSignUpAskForEmail.ContinueAction.INVALID_INPUT -> {
                        // pass
                    }
                }
            } else if (state is LiveDataSuspendState.Failure) {
                Timber.w(state.throwable, "Failed to check email status")
                view?.showSnackBar(state.throwable)
            }
        }
    }

    private val navigationExtras: Navigator.Extras
        get() = FragmentNavigatorExtras(
            viewDataBinding.phoneAskForEmailToolbar.signupAppbar to
                getString(R.string.transition_name_app_bar),
            viewDataBinding.phoneAskForEmailPhoneNumberInput.root to
                getString(R.string.transition_name_phone_number_input),
            viewDataBinding.phoneAskForEmailEmailInput.root to
                getString(R.string.transition_name_email_input),
            viewDataBinding.phoneAskForEmailContinueButton to
                getString(R.string.transition_name_main_button),
            viewDataBinding.phoneAskForEmailTermsAndConditions.signupTosText to
                getString(R.string.transition_name_terms_and_conditions),
            viewDataBinding.phoneAskForEmailContactSupportButton.contactSupportButton to
                getString(R.string.transition_name_contact_support)
        )
}
