package com.stt.android.session.login.email

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.coroutines.LiveDataSuspend
import com.stt.android.common.coroutines.ViewModelScopeProvider
import com.stt.android.common.coroutines.liveDataSuspend
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.UserSession
import com.stt.android.domain.session.EmailVerificationState
import com.stt.android.domain.session.LoginMethod
import com.stt.android.domain.session.LoginWithEmailUseCase
import com.stt.android.domain.session.MobileApp
import com.stt.android.domain.session.SessionInitType
import com.stt.android.exceptions.remote.STTError
import com.stt.android.lifecycle.mapAndObserve
import com.stt.android.session.InputError
import com.stt.android.session.R
import com.stt.android.session.SessionInitializer
import com.stt.android.session.SessionInitializerResult
import com.stt.android.session.SignInUserData
import com.stt.android.session.configuration.SignInConfiguration
import com.stt.android.session.logLoginOrSignupFailedToAnalytics
import com.stt.android.session.logLoginWithEmailScreenAnalytics
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR

class LoginWithEmailImpl
@Inject constructor(
    private val signInUserData: SignInUserData,
    private val loginWithEmailUseCase: LoginWithEmailUseCase,
    private val sessionInitializer: SessionInitializer,
    private val currentBrand: MobileApp,
    private val config: SignInConfiguration,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    override val viewModelScope: CoroutineScope,
    dispatchers: CoroutinesDispatchers
) : LoginWithEmail, ViewModelScopeProvider, CoroutinesDispatchers by dispatchers {

    private val loginSuspend: LiveDataSuspend<SessionInitializerResult> = liveDataSuspend {
        runSuspendCatching {
            val emailOrUsername = signInUserData.emailOrUsername.value
            val password = signInUserData.password.value
            val emailExistsInMovescount = signInUserData.emailExistsInMovescount.value

            if (emailOrUsername.isNullOrBlank() || password.isNullOrBlank()) {
                throw IllegalArgumentException("Missing email or password")
            }

            Timber.d("Logging in with $emailOrUsername")
            val session = loginWithEmailUseCase(emailOrUsername, password)
            emailChecked.value = session.emailVerificationState == EmailVerificationState.VERIFIED
            sessionInitializer.initialiseSession(
                UserSession.fromDomainSession(session),
                LoginMethod.EMAIL,
                SessionInitType.LOGIN,
                emailExistsInMovescount,
            )
        }.getOrElse { e ->
            Timber.w(e, "Error during login with email")
            logLoginOrSignupFailedToAnalytics(
                firebaseAnalyticsTracker,
                amplitudeAnalyticsTracker,
                AnalyticsEvent.LOGIN_ERROR,
                LoginMethod.EMAIL,
                e
            )

            if (e is STTError.InvalidUserPassword) {
                // Wrong password
                loginPasswordInputError.value = InputError(R.string.error_1401)
            }

            // propagate to UI
            throw e
        }
    }


    override fun loginWithEmail() = loginSuspend.run()
    override val loginState = loginSuspend.state
    override val emailChecked = MutableLiveData<Boolean>(false)
    override val needReLogin = MutableLiveData(false)
    override val loginInProgress = loginState.map { it.isInProgress || (it.isSuccess && emailChecked.value == true) }
    override fun cancelLogin() = loginSuspend.cancel()

    override val canLogin by lazy { loginInProgress.mapAndObserve { !it } }
    override val userNameOrEmailInputError = MutableLiveData<InputError>()
    override val loginPasswordInputError = MutableLiveData<InputError>()

    override fun checkInputForSignIn(): Boolean {
        val userNameOrEmail = signInUserData.emailOrUsername.value
        val userNameOrEmailError = when {
            userNameOrEmail.isNullOrBlank() -> InputError(BaseR.string.required)
            config.supportsUsernameLogin -> InputError.None
            signInUserData.emailValid.value == true -> InputError.None
            else -> InputError(BaseR.string.invalid_email)
        }

        val passwordError = if (signInUserData.password.value.isNullOrBlank()) {
            InputError(R.string.sign_up_password_is_required_error)
        } else {
            InputError.None
        }

        userNameOrEmailInputError.value = userNameOrEmailError
        loginPasswordInputError.value = passwordError

        return userNameOrEmailError == InputError.None && passwordError == InputError.None
    }

    override val existingAccountInfo: LiveData<LoginWithEmail.ExistingAccountInfo> =
        signInUserData.activeBrandForEmail.mapAndObserve(
            defaultValue = LoginWithEmail.ExistingAccountInfo.DEFAULT
        ) { brand ->
            when (MobileApp.entries.firstOrNull { it.value == brand }) {
                currentBrand -> LoginWithEmail.ExistingAccountInfo.DEFAULT
                MobileApp.SPORTS_TRACKER ->
                    LoginWithEmail.ExistingAccountInfo(
                        R.string.log_in_with_your_st_account,
                        R.string.log_in_st_account_description,
                        R.drawable.sports_tracker_app_icon
                    )
                MobileApp.SUUNTO_APP ->
                    LoginWithEmail.ExistingAccountInfo(
                        R.string.log_in_with_your_suunto_account,
                        R.string.log_in_suunto_account_description,
                        R.drawable.suunto_app_icon
                    )
                MobileApp.ATOMIC_APP ->
                    LoginWithEmail.ExistingAccountInfo(
                        R.string.log_in_with_your_atomic_account,
                        R.string.log_in_atomic_account_description,
                        R.drawable.atomic_app_icon
                    )
                else -> LoginWithEmail.ExistingAccountInfo.DEFAULT
            }
        }

    override fun logLoginWithEmailScreen() {
        logLoginWithEmailScreenAnalytics(emarsysAnalytics, amplitudeAnalyticsTracker)
    }

    override fun resetPasswordInputError() {
        loginPasswordInputError.value = InputError.None
    }

    override fun storeEmailChecked(checked: Boolean) {
        val userSession = currentUserController.currentUser.session?.copy(
            emailVerificationState = if (checked) EmailVerificationState.VERIFIED else EmailVerificationState.NOT_VERIFIED
        )
        val user = currentUserController.currentUser.copy(
            session = userSession
        )
        currentUserController.store(user)
        setEmailChecked(true)
    }

    override fun setEmailChecked(checked: Boolean) {
        emailChecked.value = checked
    }

    override fun setReLogin(reLogin: Boolean) {
        needReLogin.value = reLogin
    }

    override fun updateEmailInput(email: String) {
        signInUserData.rawEmailOrUsername.value = email
    }

    override suspend fun getUserEmail(): String = withContext(io) {
        userSettingsController.settings.email.orEmpty()
    }

    init {
        // Reset input error when user edits text
        signInUserData.emailOrUsername.distinctUntilChanged().mapAndObserve {
            userNameOrEmailInputError.value = InputError.None
        }

        signInUserData.nationalNumber.distinctUntilChanged().mapAndObserve {
            loginPasswordInputError.value = InputError.None
        }
    }
}
