package com.stt.android.session.gmail

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.UserSession
import com.stt.android.domain.session.FetchEmailStatusUseCase
import com.stt.android.domain.session.GmailSignInUseCase
import com.stt.android.domain.session.LoginMethod
import com.stt.android.domain.session.SessionInitType
import com.stt.android.session.SessionInitializer
import javax.inject.Inject

class GmailSignInImpl @Inject constructor(
    private val gmailSignInUseCase: GmailSignInUseCase,
    private val fetchEmailStatusUseCase: FetchEmailStatusUseCase,
    private val sessionInitializer: SessionInitializer,
) : GmailSignIn {
    private val _gmailSignInInProgress = MutableLiveData(false)
    override val gmailSignInInProgress: LiveData<Boolean> = _gmailSignInInProgress

    private val _gmailSignInState = MutableLiveData<GmaiLoginResult>()
    override val gmailSignInState: LiveData<GmaiLoginResult> = _gmailSignInState

    override suspend fun signWithGmail(authCode: String, email: String, termsCode: String) {
        runSuspendCatching {
            _gmailSignInInProgress.value = true
            val emailStatus = fetchEmailStatusUseCase(email)
            val userSession = gmailSignInUseCase(authCode, termsCode)
            userSession to if (emailStatus.existsInAsko) SessionInitType.LOGIN else SessionInitType.SIGNUP
        }.onSuccess { result ->
            val sessionInitResult = sessionInitializer.initialiseSession(
                UserSession.fromDomainSession(result.first),
                LoginMethod.GMAIL,
                result.second
            )
            _gmailSignInInProgress.value = false
            _gmailSignInState.value = GmaiLoginResult.Success(sessionInitResult)
        }.onFailure {
            _gmailSignInInProgress.value = false
            _gmailSignInState.value = GmaiLoginResult.Failure(it)
        }
    }
}
