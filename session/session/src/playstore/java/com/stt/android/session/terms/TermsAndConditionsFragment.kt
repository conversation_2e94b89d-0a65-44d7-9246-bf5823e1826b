package com.stt.android.session.terms

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.domain.session.LoginMethod
import com.stt.android.remote.BaseUrlV2
import com.stt.android.session.R
import com.stt.android.session.databinding.FragmentTermsAndConditionsBinding
import com.stt.android.session.signin.SignInActivity
import com.stt.android.session.signin.SignInOnboardingViewModel
import com.stt.android.utils.FlavorUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class TermsAndConditionsFragment : ViewModelFragment2() {
    @Inject
    @BaseUrlV2
    lateinit var baseUrlV2: String

    private val args: TermsAndConditionsFragmentArgs by navArgs()

    override val viewModel: SignInOnboardingViewModel by activityViewModels()

    private val viewDataBinding: FragmentTermsAndConditionsBinding get() = requireBinding()

    override fun getLayoutResId() = R.layout.fragment_terms_and_conditions

    @SuppressLint("SetJavaScriptEnabled")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        (activity as? AppCompatActivity)?.setSupportActionBar(viewDataBinding.termsToolbar)

        viewDataBinding.retry.setOnClickListener { load() }
        viewDataBinding.termsWebview.apply {
            settings.javaScriptEnabled = true
            settings.cacheMode = WebSettings.LOAD_NO_CACHE
            webViewClient = object : WebViewClient() {
                override fun shouldOverrideUrlLoading(
                    view: WebView?,
                    request: WebResourceRequest?
                ): Boolean {
                    if (request?.url?.toString()?.startsWith(CALLBACK_URL) == true) {
                        onTermsAccepted(code = request.url.getQueryParameter("code").orEmpty())
                        return true
                    }

                    return super.shouldOverrideUrlLoading(view, request)
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    onLoadingFinished()
                }

                override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
                    super.onReceivedError(view, request, error)
                    if (request?.isForMainFrame == true) {
                        onLoadingFailed()
                    }
                }
            }


            webChromeClient = object : WebChromeClient() {
                override fun onProgressChanged(view: WebView?, newProgress: Int) {
                    super.onProgressChanged(view, newProgress)
                    viewDataBinding.showLoadingSpinner = newProgress < 100
                }
            }

            setOnKeyListener { _, keyCode, event ->
                if (event.action != KeyEvent.ACTION_DOWN) {
                    return@setOnKeyListener true
                }
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    if (canGoBack()) {
                        goBack()
                    } else {
                        activity?.onBackPressedDispatcher?.onBackPressed()
                    }
                    return@setOnKeyListener true
                }
                return@setOnKeyListener false
            }

            if (savedInstanceState == null) {
                load()
            }
        }

        viewModel.logTermsScreen()
    }

    private fun load() = with(viewDataBinding) {
        retry.isVisible = false
        termsWebview.isInvisible = true
        viewDataBinding.termsWebview.loadUrl(createNewUserTermsUrl())
    }

    private fun onLoadingFailed() = with(viewDataBinding) {
        termsWebview.isInvisible = true
        retry.isVisible = true
    }

    private fun onLoadingFinished() = with(viewDataBinding) {
        termsWebview.isVisible = !retry.isVisible
    }

    override fun onDestroyView() {
        viewDataBinding.termsWebview.apply {
            webViewClient = WebViewClient()
            webChromeClient = WebChromeClient()
        }
        super.onDestroyView()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        viewDataBinding.termsWebview.saveState(outState)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        savedInstanceState?.let(viewDataBinding.termsWebview::restoreState)
    }

    private fun onTermsAccepted(code: String) {
        Timber.d("Terms accepted")

        viewModel.logTermsAccepted(code)
        if (args.launchSignInWithApple) {
            (activity as? SignInActivity)?.launchSignInWithApple()
        } else {
            // Return to where the user came from

            // If the user arrived here via the unverified phone number flow and inputted an email
            // address, pass requireEmail=true when returning to AskForFullNameFragment.
            val emailValid = viewModel.emailValid.value == true

            val action = when (viewModel.loginMethodValue) {
                LoginMethod.EMAIL -> TermsAndConditionsFragmentDirections
                    .actionContinueSignupWithEmail()

                LoginMethod.PHONE -> TermsAndConditionsFragmentDirections
                    .actionContinueSignupWithPhoneNumber(emailValid)

                LoginMethod.FACEBOOK -> TermsAndConditionsFragmentDirections
                    .actionContinueSignInWithFacebookAndGmail()

                LoginMethod.GMAIL -> TermsAndConditionsFragmentDirections
                    .actionContinueSignInWithFacebookAndGmail()

                LoginMethod.AUTOMATIC,
                LoginMethod.APPLE -> throw UnsupportedOperationException(
                    "Unsupported login option: ${viewModel.loginMethodValue}"
                )
            }
            findNavController().navigate(action)
        }
    }

    private fun createNewUserTermsUrl(): String =
        "${baseUrlV2}policyandterms/asknew?brand=${brand()}&callbackUrl=$CALLBACK_URL"

    private fun brand(): String = if (FlavorUtils.isSuuntoApp) {
        "SUUNTOAPP"
    } else {
        "SPORTSTRACKER"
    }

    private companion object {
        const val CALLBACK_URL = "suunto://action/agree-tos"
    }
}
