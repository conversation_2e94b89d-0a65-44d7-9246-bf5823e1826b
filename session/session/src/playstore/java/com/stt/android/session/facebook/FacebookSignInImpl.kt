package com.stt.android.session.facebook

import androidx.lifecycle.LiveData
import androidx.lifecycle.map
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.common.coroutines.LiveDataSuspendWithParam
import com.stt.android.common.coroutines.ViewModelScopeProvider
import com.stt.android.common.coroutines.paramLiveDataSuspend
import com.stt.android.domain.UserSession
import com.stt.android.domain.session.FacebookSignInParam
import com.stt.android.domain.session.FacebookSignInUseCase
import com.stt.android.domain.session.LoginMethod
import com.stt.android.domain.session.SessionInitType
import com.stt.android.domain.session.facebook.FacebookSignInResult
import com.stt.android.session.SessionInitializer
import com.stt.android.session.logFacebookUserDataAnalytics
import com.stt.android.usecases.startup.UserSettingsTracker
import kotlinx.coroutines.CoroutineScope
import javax.inject.Inject

class FacebookSignInImpl
@Inject constructor(
    val facebookSignInUseCase: FacebookSignInUseCase,
    private val sessionInitializer: SessionInitializer,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val userSettingsTracker: UserSettingsTracker,
    override val viewModelScope: CoroutineScope
) : FacebookSignIn, ViewModelScopeProvider {

    private val facebookSignInWrapper: LiveDataSuspendWithParam<FacebookSignIn.FlowResult, FacebookSignInParam> =
        paramLiveDataSuspend { param ->
            when (val result: FacebookSignInResult = facebookSignInUseCase(param.token, param.termscode)) {
                is FacebookSignInResult.Success -> {
                    // we also initialise session before notifying UI
                    val sessionInitResult = sessionInitializer.initialiseSession(
                        UserSession.fromDomainSession(result.userSession),
                        LoginMethod.FACEBOOK,
                        SessionInitType.LOGIN
                    )
                    FacebookSignIn.FlowResult.Success(sessionInitResult)
                }
                is FacebookSignInResult.SignupNeeded -> {
                    // log some info to analytics
                    logFacebookUserDataAnalytics(
                        result.newUserCredentials,
                        emarsysAnalytics,
                        amplitudeAnalyticsTracker,
                        userSettingsTracker
                    )
                    FacebookSignIn.FlowResult.SignupNeeded(result.newUserCredentials)
                }
                else -> {
                    throw IllegalArgumentException("Invalid FacebookSignInResult")
                }
            }
        }

    override val facebookSignInWrapperState: LiveData<LiveDataSuspendState<FacebookSignIn.FlowResult>> =
        facebookSignInWrapper.state

    override val facebookSignInInProgress: LiveData<Boolean> =
        facebookSignInWrapperState.map {
            it.isInProgress || it.isSuccess
        }

    override fun signInWithFacebook(token: String, termscode: String) {
        facebookSignInWrapper.run(FacebookSignInParam(token, termscode))
    }
}
