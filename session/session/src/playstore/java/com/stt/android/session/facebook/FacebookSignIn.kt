package com.stt.android.session.facebook

import androidx.lifecycle.LiveData
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.domain.session.facebook.NewUserCredentials
import com.stt.android.session.SessionInitializerResult

interface FacebookSignIn {
    sealed class FlowResult {
        data class Success(val sessionInitializerResult: SessionInitializerResult) : FlowResult()
        data class SignupNeeded(val newUserCredentials: NewUserCredentials) : FlowResult()
    }

    fun signInWithFacebook(token: String, termscode: String)
    val facebookSignInWrapperState: LiveData<LiveDataSuspendState<FlowResult>>
    val facebookSignInInProgress: LiveData<Boolean>
}
