package com.stt.android.session.splashintro

import android.app.Activity
import android.os.Bundle
import android.view.View
import androidx.activity.compose.BackHandler
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.stt.android.R
import com.stt.android.base.BaseContentBody
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.COUNT_DOWN_SECONDS
import com.stt.android.compose.widgets.ComposeCountDownTimer
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.compose.widgets.TextFieldInputWithError
import com.stt.android.newemail.NewEmailActivity
import com.stt.android.newemail.VERIFICATION_CODE_LENGTH
import com.stt.android.session.signin.SignInOnboardingViewModel
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import com.stt.android.R as BaseR

@AndroidEntryPoint
class ContinueWithEmailCheckFragment : BaseFragment() {
    private val continueWithEmailCheckFragmentArgs by navArgs<ContinueWithEmailCheckFragmentArgs>()
    private val viewModel: ContinueWithEmailCheckViewModel by activityViewModels()

    private val sharedViewModel by activityViewModels<SignInOnboardingViewModel>()

    private val finishWithResultOkAfterChangeEmailListener =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            val email = result.data?.getStringExtra(NewEmailActivity.EXTRA_EMAIL_KEY)
            if (result.resultCode == Activity.RESULT_OK && !email.isNullOrEmpty()) {
                sharedViewModel.updateEmailInput(email)
                findNavController().popBackStack()
            }
        }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // ContinueWithEmailCheckViewModel cannot access fragment args as its lifecycle is tied
        // to the activity and not the fragment to prevent unnecessary calls to sendEmailVerificationCode.
        // Before, its lifecycle was tied to this fragment and navigating back and forth from the
        // email verification view triggered a new call to the sendEmailVerificationCode API.
        viewModel.initialize(continueWithEmailCheckFragmentArgs.email)
    }

    @Composable
    override fun SetContentView() {
        ContinueWithEmailCheckScreen()
    }

    @Composable
    private fun ContinueWithEmailCheckScreen() {
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        BackHandler {
            backHandle()
        }
        LaunchedEffect(uiState.emailVerifyToken) {
            uiState.emailVerifyToken?.let {
                Timber.d("token: $it")
                sharedViewModel.setVerificationToken(it)
                sharedViewModel.storeEmailChecked(true)
                findNavController().popBackStack()
            }
        }
        BaseContentBody(
            currentFragment = this@ContinueWithEmailCheckFragment,
            viewModel = viewModel,
            onBackClicked = {
                backHandle()
            }
        ) {
            ContentBody(
                email = continueWithEmailCheckFragmentArgs.email,
                isLoading = commonUIState.isLoading,
                verifyCode = uiState.verifyCode,
                verifyCodeIsExpired = uiState.verifyCodeIsExpired,
                onInputVerifyCode = {
                    viewModel.inputVerifyCode(it)
                },
                onVerificationCodeClicked = { email, verifyCode ->
                    viewModel.checkVerificationCode(
                        email,
                        verifyCode,
                        sendLoginEmail = continueWithEmailCheckFragmentArgs.showChangeEmail
                    )
                },
                onResendClicked = { email ->
                    viewModel.sendEmailVerificationCode(email)
                },
                onChangeEmailClicked = {
                    finishWithResultOkAfterChangeEmailListener.launch(NewEmailActivity.newStartIntent(requireContext()))
                },
                showChangeEmail = continueWithEmailCheckFragmentArgs.showChangeEmail,
            )
        }
    }

    private fun backHandle() {
        sharedViewModel.setReLogin(true)
        viewModel.logoutFromEmailCheck()
        findNavController().popBackStack()
    }

    override fun onDestroy() {
        finishWithResultOkAfterChangeEmailListener.unregister()
        super.onDestroy()
    }
}

@Preview
@Composable
private fun ContinueWithEmailCheckScreenPreview() {
    AppTheme {
        Surface {
            ContentBody(
                isLoading = false,
                verifyCode = "123456",
                verifyCodeIsExpired = false,
                email = "<EMAIL>",
                onInputVerifyCode = {},
                onVerificationCodeClicked = { _, _ -> },
                onResendClicked = {},
                onChangeEmailClicked = {}
            )
        }
    }
}

@Composable
private fun ContentBody(
    isLoading: Boolean,
    email: String,
    verifyCode: String,
    verifyCodeIsExpired: Boolean,
    onVerificationCodeClicked: (String, String) -> Unit,
    onInputVerifyCode: (String) -> Unit,
    onResendClicked: (String) -> Unit,
    onChangeEmailClicked: () -> Unit,
    modifier: Modifier = Modifier,
    showChangeEmail: Boolean = false,
) {
    var timeLeft by rememberSaveable { mutableIntStateOf(COUNT_DOWN_SECONDS) }
    var resendEnable by rememberSaveable { mutableStateOf(false) }
    if (!resendEnable) {
        ComposeCountDownTimer(countDownSeconds = timeLeft, onTick = {
            timeLeft = it
        }, onDone = {
            timeLeft = 0
            resendEnable = true
        })
    }
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top,
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        Spacer(modifier = Modifier.height(height = MaterialTheme.spacing.xlarge))

        Text(
            text = stringResource(id = BaseR.string.enter_the_code_sent_by_email),
            style = MaterialTheme.typography.bodyLargeBold,
            textAlign = TextAlign.Center,
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    top = MaterialTheme.spacing.medium,
                    bottom = MaterialTheme.spacing.large,
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium
                ),
            horizontalArrangement = Arrangement.Center,
        ) {
            val checkVerificationCodeSummary =
                stringResource(id = com.stt.android.R.string.check_verification_code_summary_from_email, email)
            val startIndex = checkVerificationCodeSummary.indexOf(email)
            val text = buildAnnotatedString {
                append(checkVerificationCodeSummary)
                addStyle(
                    style = SpanStyle(fontWeight = FontWeight.Bold),
                    startIndex,
                    startIndex + email.length
                )
            }
            Text(
                text = text,
                style = MaterialTheme.typography.body,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colors.nearBlack,
            )
        }

        val errorMessage = when {
            verifyCodeIsExpired -> stringResource(id = com.stt.android.R.string.verification_code_has_expired)
            else -> ""
        }
        TextFieldInputWithError(
            currentText = verifyCode,
            placeholderText = stringResource(id = com.stt.android.R.string.verification_code),
            onChanged = {
                if (it.length <= VERIFICATION_CODE_LENGTH) {
                    onInputVerifyCode.invoke(it)
                }
            },
            onActionDone = {
                if (verifyCode.isNotEmpty() && verifyCode.length == VERIFICATION_CODE_LENGTH) {
                    onVerificationCodeClicked.invoke(email, verifyCode)
                }
            },
            errorMessage = errorMessage,
            keyboardType = KeyboardType.Number,
            modifier = Modifier.padding(
                start = MaterialTheme.spacing.medium,
                end = MaterialTheme.spacing.medium
            ),
            enablePlaceholderTextFloating = false
        )

        PrimaryButton(
            enabled = verifyCode.isNotEmpty() && verifyCode.length == VERIFICATION_CODE_LENGTH,
            onClick = {
                onVerificationCodeClicked.invoke(email, verifyCode)
            },
            text = stringResource(R.string.verify_str).uppercase(),
            buttonHeight = dimensionResource(R.dimen.height_button),
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    top = MaterialTheme.spacing.xlarge,
                    end = MaterialTheme.spacing.medium,
                )
        )

        if (showChangeEmail) {
            PrimaryButton(
                onClick = {
                    onChangeEmailClicked.invoke()
                },
                backgroundColor = Color.White,
                text = stringResource(id = R.string.change_email).uppercase(),
                buttonHeight = dimensionResource(R.dimen.height_button),
                textColor = MaterialTheme.colors.darkGrey,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = MaterialTheme.spacing.medium,
                        top = MaterialTheme.spacing.small,
                        end = MaterialTheme.spacing.medium,
                    )
            )
        }

        Text(
            text = "${stringResource(
                id = R.string.did_not_receive_code
            )}\n${stringResource(id = R.string.checking_spam_folder)}",
            style = MaterialTheme.typography.body,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colors.nearBlack,
            modifier = Modifier.padding(
                top = MaterialTheme.spacing.large,
            )
        )

        TextButton(
            enabled = resendEnable,
            onClick = {
                resendEnable = false
                timeLeft = COUNT_DOWN_SECONDS
                onResendClicked.invoke(email)
            },
            modifier = Modifier
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.xlarge
                )
        ) {
            Text(
                text = stringResource(
                    id = R.string.resend_verification_code,
                    if (timeLeft <= 0) "" else "($timeLeft)"
                ),
                style = MaterialTheme.typography.bodyBold,
                textAlign = TextAlign.Center,
                color = if (resendEnable) MaterialTheme.colors.primary else MaterialTheme.colors.cloudyGrey,
            )
        }
    }
    LoadingContent(isLoading)
}
