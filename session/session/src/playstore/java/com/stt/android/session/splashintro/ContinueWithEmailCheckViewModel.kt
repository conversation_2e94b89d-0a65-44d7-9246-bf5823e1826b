package com.stt.android.session.splashintro

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.newemail.EmailCheckRepository
import com.stt.android.newemail.NewEmailCheckViewModel
import com.stt.android.ui.tasks.LogoutTask
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class ContinueWithEmailCheckViewModel @Inject constructor(
    emailCheckRepository: EmailCheckRepository,
    dispatchers: CoroutinesDispatchers,
    userSettingsController: UserSettingsController,
    private val logoutTask: LogoutTask
) : NewEmailCheckViewModel(emailCheckRepository, dispatchers, userSettingsController) {

    @OptIn(DelicateCoroutinesApi::class)
    fun logoutFromEmailCheck() {
        GlobalScope.launch(Dispatchers.IO) {
            logoutTask.logout().subscribe(
                { Timber.d("logging out success") },
                { Timber.w(it, "logging out failed") }
            )
        }
    }
}
