package com.stt.android.session.signup.email

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.domain.session.LoginMethod
import com.stt.android.extensions.hideKeyboard
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.session.InputError
import com.stt.android.session.R
import com.stt.android.session.SessionInitializerResult
import com.stt.android.session.signin.SignInOnboardingViewModel
import com.stt.android.session.SignInUserData
import com.stt.android.session.databinding.FragmentSignupWithEmailBinding
import com.stt.android.session.phonenumberverification.PhoneRegionDialogFragment
import com.stt.android.session.setInputError
import com.stt.android.session.setupLoginFlowTransitions
import com.stt.android.session.showSnackBar
import com.stt.android.session.signin.SignInActivity
import com.stt.android.session.signup.SignUp
import com.stt.android.utils.OnActionDone
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR

@AndroidEntryPoint
class SignUpWithEmailFragment : ViewModelFragment2() {

    @Inject
    lateinit var homeActivityNavigator: HomeActivityNavigator

    override val viewModel: SignInOnboardingViewModel by activityViewModels()

    private val viewDataBinding: FragmentSignupWithEmailBinding get() = requireBinding()

    private val args: SignUpWithEmailFragmentArgs by navArgs()

    override fun getLayoutResId() = R.layout.fragment_signup_with_email

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupLoginFlowTransitions()
        setupUi()

        if (viewModel.termsAccepted.value == true) {
            viewModel.termsAccepted.value = false
            signUp()
        }
    }

    private fun setupUi() {
        // todo handle incoming httpexception in arguments

        args.email?.let { viewModel.rawEmailOrUsername.value = it }

        with(viewDataBinding) {
            signupWithEmailPasswordInput.passwordInputLayout.helperText =
                getString(BaseR.string.password_requirements)

            onSignUpClicked = View.OnClickListener {
                activity?.hideKeyboard()
                handleSignUpClick()
            }

            onActionDone = OnActionDone { _, _ ->
                handleSignUpClick()
                false
            }
        }

        // Show error state if minimum password length is not met. Show helper text if
        // minimum length is met, but the complexity requirement is not.
        viewModel.signUpPasswordInputState.observeNotNull(viewLifecycleOwner) {
            val inputLayout = viewDataBinding.signupWithEmailPasswordInput.passwordInputLayout
            when (it) {
                SignInUserData.SignUpPasswordState.GOOD_PASSWORD -> {
                    inputLayout.isErrorEnabled = false
                    inputLayout.isHelperTextEnabled = false
                }
                SignInUserData.SignUpPasswordState.WEAK_PASSWORD -> {
                    inputLayout.isErrorEnabled = false
                    inputLayout.isHelperTextEnabled = true
                    inputLayout.helperText = getString(BaseR.string.password_requirements)
                }
                SignInUserData.SignUpPasswordState.BAD_PASSWORD -> {
                    inputLayout.isHelperTextEnabled = false
                    setInputError(inputLayout, InputError(BaseR.string.password_requirements))
                }
                SignInUserData.SignUpPasswordState.REQUIRED -> {
                    inputLayout.isHelperTextEnabled = false
                    setInputError(inputLayout, InputError(R.string.sign_up_password_is_required_error))
                }
                SignInUserData.SignUpPasswordState.EMPTY -> {
                    inputLayout.isErrorEnabled = false
                    inputLayout.isHelperTextEnabled = true
                }
            }
        }

        viewModel.onPhoneRegionClicked.observeK(viewLifecycleOwner) { code ->
            if (isAdded) {
                val dialogFragment = PhoneRegionDialogFragment().apply {
                    val action =
                        SignUpWithEmailFragmentDirections.actionSignupFragmentToPhoneRegionDialogFragment(
                            code ?: -1
                        )
                    arguments = action.arguments
                }
                dialogFragment.onItemClickListener =
                    { region -> viewModel.phoneRegion.value = "+$region" }
                dialogFragment.show(parentFragmentManager, "PhoneNumberConfirmationFragment")
            }
        }

        viewModel.resetSignUpPasswordError()
        viewModel.signUpState.observeNotNull(viewLifecycleOwner, ::handleSignUpState)
        viewModel.emailChecked.observeNotNull(viewLifecycleOwner) {
            if (it && viewModel.checkInputForSignUp()) {
                showTerms()
                viewModel.setEmailChecked(false)
            }
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        (activity as? AppCompatActivity)?.setSupportActionBar(
            viewDataBinding.signupWithEmailToolbar.signupToolbar
        )
    }

    private fun handleSignUpClick() {
        if (viewModel.checkInputForSignUp()) {
            viewModel.setEmailChecked(false)
            findNavController().navigate(
                SignUpWithEmailFragmentDirections
                    .actionEmailCheck(viewDataBinding.signupWithEmailEmailInput.emailEditText.text.toString().trim(), false)
            )
        }
    }

    private fun showTerms() {
        activity?.hideKeyboard()
        findNavController().navigate(SignUpWithEmailFragmentDirections.actionShowTermsAndConditions())
    }

    private fun signUp() {
        viewModel.signUp(
            SignUp.FlowParams(
                LoginMethod.EMAIL,
                viewModel.emailExistsInMovescount.value
            )
        )
    }

    private fun handleSignUpState(state: LiveDataSuspendState<SessionInitializerResult>) {
        state.ifNotHandled {
            if (state is LiveDataSuspendState.Success) {
                (activity as? SignInActivity)?.handleSessionInitSuccess(state.successValue)
            } else if (state is LiveDataSuspendState.Failure) {
                Timber.w(state.throwable, "Sign up failed")
                view?.showSnackBar(state.throwable)
            }
        }
    }
}
