package com.stt.android.session.base

import androidx.annotation.StringRes
import com.stt.android.R

sealed class SendVerificationError(@StringRes val resId: Int) {
    data object PhoneNumberIsError : SendVerificationError(R.string.phone_error)
    data object PhoneNumberNotRegistered : SendVerificationError(R.string.phone_number_not_registered)
    data object EmailIsError : SendVerificationError(R.string.invalid_email)
    data object TooManyRequests : SendVerificationError(R.string.error_429)
}
