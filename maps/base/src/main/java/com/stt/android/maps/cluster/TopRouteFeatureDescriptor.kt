package com.stt.android.maps.cluster

import android.graphics.Bitmap
import androidx.annotation.ColorInt
import com.google.android.gms.maps.model.LatLng

data class TopRouteFeatureDescriptor(
    val points: List<TopRoutePoint>,
    val clusterMarkerBitmap: Bitmap,
    val largeClusterMarkerBitmap: Bitmap,
    val normalMarkerBitmapMap: Map<String, Bitmap>,
    val options: ClusterMakerOptions?,
)

data class TopRoutePoint(
    val markerType: String,
    val latLng: LatLng
)

data class ClusterMakerOptions(
    val showText: Boolean,
    val textSize: Float?,
    @ColorInt val textColor: Int?,
    @ColorInt val textHaloColor: Int? = null,
    val textHaloWidth: Float? = null,
    val textOffset: List<Double>? = null,
)
