package com.suunto.headset.repository

import com.stt.android.data.necks.NeckAssessmentRemoteDatasource
import com.stt.android.data.necks.NeckLocalAssessmentValues
import javax.inject.Inject

class NeckAssessmentRepository @Inject constructor(private val neckAssessmentRemoteDatasource: NeckAssessmentRemoteDatasource) {

    suspend fun saveNeckAssessment(neckLocalAssessmentValues: NeckLocalAssessmentValues): NeckLocalAssessmentValues {
        return neckAssessmentRemoteDatasource.saveNeckAssessment(neckLocalAssessmentValues)
    }

    suspend fun getNecksAssessmentValues(): List<NeckLocalAssessmentValues> {
        return neckAssessmentRemoteDatasource.getNecksAssessmentValues()
    }

    suspend fun deleteNecks(id: String): Boolean {
        return neckAssessmentRemoteDatasource.deleteNeckAssessment(id)
    }
}
