package com.suunto.headset.model

import com.stt.android.data.necks.NeckLocalAssessmentValues
import com.stt.android.utils.toEpochMilli
import java.time.LocalDateTime

data class AssessmentValues(
    val leftRotationDegree: Int = 0,
    val rightRotationDegree: Int = 0,
    val leftLateralFlexionDegree: Int = 0,
    val rightLateralFlexionDegree: Int = 0,
    val flexionDegree: Int = 0,
    val extensionDegree: Int = 0
)

fun AssessmentValues.toLocalValue(): NeckLocalAssessmentValues {
    return NeckLocalAssessmentValues(
        leftRotation = leftRotationDegree,
        rightRotation = rightRotationDegree,
        flexion = flexionDegree,
        extension = extensionDegree,
        leftExtension = leftLateralFlexionDegree,
        rightExtension = rightLateralFlexionDegree,
        createdDate = LocalDateTime.now().toEpochMilli()
    )
}
