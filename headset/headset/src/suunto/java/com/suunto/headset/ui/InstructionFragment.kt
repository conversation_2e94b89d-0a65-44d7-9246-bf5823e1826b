package com.suunto.headset.ui

import android.os.Bundle
import android.view.View
import android.widget.ImageView
import androidx.core.view.size
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import by.kirich1409.viewbindingdelegate.viewBinding
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.launchOnLifecycle
import com.stt.android.ui.utils.PagerBulletStripUtility
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.suunto.extension.popBackStack
import com.suunto.headset.R
import com.suunto.headset.capability.BaseHeadsetFeature
import com.suunto.headset.capability.SupportedHeadsetDevices
import com.suunto.headset.databinding.FragmentInstuctionBinding
import com.suunto.headset.ui.onboarding.HeadsetOnBoardingPage
import com.suunto.headset.ui.onboarding.HeadsetOnBoardingPageFragment
import com.suunto.headset.ui.onboarding.HeadsetOnBoardingPagerAdapter
import com.suunto.headset.viewmodel.InstructionViewModel
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

/**
 *  The headset introduction UI when using it for the first time.
 */
@AndroidEntryPoint
class InstructionFragment : Fragment(R.layout.fragment_instuction) {

    private val binding by viewBinding(FragmentInstuctionBinding::bind)

    private val viewModel by viewModels<InstructionViewModel>()

    protected lateinit var adapter: HeadsetOnBoardingPagerAdapter

    @Inject
    lateinit var baseHeadsetFeature: BaseHeadsetFeature

    @Inject
    lateinit var supportedHeadsetDevices: SupportedHeadsetDevices

    @Inject
    lateinit var currentUserController: CurrentUserController

    private var currentSelectedPageImageResId = 0

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setViewPager()

        binding.onboardingCloseButton.visibility =
            if (showCloseButtonByIndex(0)) View.GONE else View.VISIBLE
        binding.onboardingCloseButton.setOnClickListenerThrottled {
            popBackStack()
        }

        binding.forwardArrow.setOnClickListenerThrottled {
            binding.viewPager.currentItem++
        }

        binding.backArrow.setOnClickListenerThrottled {
            binding.viewPager.currentItem--
        }
    }

    private fun updateNeckReminderSwitchState(fragment: HeadsetOnBoardingPageFragment) {
        fragment.viewLifecycleOwner.launchOnLifecycle(Lifecycle.State.STARTED) {
            viewModel.neckReminderEnabled.collect {
                fragment.setSwitchEnabled(it)
            }
        }
    }

    private fun setViewPager() {
        adapter = HeadsetOnBoardingPagerAdapter(
            childFragmentManager, getOnboardingPages(),
            object :
                HeadsetOnBoardingPageFragment.Listener {
                override fun onBoardingPageCreated(
                    page: HeadsetOnBoardingPage,
                    fragment: HeadsetOnBoardingPageFragment
                ) {
                    Timber.d("onBoardingPageCreated")
                    if (page.switchTitleResId == R.string.neck_movement_monitoring) {
                        updateNeckReminderSwitchState(fragment)
                    }
                }

                override fun onBoardingPrimaryButtonClicked(page: HeadsetOnBoardingPage) {
                    Timber.d("onBoardingPrimaryButtonClicked")
                    when {
                        binding.viewPager.currentItem < adapter.count - 1 -> {
                            binding.viewPager.currentItem++
                        }

                        else -> {
                            popBackStack()
                        }
                    }
                }

                override fun onBoardingSwitchButtonClicked(
                    page: HeadsetOnBoardingPage,
                    checked: Boolean
                ) {
                    if (supportedHeadsetDevices.supportNeckMovementAssessment(baseHeadsetFeature)) {
                        viewModel.setNeckReminderInterval(checked)
                    }
                }
            }
        )
        binding.viewPager.adapter = adapter
        val bullets = PagerBulletStripUtility.updateBulletStrip(
            adapter.count,
            binding.bulletStrip,
            binding.viewPager,
            R.layout.headset_dashboard_page_bullet
        )
        binding.viewPager.addOnPageChangeListener(OnBoardingPageChangeListener(bullets))
        if (adapter.pages.isNotEmpty()) {
            currentSelectedPageImageResId = adapter.pages.first().onboardingPhotoResId
            binding.forwardArrow.visibility = View.VISIBLE
        }
    }

    private fun getOnboardingPages(): List<HeadsetOnBoardingPage> {
        return supportedHeadsetDevices.getFunctionIntroduction(baseHeadsetFeature).map {
            it.copy(userName = currentUserController.realNameOrUsername)
        }
    }

    internal fun isLastPage(position: Int) = position == adapter.count - 1

    internal fun isFirstPage(position: Int) = position == 0

    internal fun showCloseButtonByIndex(position: Int): Boolean {
        return isLastPage(position)
    }

    private inner class OnBoardingPageChangeListener(bullets: Array<ImageView>) :
        PagerBulletStripUtility.BulletPageChangeListener(bullets) {

        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            with(binding) {
                backArrow.visibility =
                    if (isFirstPage(position)) View.GONE else View.VISIBLE
                forwardArrow.visibility =
                    if (isLastPage(position)) View.GONE else View.VISIBLE
            }
            val expectedPhotoResId =
                adapter.pages[binding.viewPager.currentItem].onboardingPhotoResId
            if (currentSelectedPageImageResId != expectedPhotoResId) {
                adapter.showBoardingImageWithAnimation(position, currentSelectedPageImageResId)
                currentSelectedPageImageResId = expectedPhotoResId
            }
        }
    }
}
