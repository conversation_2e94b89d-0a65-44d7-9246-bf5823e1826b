<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?suuntoItemBackgroundColor">

    <TextView
        android:id="@+id/tv_dual_device_label"
        style="@style/Body.Larger"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:padding="@dimen/size_spacing_medium"
        android:text="@string/dual_device_connection_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_dual_device_summary"
        style="@style/Body.Larger"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/size_spacing_medium"
        android:paddingEnd="@dimen/size_spacing_medium"
        android:paddingBottom="@dimen/size_spacing_medium"
        android:text="@string/dual_device_connection_summary"
        android:textColor="@color/dark_gray"
        app:layout_constraintStart_toStartOf="@id/tv_dual_device_label"
        app:layout_constraintTop_toBottomOf="@id/tv_dual_device_label" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/sc_device_dual_connect"
        style="@style/RadioCheckSwitchStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="16dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_dual_device_label"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_dual_device_label"
        tools:visibility="gone" />

    <View
        android:id="@+id/divider1"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        android:background="?suuntoDividerColor"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_dual_device_summary" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_connect_guide"
        style="@style/Body.Medium"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawableEnd="@drawable/chevron_right"
        android:padding="@dimen/padding"
        android:text="@string/dual_device_connect_guide"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider1" />

    <View
        android:id="@+id/divider2"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        android:background="?suuntoDividerColor"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_connect_guide" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_connected_device"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/light_grey"
        android:paddingHorizontal="@dimen/size_spacing_medium"
        android:paddingVertical="@dimen/size_spacing_smaller"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_connect_guide">

        <TextView
            android:id="@+id/tv_connected_device_label"
            style="@style/Body.Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/padding"
            android:gravity="center_vertical"
            android:text="@string/connected_device_label"
            android:textColor="@color/near_black"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_connect_device_count"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_connect_device_count"
            style="@style/Body.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/near_black"
            app:layout_constraintBottom_toBottomOf="@id/tv_connected_device_label"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_connected_device_label"
            tools:text="1/2" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_devices"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_connected_device" />

</androidx.constraintlayout.widget.ConstraintLayout>
