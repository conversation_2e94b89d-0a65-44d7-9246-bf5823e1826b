package com.stt.android.home.explore.toproutes.filter

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.rememberNestedScrollInteropConnection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.R
import com.stt.android.home.explore.toproutes.TopRoutesSharedViewModel
import com.stt.android.home.explore.toproutes.carousel.RouteFeature
import com.stt.android.mapping.InfoModelFormatter

@Composable
fun TopRoutesFilterBottomScreen(
    viewModel: TopRoutesFilterViewModel,
    sharedViewModel: TopRoutesSharedViewModel,
    measurementUnit: MeasurementUnit,
    infoModelFormatter: InfoModelFormatter,
    onItemClick: (RouteFeature) -> Unit,
    modifier: Modifier = Modifier,
) {
    var isFilterShown by rememberSaveable { mutableStateOf(false) }
    var filterChanged by rememberSaveable { mutableStateOf(false) }
    var latestTopRouteFilter by remember { mutableStateOf(TopRouteFilter()) }
    val sortFilterList = remember { listOf(SortFilter.MostPopular, SortFilter.Nearest) }
    val visibleFeatures by sharedViewModel.visibleFeatures.collectAsState()

    var filterTopRoutes by remember { mutableStateOf(visibleFeatures) }

    LaunchedEffect(visibleFeatures, latestTopRouteFilter) {
        val activityTypes =
            latestTopRouteFilter.activityTypes ?: viewModel.getDefaultTopRoutesActivityTypeList()
        filterTopRoutes = filterAndSortRoutes(
            activityTypes,
            measurementUnit,
            visibleFeatures,
            latestTopRouteFilter
        )
    }

    val nestedScrollConnection = rememberNestedScrollInteropConnection()

    Column(
        modifier = modifier
            .fillMaxWidth()
            .nestedScroll(nestedScrollConnection)
    ) {
        PopularRouteFilter(
            isOpen = isFilterShown,
            showFilterIcon = visibleFeatures.isNotEmpty(),
            showResetIcon = filterChanged,
            onReset = {
                filterChanged = false
                latestTopRouteFilter = TopRouteFilter()
            },
            onClick = {
                isFilterShown = !isFilterShown
            }
        )

        HorizontalDivider(color = MaterialTheme.colorScheme.lightGrey)

        if (isFilterShown) {
            TopRoutesFilterScreen(
                sortFilterList = sortFilterList,
                allTypes = viewModel.getDefaultTopRoutesActivityTypeList(),
                topRouteFilter = latestTopRouteFilter,
                measurementUnit = measurementUnit,
                onFilterChanged = {
                    filterChanged = true
                    latestTopRouteFilter = it
                },
            )
            HorizontalDivider(color = MaterialTheme.colorScheme.lightGrey)
        }

        Box(modifier = Modifier.weight(1f)) {
            key(filterTopRoutes, isFilterShown) {
                if (filterTopRoutes.isEmpty()) {
                    FilterTopRoutesEmptyScreen()
                } else {
                    val loadedRoutes by viewModel.loadedRoutes.collectAsState()
                    FilterTopRoutesListScreen(
                        isScrollable = !isFilterShown,
                        loadedRoutes = loadedRoutes,
                        routeFeatures = filterTopRoutes,
                        infoModelFormatter = infoModelFormatter,
                        onItemClick = onItemClick,
                        onLoadRoute = { feature ->
                            viewModel.loadRouteIfNeeded(feature)
                        }
                    )
                }
            }
        }
    }
}

fun filterAndSortRoutes(
    activityTypes: List<ActivityType>,
    measurementUnit: MeasurementUnit,
    routes: List<RouteFeature>,
    filter: TopRouteFilter
): List<RouteFeature> {
    val distanceMinInMeter = filter.distanceFilter.getLeftInMeter(measurementUnit)
    val distanceMaxInMeter = filter.distanceFilter.getRightInMeter(measurementUnit)

    val filtered = routes.filter {
        it.activityType in activityTypes &&
            it.distance in distanceMinInMeter..distanceMaxInMeter
    }

    return when (filter.sortFilter) {
        SortFilter.MostPopular -> filtered.sortedByDescending { it.popularity }
        SortFilter.Nearest -> filtered.sortedBy { it.awayYouDistance }
        else -> filtered
    }
}

@Composable
fun PopularRouteFilter(
    isOpen: Boolean,
    showFilterIcon: Boolean,
    showResetIcon: Boolean,
    onReset: () -> Unit,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium)
    ) {
        Text(
            text = stringResource(id = R.string.top_route_popular_routes),
            style = MaterialTheme.typography.bodyXLargeBold,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.weight(1f)
        )

        if (showResetIcon) {
            ResetButton(
                onClick = {
                    onReset.invoke()
                }
            )

            Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
        }

        if (showFilterIcon) {
            Icon(
                painter = if (isOpen) {
                    painterResource(R.drawable.ic_filter_collapse)
                } else {
                    painterResource(R.drawable.ic_filter_expand)
                },
                contentDescription = null,
                tint = MaterialTheme.colorScheme.nearBlack,
                modifier = Modifier
                    .size(MaterialTheme.iconSizes.small)
                    .clickableThrottleFirst {
                        onClick.invoke()
                    }
            )
        }
    }
}

@Composable
private fun FilterTopRoutesEmptyScreen(
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(
                start = MaterialTheme.spacing.medium,
                end = MaterialTheme.spacing.medium,
                top = MaterialTheme.spacing.medium,
                bottom = MaterialTheme.spacing.medium.plus(MaterialTheme.spacing.xxxxlarge),
            )
    ) {
        Text(
            text = stringResource(R.string.top_route_filter_empty_content),
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.align(Alignment.CenterHorizontally)
        )
    }
}

@Preview
@Composable
private fun ResetButtonPreview() {
    ResetButton(
        onClick = {}
    )
}
